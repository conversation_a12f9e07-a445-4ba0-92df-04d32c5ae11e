<?php
/**
 * Compare Services Data Script
 * 
 * This script compares the old mapping data with the new enhanced services data
 * Run this AFTER importing production data and running the migration
 */

require_once '../../../wp-config.php';
require_once '../../../wp-load.php';

global $wpdb;

echo "=== KIVICARE SERVICES DATA COMPARISON ===" . PHP_EOL;
echo "Timestamp: " . current_time('Y-m-d H:i:s') . PHP_EOL;
echo "=========================================" . PHP_EOL . PHP_EOL;

// Create comparison directory if it doesn't exist
$comparison_dir = 'data_comparisons';
if (!file_exists($comparison_dir)) {
    mkdir($comparison_dir, 0755, true);
}

$timestamp = date('Y-m-d_H-i-s');
$comparison_file = $comparison_dir . '/comparison_' . $timestamp . '.txt';

$output = "=== SERVICES DATA COMPARISON REPORT ===" . PHP_EOL;
$output .= "Generated: " . current_time('Y-m-d H:i:s') . PHP_EOL;
$output .= "=======================================" . PHP_EOL . PHP_EOL;

// Check if old mapping table exists
$old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';
$current_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
$services_table = $wpdb->prefix . 'kc_services';

$mapping_table_to_compare = null;
if ($wpdb->get_var("SHOW TABLES LIKE '{$old_mapping_table}'")) {
    $mapping_table_to_compare = $old_mapping_table;
    $output .= "Using old mapping table: {$old_mapping_table}" . PHP_EOL;
} elseif ($wpdb->get_var("SHOW TABLES LIKE '{$current_mapping_table}'")) {
    $mapping_table_to_compare = $current_mapping_table;
    $output .= "Using current mapping table: {$current_mapping_table}" . PHP_EOL;
} else {
    $output .= "ERROR: No mapping table found for comparison!" . PHP_EOL;
    file_put_contents($comparison_file, $output);
    echo $output;
    exit;
}

$output .= "Comparing with enhanced services table: {$services_table}" . PHP_EOL . PHP_EOL;

// 1. Get statistics
echo "1. Gathering statistics..." . PHP_EOL;
$stats = [];

// Old mapping data
$stats['old_total_mappings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$mapping_table_to_compare}");
$stats['old_active_mappings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$mapping_table_to_compare} WHERE status = 1");
$stats['old_unique_services'] = $wpdb->get_var("SELECT COUNT(DISTINCT service_id) FROM {$mapping_table_to_compare}");

// New enhanced services data
$stats['new_total_services'] = $wpdb->get_var("SELECT COUNT(*) FROM {$services_table}");
$stats['new_services_with_doctors'] = $wpdb->get_var("SELECT COUNT(*) FROM {$services_table} WHERE doctor_id > 0");
$stats['new_active_services_with_doctors'] = $wpdb->get_var("SELECT COUNT(*) FROM {$services_table} WHERE doctor_id > 0 AND status = 1");
$stats['new_base_services'] = $wpdb->get_var("SELECT COUNT(*) FROM {$services_table} WHERE doctor_id = 0");

$output .= "=== STATISTICS COMPARISON ===" . PHP_EOL;
$output .= "Old Mapping Table:" . PHP_EOL;
$output .= "  Total mappings: {$stats['old_total_mappings']}" . PHP_EOL;
$output .= "  Active mappings: {$stats['old_active_mappings']}" . PHP_EOL;
$output .= "  Unique services: {$stats['old_unique_services']}" . PHP_EOL . PHP_EOL;

$output .= "New Enhanced Services Table:" . PHP_EOL;
$output .= "  Total services: {$stats['new_total_services']}" . PHP_EOL;
$output .= "  Services with doctors: {$stats['new_services_with_doctors']}" . PHP_EOL;
$output .= "  Active services with doctors: {$stats['new_active_services_with_doctors']}" . PHP_EOL;
$output .= "  Base services (doctor_id=0): {$stats['new_base_services']}" . PHP_EOL . PHP_EOL;

// 2. Data integrity check
echo "2. Performing data integrity check..." . PHP_EOL;
$output .= "=== DATA INTEGRITY CHECK ===" . PHP_EOL;

// Check if migration preserved all active mappings
$missing_mappings = $wpdb->get_results("
    SELECT m.*, s.name as service_name
    FROM {$mapping_table_to_compare} m
    LEFT JOIN {$services_table} s_base ON s_base.id = m.service_id
    LEFT JOIN {$services_table} s_enhanced ON (
        s_enhanced.name = s_base.name 
        AND s_enhanced.doctor_id = m.doctor_id 
        AND s_enhanced.clinic_id = m.clinic_id
    )
    WHERE m.status = 1 
    AND s_enhanced.id IS NULL
    ORDER BY m.service_id, m.doctor_id, m.clinic_id
");

if (empty($missing_mappings)) {
    $output .= "✅ All active mappings successfully migrated!" . PHP_EOL;
} else {
    $output .= "❌ Found " . count($missing_mappings) . " missing mappings:" . PHP_EOL;
    foreach ($missing_mappings as $missing) {
        $output .= "  - Mapping ID {$missing->id}: Service '{$missing->service_name}' (ID: {$missing->service_id}) for Doctor {$missing->doctor_id}, Clinic {$missing->clinic_id}" . PHP_EOL;
    }
}
$output .= PHP_EOL;

// 3. Detailed comparison
echo "3. Performing detailed comparison..." . PHP_EOL;
$output .= "=== DETAILED COMPARISON ===" . PHP_EOL;

// Get all mappings and their corresponding enhanced services
$comparison_query = "
    SELECT 
        m.id as mapping_id,
        m.service_id as original_service_id,
        m.doctor_id,
        m.clinic_id,
        m.charges as mapping_charges,
        m.status as mapping_status,
        m.extra as mapping_extra,
        m.telemed_service as mapping_telemed,
        m.duration as mapping_duration,
        s_base.name as service_name,
        s_base.type as service_type,
        s_base.price as service_price,
        s_enhanced.id as enhanced_service_id,
        s_enhanced.charges as enhanced_charges,
        s_enhanced.status as enhanced_status,
        s_enhanced.extra as enhanced_extra,
        s_enhanced.telemed_service as enhanced_telemed,
        s_enhanced.duration as enhanced_duration
    FROM {$mapping_table_to_compare} m
    LEFT JOIN {$services_table} s_base ON s_base.id = m.service_id
    LEFT JOIN {$services_table} s_enhanced ON (
        s_enhanced.name = s_base.name 
        AND s_enhanced.doctor_id = m.doctor_id 
        AND s_enhanced.clinic_id = m.clinic_id
    )
    WHERE m.status = 1
    ORDER BY m.service_id, m.doctor_id, m.clinic_id
";

$comparisons = $wpdb->get_results($comparison_query);

$mismatches = [];
$perfect_matches = 0;

foreach ($comparisons as $comp) {
    $issues = [];
    
    if (!$comp->enhanced_service_id) {
        $issues[] = "Enhanced service not found";
    } else {
        // Compare charges
        if ($comp->mapping_charges != $comp->enhanced_charges) {
            $issues[] = "Charges mismatch (mapping: {$comp->mapping_charges}, enhanced: {$comp->enhanced_charges})";
        }
        
        // Compare status
        if ($comp->mapping_status != $comp->enhanced_status) {
            $issues[] = "Status mismatch (mapping: {$comp->mapping_status}, enhanced: {$comp->enhanced_status})";
        }
        
        // Compare extra data
        if ($comp->mapping_extra != $comp->enhanced_extra) {
            $issues[] = "Extra data mismatch";
        }
        
        // Compare telemed service
        if ($comp->mapping_telemed != $comp->enhanced_telemed) {
            $issues[] = "Telemed service mismatch";
        }
        
        // Compare duration
        if ($comp->mapping_duration != $comp->enhanced_duration) {
            $issues[] = "Duration mismatch";
        }
    }
    
    if (empty($issues)) {
        $perfect_matches++;
    } else {
        $mismatches[] = [
            'mapping' => $comp,
            'issues' => $issues
        ];
    }
}

$output .= "Perfect matches: {$perfect_matches}" . PHP_EOL;
$output .= "Mismatches found: " . count($mismatches) . PHP_EOL . PHP_EOL;

if (!empty($mismatches)) {
    $output .= "=== MISMATCH DETAILS ===" . PHP_EOL;
    foreach ($mismatches as $mismatch) {
        $comp = $mismatch['mapping'];
        $output .= "Mapping ID {$comp->mapping_id} - Service '{$comp->service_name}' (Doctor {$comp->doctor_id}, Clinic {$comp->clinic_id}):" . PHP_EOL;
        foreach ($mismatch['issues'] as $issue) {
            $output .= "  - {$issue}" . PHP_EOL;
        }
        $output .= PHP_EOL;
    }
}

// 4. Appointment mappings check
echo "4. Checking appointment mappings..." . PHP_EOL;
$output .= "=== APPOINTMENT MAPPINGS CHECK ===" . PHP_EOL;

$orphaned_appointments = $wpdb->get_results("
    SELECT asm.*, a.appointment_start_date, a.appointment_start_time
    FROM {$wpdb->prefix}kc_appointment_service_mapping asm
    LEFT JOIN {$services_table} s ON s.id = asm.service_id
    LEFT JOIN {$wpdb->prefix}kc_appointments a ON a.id = asm.appointment_id
    WHERE s.id IS NULL
    ORDER BY asm.appointment_id
");

if (empty($orphaned_appointments)) {
    $output .= "✅ All appointment service mappings are valid!" . PHP_EOL;
} else {
    $output .= "❌ Found " . count($orphaned_appointments) . " orphaned appointment mappings:" . PHP_EOL;
    foreach ($orphaned_appointments as $orphaned) {
        $output .= "  - Appointment {$orphaned->appointment_id} references non-existent service {$orphaned->service_id}" . PHP_EOL;
    }
}

// Save the comparison report
file_put_contents($comparison_file, $output);

echo PHP_EOL . "=== COMPARISON COMPLETED ===" . PHP_EOL;
echo "Report saved to: {$comparison_file}" . PHP_EOL;
echo "Summary:" . PHP_EOL;
echo "- Perfect matches: {$perfect_matches}" . PHP_EOL;
echo "- Mismatches: " . count($mismatches) . PHP_EOL;
echo "- Missing mappings: " . count($missing_mappings) . PHP_EOL;
echo "- Orphaned appointments: " . count($orphaned_appointments) . PHP_EOL;

// Display the report content
echo PHP_EOL . "=== REPORT CONTENT ===" . PHP_EOL;
echo $output;
?>
