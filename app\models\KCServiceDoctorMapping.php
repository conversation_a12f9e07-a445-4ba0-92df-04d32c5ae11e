<?php

namespace App\models;

use App\baseClasses\KCModel;

/**
 * KCServiceDoctorMapping - Now uses enhanced services table
 * This class is maintained for backward compatibility but now operates on the enhanced services table
 */
class KCServiceDoctorMapping extends KCModel {

    public function __construct()
    {
        // Now points to the enhanced services table
        parent::__construct('services');
    }

	public function serviceUserPermission($service_id){
		$doctor_id = $this->get_var(
			[
				'id' => $service_id
			],
			'doctor_id'
		);

		// If service doesn't exist, allow admin users
		if (empty($doctor_id)) {
			$user_role = $this->getLoginUserRole();
			return $user_role === 'administrator' || $user_role === $this->getClinicAdminRole();
		}

		return (new KCUser())->doctorPermissionUserWise($doctor_id);
	}

	/**
	 * Get service mappings by doctor
	 */
	public function getServicesByDoctor($doctor_id, $clinic_id = null) {
		$conditions = ['doctor_id' => $doctor_id, 'status' => 1];

		if ($clinic_id) {
			$conditions['clinic_id'] = $clinic_id;
		}

		// Only get services that have doctor_id > 0 (enhanced services)
		global $wpdb;
		$services_table = $wpdb->prefix . 'kc_services';

		$where_clause = "doctor_id = %d AND status = 1";
		$params = [$doctor_id];

		if ($clinic_id) {
			$where_clause .= " AND clinic_id = %d";
			$params[] = $clinic_id;
		}

		$query = $wpdb->prepare("SELECT * FROM {$services_table} WHERE {$where_clause}", $params);
		return $wpdb->get_results($query);
	}

	/**
	 * Get service mappings by clinic
	 */
	public function getServicesByClinic($clinic_id) {
		global $wpdb;
		$services_table = $wpdb->prefix . 'kc_services';

		$query = $wpdb->prepare("
			SELECT * FROM {$services_table}
			WHERE clinic_id = %d AND status = 1 AND doctor_id > 0
		", $clinic_id);

		return $wpdb->get_results($query);
	}

	/**
	 * Update service charges for a specific doctor/clinic combination
	 */
	public function updateServiceCharges($service_id, $doctor_id, $clinic_id, $charges) {
		return $this->update(
			['charges' => $charges, 'updated_at' => current_time('mysql')],
			['id' => $service_id, 'doctor_id' => $doctor_id, 'clinic_id' => $clinic_id]
		);
	}

	/**
	 * Check if service exists for doctor/clinic combination
	 */
	public function serviceExistsForDoctorClinic($service_name, $doctor_id, $clinic_id) {
		global $wpdb;
		$services_table = $wpdb->prefix . 'kc_services';

		$query = $wpdb->prepare("
			SELECT id FROM {$services_table}
			WHERE name = %s AND doctor_id = %d AND clinic_id = %d AND status = 1
		", $service_name, $doctor_id, $clinic_id);

		return $wpdb->get_var($query);
	}

	/**
	 * Create new service with doctor/clinic mapping
	 */
	public function createServiceWithMapping($service_data) {
		$service_data['created_at'] = current_time('mysql');
		$service_data['updated_at'] = current_time('mysql');

		return $this->insert($service_data);
	}

	/**
	 * Get all mappings for a specific service (by original service name)
	 */
	public function getMappingsByServiceName($service_name) {
		global $wpdb;
		$services_table = $wpdb->prefix . 'kc_services';

		$query = $wpdb->prepare("
			SELECT * FROM {$services_table}
			WHERE name = %s
			AND status = 1
			AND doctor_id > 0
			ORDER BY doctor_id, clinic_id
		", $service_name);

		return $wpdb->get_results($query);
	}

	/**
	 * Delete service mapping (set status to 0)
	 */
	public function deleteMapping($service_id) {
		return $this->update(
			['status' => 0, 'updated_at' => current_time('mysql')],
			['id' => $service_id]
		);
	}

	/**
	 * Get mapping statistics
	 */
	public function getMappingStats() {
		global $wpdb;
		$services_table = $wpdb->prefix . 'kc_services';

		$stats = [];

		// Total services with mappings
		$stats['total_mapped_services'] = $wpdb->get_var("
			SELECT COUNT(*) FROM {$services_table}
			WHERE doctor_id > 0 AND status = 1
		");

		// Services by doctor count
		$stats['services_by_doctor'] = $wpdb->get_results("
			SELECT doctor_id, COUNT(*) as service_count
			FROM {$services_table}
			WHERE doctor_id > 0 AND status = 1
			GROUP BY doctor_id
		");

		// Services by clinic count
		$stats['services_by_clinic'] = $wpdb->get_results("
			SELECT clinic_id, COUNT(*) as service_count
			FROM {$services_table}
			WHERE clinic_id > 0 AND status = 1
			GROUP BY clinic_id
		");

		return $stats;
	}

}