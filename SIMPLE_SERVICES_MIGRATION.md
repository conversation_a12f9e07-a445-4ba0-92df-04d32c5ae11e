# Simple Services Migration Guide

## Overview

This migration enhances the existing `wp_kc_services` table by adding doctor/clinic mapping fields directly to it, eliminating the need for the `wp_kc_service_doctor_mapping` table.

## What Happens

1. **Automatic Migration**: Runs on site reload/refresh
2. **Table Enhancement**: Adds mapping fields to services table
3. **Data Migration**: Copies all mapping data to enhanced services
4. **Multiple Combinations**: Creates separate service records for each doctor/clinic pair
5. **Reference Updates**: Updates appointment mappings to use enhanced services

## Files Created

1. `app/database/migrations/2024_01_01_000000_enhance_services_table.php` - Main migration
2. `app/database/migrations/drop_mapping_table.php` - Optional cleanup script
3. Updated models: `KCService.php` and `KCServiceDoctorMapping.php`

## How It Works

### Before Migration
```
wp_kc_services:
- ID: 1, Name: "Consultation", Price: $50

wp_kc_service_doctor_mapping:
- ID: 1, service_id: 1, doctor_id: 10, clinic_id: 1, charges: $60
- ID: 2, service_id: 1, doctor_id: 20, clinic_id: 1, charges: $70
- ID: 3, service_id: 1, doctor_id: 10, clinic_id: 2, charges: $55
```

### After Migration
```
wp_kc_services:
- ID: 1, Name: "Consultation", doctor_id: 10, clinic_id: 1, charges: $60
- ID: 2, Name: "Consultation", doctor_id: 20, clinic_id: 1, charges: $70  
- ID: 3, Name: "Consultation", doctor_id: 10, clinic_id: 2, charges: $55
```

## Enhanced Table Structure

### New Fields Added to wp_kc_services
- `doctor_id` - Doctor associated with service
- `clinic_id` - Clinic where service is offered
- `charges` - Doctor-specific charges
- `extra` - Additional information
- `telemed_service` - Telemedicine flag
- `duration` - Service duration in minutes
- `service_name_alias` - Doctor-specific name
- `multiple` - Multiple service flag
- `image` - Service image
- `description` - Service description

## Migration Process

### Automatic Execution
The migration runs automatically when you reload your WordPress site. It:

1. Checks if migration has already run
2. Adds new columns to services table
3. Migrates data from mapping table
4. Updates appointment references
5. Sets completion flag

### Manual Verification

After the migration runs, verify it worked:

```sql
-- Check enhanced services were created
SELECT COUNT(*) as enhanced_services 
FROM wp_kc_services 
WHERE doctor_id > 0;

-- Check original mappings count
SELECT COUNT(*) as original_mappings 
FROM wp_kc_service_doctor_mapping;

-- These numbers should match
```

### Check for Issues

```sql
-- Look for services with doctor_id = 0 (these weren't migrated)
SELECT * FROM wp_kc_services WHERE doctor_id = 0 AND id IN (
    SELECT DISTINCT service_id FROM wp_kc_service_doctor_mapping
);

-- Check for orphaned appointment references
SELECT COUNT(*) FROM wp_kc_appointment_service_mapping asm 
LEFT JOIN wp_kc_services s ON asm.service_id = s.id 
WHERE s.id IS NULL;
```

## Using Enhanced Services

### Get Services for Doctor/Clinic
```php
$service = new KCService();

// Get services for specific doctor and clinic
$services = $service->getServicesWithCategory([
    'doctor_id' => 10,
    'clinic_id' => 1
]);

// Get all services for a doctor
$services = $service->getServicesByDoctor(10);

// Get all services for a clinic
$services = $service->getServicesByClinic(1);
```

### Create New Service
```php
$mapping = new KCServiceDoctorMapping();

$service_id = $mapping->createServiceWithMapping([
    'name' => 'New Service',
    'type' => 'consultation',
    'price' => '100',
    'doctor_id' => 10,
    'clinic_id' => 1,
    'charges' => '120',
    'duration' => 30,
    'description' => 'Service description'
]);
```

## Cleanup (After Testing)

Once you've confirmed everything works correctly:

1. **Test thoroughly** - Check all service-related functionality
2. **Verify data integrity** - Ensure all services and appointments work
3. **Drop mapping table** (optional):

```php
// Uncomment and run in drop_mapping_table.php
drop_service_mapping_table();
```

Or manually:
```sql
DROP TABLE wp_kc_service_doctor_mapping;
```

## Troubleshooting

### Migration Didn't Run
- Check error logs: `wp-content/debug.log`
- Manually trigger: Add `?force_migration=1` to any page URL

### Doctor ID is 0
- Check if mapping table exists and has data
- Verify mapping table has proper service_id references
- Check for orphaned mappings (mappings without valid service_id)

### Appointment Booking Issues
- Verify appointment_service_mapping references are updated
- Check that service IDs in appointments point to enhanced services

### Performance Issues
- Ensure indexes were created properly:
```sql
SHOW INDEX FROM wp_kc_services;
```

## Rollback

If you need to rollback:

```php
$migration = new EnhanceServicesTableMigration();
$migration->down();
delete_option('kc_services_enhanced_migration_run');
```

This will:
- Remove added columns from services table
- Remove indexes
- Reset migration flag

## Important Notes

- ✅ **Automatic**: Runs on site reload
- ✅ **Safe**: Only runs once, won't duplicate data
- ✅ **Simple**: Minimal files, clean implementation
- ✅ **Backward Compatible**: All existing code continues to work
- ⚠️ **Test First**: Always test in staging before production
- ⚠️ **Backup**: Take database backup before running

The migration is designed to be simple, safe, and automatic. It will enhance your services table and eliminate the mapping table complexity while maintaining all existing functionality.
