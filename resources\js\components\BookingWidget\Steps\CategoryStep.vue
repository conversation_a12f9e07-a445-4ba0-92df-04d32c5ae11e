<template>
  <div class="kivi-booking-step" id="step-category">
    <h2 class="kivi-step-title"></h2>
    <p class="kivi-step-subtitle"></p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader"></div>
    </div>

    <template v-else>
      <div class="kivi-form-group kivi-search-input">
        <div class="kivi-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          class="kivi-form-input"
          id="category-search"
          placeholder="Search categories..."
          v-model="searchTerm"
        >
      </div>

      <div class="kivi-grid" id="category-list">
        <div
          v-for="category in filteredCategories"
          :key="category.category_value"
          class="kivi-card category-card"
          :class="{ 'selected': selectedCategoryId === category.category_value }"
          @click="selectCategory(category)"
        >
          <div class="kivi-card-header">
            <div>
              <h3 class="kivi-card-title">{{ category.name || formatCategoryName(category.category_value) }}</h3>
              <div class="kivi-card-subtitle">{{ category.service_count || '?' }} services available</div>
            </div>
          </div>
          <div class="kivi-card-footer">
            <span class="kivi-badge kivi-badge-purple">In-clinic</span>
            <span v-if="hasVirtualServices(category)" class="kivi-badge kivi-badge-green">Virtual</span>
          </div>
        </div>
      </div>

      <div v-if="filteredCategories.length === 0 && !isLoading && showNoResults" class="kivi-empty-state">
        <p>No categories found. Please try another search term or select a different clinic.</p>
      </div>
    </template>
  </div>
</template>

<script>
import { apiCall } from '../../../config/request';

export default {
  name: 'CategoryStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      categories: [],
      searchTerm: '',
      selectedCategoryId: null,
      isLoading: false,
      isInitialLoad: true, // Flag to track initial loading state
      showNoResults: false, // Flag to control when to show "No categories found" message
      virtualCategoryIds: [], // IDs of categories that have virtual services
      fetchInProgress: false, // Flag to prevent multiple simultaneous API calls
      categoryCache: {}, // Cache for category data by clinic ID
      serviceCache: {} // Cache for service data by clinic ID
    };
  },
  computed: {
    filteredCategories() {
      if (!this.searchTerm) {
        return this.categories;
      }

      const term = this.searchTerm.toLowerCase();
      return this.categories.filter(category =>
        this.formatCategoryName(category.category_value).toLowerCase().includes(term)
      );
    }
  },
  watch: {
    'bookingData.clinic': {
      immediate: true,
      handler(newClinic) {
        if (newClinic && newClinic.id) {
          // Reset the no results flag when changing clinics
          this.showNoResults = false;

          // Debounce the fetch call to prevent multiple rapid calls
          if (this.fetchDebounceTimer) {
            clearTimeout(this.fetchDebounceTimer);
          }

          this.fetchDebounceTimer = setTimeout(() => {
            this.fetchCategoriesWithCache(newClinic.id);
          }, 100); // 100ms debounce
        }
      }
    },
    searchTerm() {
      // Update the no results state when the search term changes
      this.updateNoResultsState();
    }
  },

  mounted() {
    // Check if we already have a clinic selected
    if (this.bookingData.clinic && this.bookingData.clinic.id) {
      this.fetchCategoriesWithCache(this.bookingData.clinic.id);
    }
  },
  methods: {
    // New method that uses caching to prevent redundant API calls
    async fetchCategoriesWithCache(clinicId) {
      // If a fetch is already in progress, don't start another one
      if (this.fetchInProgress) {
        return;
      }

      this.fetchInProgress = true;
      this.isLoading = true;

      try {
        // Check if we have cached data for this clinic
        if (this.categoryCache[clinicId]) {
          this.categories = [...this.categoryCache[clinicId]];

          // If we have cached service data, use that too
          if (this.serviceCache[clinicId]) {
            this.virtualCategoryIds = [...this.serviceCache[clinicId]];
          } else {
            // Only fetch services if we don't have them cached
            await this.fetchVirtualServiceDataWithCache(clinicId);
          }
        } else {
          // No cached data, fetch from API
          await this.fetchCategories(clinicId);
        }

        // Handle selection logic after data is loaded (either from cache or API)
        this.handleCategorySelection();
      } catch (error) {
        console.error('Error in fetchCategoriesWithCache:', error);
      } finally {
        this.isLoading = false;
        this.fetchInProgress = false;

        // Set isInitialLoad to false after the first load
        if (this.isInitialLoad) {
          this.isInitialLoad = false;
        }

        // Control when to show "No categories found" message
        this.updateNoResultsState();
      }
    },

    // Handle category selection logic (extracted to avoid duplication)
    handleCategorySelection() {
      // If we only have one category, preselect it
      if (this.categories.length === 1) {
        this.selectCategory(this.categories[0]);
      }

      // If we already have a selected category in bookingData, select it
      if (this.bookingData.category && this.bookingData.category.id) {
        const existingCategory = this.categories.find(
          c => c.category_value === this.bookingData.category.id
        );

        if (existingCategory) {
          this.selectedCategoryId = existingCategory.category_value;
        }
      }
    },

    // Original fetch method, now updated to cache results
    async fetchCategories(clinicId) {
      try {
        this.categories = [];
        this.selectedCategoryId = null;

        // Make sure clinicId is defined
        if (!clinicId) {
          console.error('No clinic ID provided to fetch categories');
          return;
        }

        // Try using the correct endpoint for categories
        let response;
        try {
          response = await apiCall.get('get_clinic_service_category', {
            params: {
              clinic_id: clinicId,
              format: 'json' // Request JSON format explicitly
            }
          });
        } catch (error) {
          console.error('Error using apiCall.get, trying direct fetch:', error);

          // Fallback to direct fetch if apiCall.get fails
          const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
          const nonce = window.ajaxData?.get_nonce || '';

          const fetchResponse = await fetch(
            `${ajaxurl}?action=ajax_get&route_name=get_clinic_service_category&clinic_id=${clinicId}&format=json&_ajax_nonce=${nonce}`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              }
            }
          );

          const jsonData = await fetchResponse.json();
          response = {
            data: jsonData
          };
        }



        if (response.data.status) {
          // Process the categories data
          if (Array.isArray(response.data.data)) {
            // Transform the data to match our expected format
            this.categories = response.data.data.map(category => ({
              category_value: category.category_id || category.category_value,
              service_count: category.service_count || 0,
              name: category.category_name || this.formatCategoryName(category.category_id || category.category_value)
            }));

            // Cache the processed categories
            this.categoryCache[clinicId] = [...this.categories];


          } else if (typeof response.data.data === 'string' && response.data.data.includes('<')) {
            // Handle HTML content as fallback
            try {
              // Sometimes the API sends JSON data wrapped in HTML
              const jsonMatch = response.data.data.match(/\{.*\}/s);
              if (jsonMatch) {
                const jsonData = JSON.parse(jsonMatch[0]);
                this.categories = (jsonData.data || []).map(category => ({
                  category_value: category.id || category.category_value,
                  service_count: category.service_count || 0,
                  name: category.name || this.formatCategoryName(category.id || category.category_value)
                }));

                // Cache the processed categories
                this.categoryCache[clinicId] = [...this.categories];
              } else {
                console.warn('Unable to extract category data from HTML response');
              }
            } catch (e) {
              console.error('Error parsing category JSON from HTML:', e);
            }
          }

          // Fetch virtual service data (only once per clinic)
          await this.fetchVirtualServiceDataWithCache(clinicId);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    },

    // New method that uses caching for service data
    async fetchVirtualServiceDataWithCache(clinicId) {
      // Check if we already have cached service data
      if (this.serviceCache[clinicId]) {
        console.log('Using cached virtual service data for clinic ID:', clinicId);
        this.virtualCategoryIds = [...this.serviceCache[clinicId]];
        return;
      }

      // No cached data, fetch from API
      await this.fetchVirtualServiceData(clinicId);
    },

    // Original service fetch method, now updated to cache results
    async fetchVirtualServiceData(clinicId) {
      try {
        // Use the get_clinic_service endpoint to get all services for this clinic
        const response = await apiCall.get('get_clinic_service', {
          params: {
            clinic_id: clinicId,
            format: 'json'
          }
        });

        console.log('Virtual services response:', response.data);

        if (response.data.status && response.data.data) {
          let services = [];

          if (Array.isArray(response.data.data)) {
            services = response.data.data;
          } else if (typeof response.data.data === 'string' && response.data.data.includes('<')) {
            // Try to extract JSON from HTML if necessary
            try {
              const jsonMatch = response.data.data.match(/\{.*\}/s);
              if (jsonMatch) {
                const jsonData = JSON.parse(jsonMatch[0]);
                services = jsonData.data || [];
              }
            } catch (e) {
              console.error('Error parsing services JSON from HTML:', e);
            }
          }

          // Find categories with virtual services
          const virtualCategories = new Set();

          services.forEach(service => {
            // Check if the service is a telemedicine service
            if (service.telemed_service === 'yes' || service.type === 'virtual') {
              // Get category from category_id, service_name_alias or type
              const categoryValue = service.category_id || service.service_name_alias || service.type;
              if (categoryValue) {
                virtualCategories.add(categoryValue);
              }
            }
          });

          this.virtualCategoryIds = Array.from(virtualCategories);

          // Cache the virtual category IDs
          this.serviceCache[clinicId] = [...this.virtualCategoryIds];

          console.log('Virtual category IDs:', this.virtualCategoryIds);
        }
      } catch (error) {
        console.error('Error fetching services:', error);
      }
    },

    formatCategoryName(categoryValue) {
      if (!categoryValue) return '';

      // Replace underscores with spaces and capitalize each word
      return categoryValue
        .replace(/_/g, ' ')
        .replace(/\b\w/g, char => char.toUpperCase());
    },

    hasVirtualServices(category) {
      return this.virtualCategoryIds.includes(category.category_value);
    },

    selectCategory(category) {
      this.selectedCategoryId = category.category_value;

      // Update parent component with new selection
      this.$emit('update:booking-data', {
        ...this.bookingData,
        category: {
          id: category.category_value,
          name: this.formatCategoryName(category.category_value)
        },
        // Clear subsequent selections when changing category
        services: []
      });

      // Removed auto-advance to next step - user must click Next button
    },

    // Method to control when to show "No categories found" message
    updateNoResultsState() {
      // Reset the flag first
      this.showNoResults = false;

      // If we have no categories and we're not in the initial loading state
      if (this.categories.length === 0 && !this.isInitialLoad && !this.isLoading) {
        // Use a delay to prevent the message from flashing briefly
        setTimeout(() => {
          // Only show the message if we still have no categories
          if (this.categories.length === 0 && !this.isLoading) {
            this.showNoResults = true;
          }
        }, 500); // 500ms delay
      }
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-form-group {
  margin-bottom: 1rem;
}

.kivi-search-input {
  position: relative;
}

.kivi-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.kivi-search-input input {
  padding-left: 2.5rem;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.kivi-card {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.kivi-card:hover {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
}

.kivi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kivi-card-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.75rem;
  color: var(--gray);
}

.kivi-card-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.kivi-badge-purple {
  background-color: rgba(124, 58, 237, 0.1);
  color: rgba(109, 40, 217, 1);
}

.kivi-badge-green {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgba(5, 150, 105, 1);
}

.kivi-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-empty-state {
  text-align: center;
  padding: 2rem 0;
  color: var(--gray);
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .kivi-grid {
    grid-template-columns: 1fr;
  }
}
</style>