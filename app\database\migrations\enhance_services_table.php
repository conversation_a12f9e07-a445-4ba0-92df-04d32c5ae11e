<?php

class EnhanceServicesTable {

    public function up() {
        global $wpdb;
        
        $services_table = $wpdb->prefix . 'kc_services';
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        
        // Step 1: Add new columns to services table if they don't exist
        $this->addColumnsToServicesTable($services_table);
        
        // Step 2: Migrate data from mapping table to services table
        $this->migrateDataFromMappingTable($services_table, $mapping_table);
        
        // Step 3: Update references in other tables
        $this->updateReferences();

        // Step 4: Rename the old mapping table to _old (keep as backup)
        $this->renameMappingTable($mapping_table);

        return true;
    }
    
    private function addColumnsToServicesTable($services_table) {
        global $wpdb;
        
        $columns_to_add = [
            'doctor_id' => 'bigint(20) UNSIGNED DEFAULT 0',
            'clinic_id' => 'int(11) DEFAULT 0',
            'charges' => 'varchar(50) DEFAULT "0"',
            'extra' => 'longtext NULL',
            'telemed_service' => 'varchar(10) NULL',
            'duration' => 'int(11) NULL',
            'service_name_alias' => 'varchar(191) NULL',
            'multiple' => 'varchar(191) NULL',
            'image' => 'bigint(20) NULL',
            'description' => 'varchar(255) NULL'
        ];
        
        foreach ($columns_to_add as $column => $definition) {
            $column_exists = $wpdb->get_var("SHOW COLUMNS FROM {$services_table} LIKE '{$column}'");
            if (!$column_exists) {
                $wpdb->query("ALTER TABLE {$services_table} ADD COLUMN `{$column}` {$definition}");
            }
        }
        
        // Add indexes
        $wpdb->query("ALTER TABLE {$services_table} ADD INDEX IF NOT EXISTS `idx_doctor_clinic` (`doctor_id`, `clinic_id`)");
        $wpdb->query("ALTER TABLE {$services_table} ADD INDEX IF NOT EXISTS `idx_doctor_id` (`doctor_id`)");
        $wpdb->query("ALTER TABLE {$services_table} ADD INDEX IF NOT EXISTS `idx_clinic_id` (`clinic_id`)");
    }
    
    private function migrateDataFromMappingTable($services_table, $mapping_table) {
        global $wpdb;
        
        // Check if mapping table exists
        if (!$wpdb->get_var("SHOW TABLES LIKE '{$mapping_table}'")) {
            return;
        }
        
        // Get all service mappings
        $mappings = $wpdb->get_results("
            SELECT 
                m.*,
                s.name,
                s.type,
                s.category_id,
                s.price,
                s.visibility,
                s.sort_order,
                s.status as service_status,
                s.created_at as service_created_at,
                s.updated_at as service_updated_at
            FROM {$mapping_table} m
            INNER JOIN {$services_table} s ON m.service_id = s.id
            WHERE m.status = 1
            ORDER BY m.service_id, m.id
        ");
        
        $processed_services = [];
        
        foreach ($mappings as $mapping) {
            if (!isset($processed_services[$mapping->service_id])) {
                // First mapping for this service - update the original service
                $wpdb->update(
                    $services_table,
                    [
                        'doctor_id' => $mapping->doctor_id,
                        'clinic_id' => $mapping->clinic_id,
                        'charges' => $mapping->charges ?: $mapping->price,
                        'extra' => $mapping->extra,
                        'telemed_service' => $mapping->telemed_service,
                        'duration' => $mapping->duration,
                        'service_name_alias' => $mapping->service_name_alias,
                        'multiple' => $mapping->multiple,
                        'image' => $mapping->image,
                        'description' => $mapping->description,
                        'updated_at' => current_time('mysql')
                    ],
                    ['id' => $mapping->service_id]
                );
                
                $processed_services[$mapping->service_id] = $mapping->service_id;
            } else {
                // Additional mapping for this service - create new service record
                $wpdb->insert(
                    $services_table,
                    [
                        'type' => $mapping->type,
                        'category_id' => $mapping->category_id,
                        'name' => $mapping->name,
                        'price' => $mapping->price,
                        'visibility' => $mapping->visibility ?: 'public',
                        'sort_order' => $mapping->sort_order ?: 0,
                        'status' => $mapping->service_status,
                        'created_at' => $mapping->service_created_at,
                        'updated_at' => current_time('mysql'),
                        'doctor_id' => $mapping->doctor_id,
                        'clinic_id' => $mapping->clinic_id,
                        'charges' => $mapping->charges ?: $mapping->price,
                        'extra' => $mapping->extra,
                        'telemed_service' => $mapping->telemed_service,
                        'duration' => $mapping->duration,
                        'service_name_alias' => $mapping->service_name_alias,
                        'multiple' => $mapping->multiple,
                        'image' => $mapping->image,
                        'description' => $mapping->description
                    ]
                );
            }
        }
    }
    
    private function updateReferences() {
        global $wpdb;
        
        $services_table = $wpdb->prefix . 'kc_services';
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        $appointment_mapping_table = $wpdb->prefix . 'kc_appointment_service_mapping';
        
        // Update appointment service mappings to reference the enhanced services
        if ($wpdb->get_var("SHOW TABLES LIKE '{$appointment_mapping_table}'")) {
            // For appointments that reference mapping IDs, update to service IDs
            $wpdb->query("
                UPDATE {$appointment_mapping_table} asm
                INNER JOIN {$mapping_table} m ON asm.service_id = m.id
                INNER JOIN {$services_table} s ON s.doctor_id = m.doctor_id 
                    AND s.clinic_id = m.clinic_id 
                    AND s.name = (SELECT name FROM {$services_table} WHERE id = m.service_id)
                SET asm.service_id = s.id
                WHERE s.doctor_id > 0
            ");
        }
    }

    private function renameMappingTable($mapping_table) {
        global $wpdb;

        $old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';

        // Check if mapping table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$mapping_table}'")) {
            // Check if old table already exists
            if ($wpdb->get_var("SHOW TABLES LIKE '{$old_mapping_table}'")) {
                // If old table exists, drop it first
                $wpdb->query("DROP TABLE IF EXISTS {$old_mapping_table}");
            }

            // Rename the mapping table to _old
            $wpdb->query("RENAME TABLE {$mapping_table} TO {$old_mapping_table}");
        }
    }

    public function down() {
        global $wpdb;

        $services_table = $wpdb->prefix . 'kc_services';
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        $old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';

        // Restore the mapping table if it exists as _old
        if ($wpdb->get_var("SHOW TABLES LIKE '{$old_mapping_table}'")) {
            $wpdb->query("RENAME TABLE {$old_mapping_table} TO {$mapping_table}");
        }

        // Remove added columns
        $columns_to_remove = [
            'doctor_id', 'clinic_id', 'charges', 'extra', 'telemed_service',
            'duration', 'service_name_alias', 'multiple', 'image', 'description'
        ];

        foreach ($columns_to_remove as $column) {
            $wpdb->query("ALTER TABLE {$services_table} DROP COLUMN IF EXISTS `{$column}`");
        }

        // Remove indexes
        $wpdb->query("ALTER TABLE {$services_table} DROP INDEX IF EXISTS `idx_doctor_clinic`");
        $wpdb->query("ALTER TABLE {$services_table} DROP INDEX IF EXISTS `idx_doctor_id`");
        $wpdb->query("ALTER TABLE {$services_table} DROP INDEX IF EXISTS `idx_clinic_id`");
    }
}