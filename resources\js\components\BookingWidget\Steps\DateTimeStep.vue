<template>
  <div class="kivi-booking-step" id="step-datetime">
    <h2 class="kivi-step-title">Select Date and Time</h2>
    <p class="kivi-step-subtitle">Choose when you'd like to schedule your appointment.</p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader"></div>
    </div>

    <template v-else>
      <div class="kivi-date-time-container">
        <!-- Calendar Section -->
        <div class="kivi-date-section">
          <h3 class="kivi-section-title">1. Select a Date</h3>

          <!-- Month Navigation -->
          <div class="kivi-calendar-header">
            <button
              @click="previousMonth"
              class="kivi-calendar-nav-btn"
              :disabled="isPreviousMonthDisabled"
              :class="{ 'disabled': isPreviousMonthDisabled }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <span class="kivi-current-month">{{ currentMonthName }} {{ currentYear }}</span>
            <button
              @click="nextMonth"
              class="kivi-calendar-nav-btn"
              :disabled="isNextMonthDisabled"
              :class="{ 'disabled': isNextMonthDisabled }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>

          <!-- Weekday Headers -->
          <div class="kivi-calendar-weekdays">
            <div v-for="day in weekdays" :key="day" class="kivi-weekday">{{ day }}</div>
          </div>

          <!-- Calendar Days -->
          <div class="kivi-calendar-days">
            <!-- Placeholder for days from previous month -->
            <div
              v-for="emptyDay in firstDayOfMonth"
              :key="'empty-' + emptyDay"
              class="kivi-calendar-day empty"
            ></div>

            <!-- Actual days of current month -->
            <div
              v-for="day in daysInMonth"
              :key="day"
              class="kivi-calendar-day"
              :class="{
                'unavailable': isDateUnavailable(day),
                'selected': isDateSelected(day),
                'has-appointments': hasAvailableAppointments(day),
                'today': isToday(day),
                'future': isFutureDate(day)
              }"
              @click="selectDate(day)"
            >
              <span class="kivi-day-number">{{ day }}</span>
              <span v-if="hasAvailableAppointments(day)" class="kivi-availability-indicator">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="kivi-availability-icon">
                  <circle cx="12" cy="12" r="10" />
                </svg>
              </span>
            </div>

            <!-- Placeholder for days from next month -->
            <div
              v-for="emptyDay in remainingDaysInCalendarView"
              :key="'next-empty-' + emptyDay"
              class="kivi-calendar-day empty"
            ></div>
          </div>

          <div class="kivi-calendar-legend">
            <div class="kivi-legend-item">
              <span class="kivi-legend-indicator available"></span>
              <span class="kivi-legend-text">Available</span>
            </div>
            <div class="kivi-legend-item">
              <span class="kivi-legend-indicator unavailable"></span>
              <span class="kivi-legend-text">Unavailable</span>
            </div>
            <div class="kivi-legend-item">
              <span class="kivi-legend-indicator selected"></span>
              <span class="kivi-legend-text">Selected</span>
            </div>
          </div>
        </div>

        <!-- Time Slots Section -->
        <div class="kivi-time-section">
          <h3 class="kivi-section-title">2. Select a Time</h3>

          <template v-if="selectedDateObj">
            <div class="kivi-selected-date">
              <span>{{ formatSelectedDate(selectedDateObj) }}</span>
            </div>

            <div v-if="isLoadingSlots" class="kivi-loader-container">
              <div class="kivi-loader"></div>
            </div>

            <div v-else-if="timeSlots.length === 0" class="kivi-empty-state">
              <svg xmlns="http://www.w3.org/2000/svg" class="kivi-empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="8" x2="12" y2="12" />
                <line x1="12" y1="16" x2="12.01" y2="16" />
              </svg>
              <p>No available time slots for the selected date.</p>
              <p>Please choose another date with available slots.</p>
            </div>

            <template v-else>
              <!-- Time slot availability grouping -->
              <div class="kivi-time-slot-groups">
                <div v-if="morningSlots.length > 0" class="kivi-time-slot-group">
                  <h4 class="kivi-time-group-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="kivi-time-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="5" />
                      <line x1="12" y1="1" x2="12" y2="3" />
                      <line x1="12" y1="21" x2="12" y2="23" />
                      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
                      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
                      <line x1="1" y1="12" x2="3" y2="12" />
                      <line x1="21" y1="12" x2="23" y2="12" />
                      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
                      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
                    </svg>
                    Morning
                  </h4>
                  <div class="kivi-time-slots">
                    <div
                      v-for="slot in morningSlots"
                      :key="slot.time"
                      class="kivi-time-slot"
                      :class="{ 'selected': selectedTime === slot.time }"
                      @click="selectTimeSlot(slot.time)"
                    >
                      <span class="kivi-time-slot-time">{{ formatTime(slot.time) }}</span>
                    </div>
                  </div>
                </div>

                <div v-if="afternoonSlots.length > 0" class="kivi-time-slot-group">
                  <h4 class="kivi-time-group-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="kivi-time-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 1v22m5.5-15h-11" />
                    </svg>
                    Afternoon
                  </h4>
                  <div class="kivi-time-slots">
                    <div
                      v-for="slot in afternoonSlots"
                      :key="slot.time"
                      class="kivi-time-slot"
                      :class="{ 'selected': selectedTime === slot.time }"
                      @click="selectTimeSlot(slot.time)"
                    >
                      <span class="kivi-time-slot-time">{{ formatTime(slot.time) }}</span>
                    </div>
                  </div>
                </div>

                <div v-if="eveningSlots.length > 0" class="kivi-time-slot-group">
                  <h4 class="kivi-time-group-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="kivi-time-group-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z" />
                    </svg>
                    Evening
                  </h4>
                  <div class="kivi-time-slots">
                    <div
                      v-for="slot in eveningSlots"
                      :key="slot.time"
                      class="kivi-time-slot"
                      :class="{ 'selected': selectedTime === slot.time }"
                      @click="selectTimeSlot(slot.time)"
                    >
                      <span class="kivi-time-slot-time">{{ formatTime(slot.time) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </template>

          <div v-else class="kivi-prompt-select-date">
            <svg xmlns="http://www.w3.org/2000/svg" class="kivi-prompt-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
            <p>Please select a date first</p>
          </div>
        </div>
      </div>

      <!-- Selection Summary -->
      <div v-if="selectedDateObj && selectedTime" class="kivi-selection-summary">
        <h3 class="kivi-summary-title">Your Selection</h3>
        <div class="kivi-summary-content">
          <div class="kivi-summary-item">
            <svg xmlns="http://www.w3.org/2000/svg" class="kivi-summary-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
            <span>{{ formatSelectedDate(selectedDateObj) }}</span>
          </div>
          <div class="kivi-summary-item">
            <svg xmlns="http://www.w3.org/2000/svg" class="kivi-summary-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
            <span>{{ formatTime(selectedTime) }}</span>
          </div>
        </div>

        <button class="kivi-reset-selection" @click="resetSelection">
          <svg xmlns="http://www.w3.org/2000/svg" class="kivi-reset-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 12a9 9 0 0 1-9 9c-2.39 0-4.68-.94-6.4-2.6" />
            <path d="M9 6.6A9 9 0 0 1 21 12" />
            <polyline points="3 14 9 18 15 12" />
          </svg>
          Change Selection
        </button>
      </div>
    </template>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'DateTimeStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      selectedDateObj: null,
      selectedTime: null,
      timeSlots: [],
      isLoading: false,
      isLoadingSlots: false,
      disabledDates: [],
      clinicHolidays: [],
      datesWithAvailability: [],
      availableWeekdays: [], // Days of week (0-6) when doctor works

      // Calendar data
      currentMonth: new Date().getMonth(),
      currentYear: new Date().getFullYear(),
      weekdays: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
      monthNames: [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ]
    };
  },
  computed: {
    // These are now used only for UI purposes, not for API calls
    clinicId() {
      return this.bookingData.clinic ? this.bookingData.clinic.id : null;
    },

    serviceIds() {
      return this.bookingData.services ? this.bookingData.services.map(s => s.id).join(',') : '';
    },

    doctorId() {
      return this.bookingData.doctor ? this.bookingData.doctor.id : null;
    },

    minDate() {
      // Set minimum date to today (can't book for past dates)
      return new Date();
    },

    maxDate() {
      // Set maximum date to 3 months from now
      const maxDate = new Date();
      maxDate.setMonth(maxDate.getMonth() + 3);
      return maxDate;
    },

    // Calendar navigation controls
    isPreviousMonthDisabled() {
      // Check if the previous month is earlier than the current month
      const today = new Date();
      if (this.currentYear < today.getFullYear()) {
        return true;
      }
      if (this.currentYear === today.getFullYear() && this.currentMonth <= today.getMonth()) {
        return true;
      }
      return false;
    },

    isNextMonthDisabled() {
      // Check if the next month is later than the max allowed date
      const maxAllowed = new Date(this.maxDate);
      if (this.currentYear > maxAllowed.getFullYear()) {
        return true;
      }
      if (this.currentYear === maxAllowed.getFullYear() && this.currentMonth >= maxAllowed.getMonth()) {
        return true;
      }
      return false;
    },

    currentMonthName() {
      return this.monthNames[this.currentMonth];
    },

    daysInMonth() {
      // Returns the number of days in the current month
      return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
    },

    firstDayOfMonth() {
      // Return the day of the week for the first day of the month (0-6, where 0 is Sunday)
      return new Date(this.currentYear, this.currentMonth, 1).getDay();
    },

    remainingDaysInCalendarView() {
      // Calculate how many empty slots are needed at the end of the calendar to complete the grid
      const totalDaysInView = Math.ceil((this.daysInMonth + this.firstDayOfMonth) / 7) * 7;
      return totalDaysInView - this.daysInMonth - this.firstDayOfMonth;
    },

    // This helper function has been removed and its functionality
    // has been integrated directly into the morning/afternoon/evening slot methods

    // Time slot grouping
    morningSlots() {
      // Directly use time parsing logic instead of separate method
      return this.timeSlots.filter(slot => {
        if (!slot || !slot.time) return false;

        try {
          const timeStr = String(slot.time);
          let hours = 0;

          // Handle am/pm format
          if (timeStr.toLowerCase().includes('am')) {
            // Morning slot
            return true;
          } else if (timeStr.toLowerCase().includes('pm')) {
            // Not a morning slot
            const hourMatch = timeStr.match(/(\d+):/);
            if (hourMatch && hourMatch[1] && parseInt(hourMatch[1]) === 12) {
              return true; // 12pm is still considered morning/noon
            }
            return false;
          } else if (timeStr.includes(':')) {
            // 24-hour format
            const hourPart = timeStr.split(':')[0];
            hours = parseInt(hourPart, 10) || 0;
            return hours >= 0 && hours < 12;
          }
          return false;
        } catch (error) {
          console.error('Error processing morning slot:', slot, error);
          return false;
        }
      });
    },

    afternoonSlots() {
      return this.timeSlots.filter(slot => {
        if (!slot || !slot.time) return false;

        try {
          const timeStr = String(slot.time);
          let hours = 0;

          // Handle am/pm format
          if (timeStr.toLowerCase().includes('pm')) {
            const hourMatch = timeStr.match(/(\d+):/);
            if (hourMatch && hourMatch[1]) {
              const hour = parseInt(hourMatch[1]);
              return hour >= 12 || hour <= 4; // 12pm-4pm
            }
          } else if (timeStr.includes(':')) {
            // 24-hour format
            const hourPart = timeStr.split(':')[0];
            hours = parseInt(hourPart, 10) || 0;
            return hours >= 12 && hours < 17;
          }
          return false;
        } catch (error) {
          console.error('Error processing afternoon slot:', slot, error);
          return false;
        }
      });
    },

    eveningSlots() {
      return this.timeSlots.filter(slot => {
        if (!slot || !slot.time) return false;

        try {
          const timeStr = String(slot.time);
          let hours = 0;

          // Handle am/pm format
          if (timeStr.toLowerCase().includes('pm')) {
            const hourMatch = timeStr.match(/(\d+):/);
            if (hourMatch && hourMatch[1]) {
              const hour = parseInt(hourMatch[1]);
              return hour >= 5 && hour <= 11; // 5pm-11pm
            }
          } else if (timeStr.includes(':')) {
            // 24-hour format
            const hourPart = timeStr.split(':')[0];
            hours = parseInt(hourPart, 10) || 0;
            return hours >= 17 && hours < 24;
          }
          return false;
        } catch (error) {
          console.error('Error processing evening slot:', slot, error);
          return false;
        }
      });
    }
  },
  watch: {
    selectedDateObj(newDate) {
      if (newDate) {
        this.fetchTimeSlots(newDate);
      } else {
        this.timeSlots = [];
        this.selectedTime = null;
      }
    },

    selectedTime(newTime) {
      if (newTime) {
        // Update parent component with new selection
        this.$emit('update:booking-data', {
          ...this.bookingData,
          date: this.formatDateForApi(this.selectedDateObj),
          time: newTime
        });
      }
    }
  },
  mounted() {
    console.log('DateTimeStep mounted, booking data:', this.bookingData);

    // Initialize the selected date to today or tomorrow if today is not available
    const today = new Date();
    this.selectedDateObj = today;

    // If we already have date and time in the booking data, set them
    if (this.bookingData.date && this.bookingData.time) {
      this.selectedDateObj = new Date(this.bookingData.date);
      this.selectedTime = this.bookingData.time;

      // Adjust the current month and year to match the selected date
      this.currentMonth = this.selectedDateObj.getMonth();
      this.currentYear = this.selectedDateObj.getFullYear();
    }

    // Load available appointments
    this.loadAvailableDays();
  },
  methods: {
    // Calendar Navigation
    previousMonth() {
      if (this.isPreviousMonthDisabled) return;

      if (this.currentMonth === 0) {
        this.currentMonth = 11;
        this.currentYear--;
      } else {
        this.currentMonth--;
      }
    },

    nextMonth() {
      if (this.isNextMonthDisabled) return;

      if (this.currentMonth === 11) {
        this.currentMonth = 0;
        this.currentYear++;
      } else {
        this.currentMonth++;
      }
    },

    // Date Selection and Validation
    selectDate(day) {
      // Create a new date object for the selected day
      const date = new Date(this.currentYear, this.currentMonth, day);

      // Check if the date is unavailable
      if (this.isDateUnavailable(day)) {
        return;
      }

      // Set as selected date
      this.selectedDateObj = date;

      // Clear any previously selected time
      this.selectedTime = null;

      // Fetch time slots for this date
      this.fetchTimeSlots(date);
    },

    isDateUnavailable(day) {
      const date = new Date(this.currentYear, this.currentMonth, day);

      // Past dates are unavailable
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (date < today) return true;

      // Format as YYYY-MM-DD for comparison
      const dateStr = this.formatDateForApi(date);

      // Check if this date is NOT in our available dates array
      return !this.datesWithAvailability.includes(dateStr);
    },

    isDateSelected(day) {
      if (!this.selectedDateObj) return false;

      return this.selectedDateObj.getFullYear() === this.currentYear &&
             this.selectedDateObj.getMonth() === this.currentMonth &&
             this.selectedDateObj.getDate() === day;
    },

    hasAvailableAppointments(day) {
      // Check if the date is available for booking
      const date = new Date(this.currentYear, this.currentMonth, day);

      // Don't show availability for past dates
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (date < today) return false;

      // Format as YYYY-MM-DD for comparison
      const dateStr = this.formatDateForApi(date);

      // Check if this date is in our available dates array
      return this.datesWithAvailability.includes(dateStr);
    },

    isToday(day) {
      const today = new Date();
      return today.getFullYear() === this.currentYear &&
             today.getMonth() === this.currentMonth &&
             today.getDate() === day;
    },

    isFutureDate(day) {
      const date = new Date(this.currentYear, this.currentMonth, day);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return date > today;
    },

    // API Interactions
    async loadAvailableDays() {
      if (!this.clinicId) {
        console.log('Missing clinic_id for loading available days');
        return;
      }

      // If doctor_id is not available, we'll use a default approach
      const doctorId = this.doctorId || (this.bookingData.services && this.bookingData.services.length > 0 ?
        this.bookingData.services[0].doctor_id : null);

      if (!doctorId) {
        console.log('No doctor_id available, will use default availability');
        // Set default availability (all weekdays)
        this.availableWeekdays = [0, 1, 2, 3, 4, 5, 6];
        this.processAvailableDates();
        return;
      }

      try {
        this.isLoading = true;

        // Get the available days for this doctor and clinic
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = window.ajaxData && window.ajaxData.get_nonce ? window.ajaxData.get_nonce : '';

        const params = {
          action: 'ajax_get',
          route_name: 'get_doctor_workdays',
          clinic_id: this.clinicId,
          doctor_id: doctorId,
          type: 'flatpicker',
          _ajax_nonce: nonce
        };

        console.log('Fetching doctor workdays with params:', params);

        const response = await axios.get(ajaxurl, { params });
        console.log('Doctor workdays response:', response.data);

        if (response.data && response.data.status === true) {
          // Store workdays (days of week 0-6 where the doctor works)
          this.availableWeekdays = response.data.data || [];

          // Store holidays
          this.clinicHolidays = response.data.holiday || [];

          // Process the first available date
          this.processAvailableDates();
        } else {
          console.error('Failed to get doctor workdays:', response.data ? response.data.message : 'Unknown error');
        }

      } catch (error) {
        console.error('Error loading available days:', error);
      } finally {
        this.isLoading = false;
      }
    },

    processAvailableDates() {
      console.log('Processing available dates with workdays:', this.availableWeekdays);

      // Clear existing dates
      this.datesWithAvailability = [];

      // Convert holidays to a set of dates for quick lookup
      const holidayDates = new Set();
      if (Array.isArray(this.clinicHolidays)) {
        this.clinicHolidays.forEach(holiday => {
          const startDate = new Date(holiday.start_date);
          const endDate = new Date(holiday.end_date);

          // Add all dates between start and end
          for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            holidayDates.add(d.toISOString().split('T')[0]);
          }
        });
      }

      // Special handling for empty availableWeekdays - assume all weekdays are available
      // This is important as the API sometimes doesn't return workdays correctly
      if (!this.availableWeekdays || this.availableWeekdays.length === 0) {
        console.log('No available weekdays found, enabling all days');
        this.availableWeekdays = [0, 1, 2, 3, 4, 5, 6]; // All days of the week
      }

      // Check if all days of the week are available
      if ([0, 1, 2, 3, 4, 5, 6].every(day => this.availableWeekdays.includes(day))) {
        console.log('All days of the week are available');

        // Get dates for the next 3 months
        const today = new Date();
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 3);

        // Check each date
        for (let d = new Date(today); d <= endDate; d.setDate(d.getDate() + 1)) {
          const dateStr = d.toISOString().split('T')[0];

          // Add if it's not a holiday
          if (!holidayDates.has(dateStr)) {
            this.datesWithAvailability.push(dateStr);
          }
        }
      } else {
        // Get dates for the next 3 months
        const today = new Date();
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 3);

        // Check each date
        for (let d = new Date(today); d <= endDate; d.setDate(d.getDate() + 1)) {
          const dateStr = d.toISOString().split('T')[0];
          const dayOfWeek = d.getDay(); // 0-6

          // Add if it's a workday and not a holiday
          if (this.availableWeekdays.includes(dayOfWeek) && !holidayDates.has(dateStr)) {
            this.datesWithAvailability.push(dateStr);
          }
        }
      }

      console.log('Available dates processed:', this.datesWithAvailability.length, 'dates available');

      // Find and select the first available date
      if (this.datesWithAvailability.length > 0) {
        const firstDate = new Date(this.datesWithAvailability[0]);
        this.currentMonth = firstDate.getMonth();
        this.currentYear = firstDate.getFullYear();
        this.selectedDateObj = firstDate;

        // Immediately fetch time slots for the first available date
        this.fetchTimeSlots(firstDate);
      } else {
        console.warn('No available dates found');
      }
    },

    async fetchTimeSlots(date) {
      if (!date) {
        console.log('Missing date for fetching time slots');
        this.timeSlots = []; // Clear slots on error
        this.isLoadingSlots = false;
        return;
      }

      // Get doctor_id and clinic_id from the booking data
      const doctorId = this.bookingData.doctor ? this.bookingData.doctor.id : null;
      const clinicId = this.bookingData.clinic ? this.bookingData.clinic.id : null;

      if (!doctorId || !clinicId) {
        console.log('Missing doctor_id or clinic_id for fetching time slots');
        this.timeSlots = []; // Clear slots on error
        this.isLoadingSlots = false;
        return;
      }

      try {
        this.isLoadingSlots = true;
        this.timeSlots = []; // Clear existing slots

        const formattedDate = this.formatDateForApi(date);
        console.log(`Fetching time slots for date ${formattedDate}`);

        // Get the admin-ajax.php URL and nonce
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = window.ajaxData && window.ajaxData.get_nonce ? window.ajaxData.get_nonce : '';

        // Build parameters exactly as specified
        const params = {
          action: 'ajax_get',
          route_name: 'get_time_slots',
          doctor_id: doctorId,
          clinic_id: clinicId,
          date: formattedDate,
          widgetType: 'phpWidget',
          _ajax_nonce: nonce
        };

        // Add service parameters - This is CRITICAL for the API to work correctly
        if (this.bookingData.services && this.bookingData.services.length > 0) {
          // Format services exactly as expected by the API
          const services = this.bookingData.services.map(service => ({
            id: service.id,
            service_id: service.service_id || service.id,
            name: service.name,
            charges: service.charges || service.price || '$0'
          }));

          // Log the service data for debugging
          console.log('Service data for API:', services);

          // Add service parameters in the format expected by the API
          services.forEach((service, index) => {
            params[`service[${index}][id]`] = service.id;
            params[`service[${index}][service_id]`] = service.service_id;
            params[`service[${index}][name]`] = service.name;
            params[`service[${index}][charges]`] = service.charges;
          });
        } else {
          console.error('No services found in booking data');
          this.isLoadingSlots = false;
          return;
        }

        console.log('Time slots request params:', params);

        // Make the API request
        console.log('Making API request to:', ajaxurl);
        const response = await axios.get(ajaxurl, { params });

        // Log the raw response for debugging
        console.log('Raw API response:', response);
        console.log('Time slots response:', response.data);

        // Process the time slots from the response
        let slots = [];

        if (response.data && response.data.status === true) {
          console.log('Processing time slots from response:', response.data);

          // First priority: HTML fallback - This is our safest option as the HTML is always included
          if (response.data.html) {
            console.log('Parsing HTML fallback for time slots');

            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = response.data.html;

            // Find all time slot elements
            const timeSlotElements = tempDiv.querySelectorAll('.time-slot');
            const htmlSlots = [];

            timeSlotElements.forEach(element => {
              const timeValue = element.getAttribute('data-time');
              if (timeValue) {
                htmlSlots.push({
                  time: timeValue,
                  end_time: null
                });
              }
            });

            // If no time-slot elements found, try the regex approach
            if (htmlSlots.length === 0) {
              const timeRegex = /data-time="([^"]+)"/g;
              let match;
              while ((match = timeRegex.exec(response.data.html)) !== null) {
                htmlSlots.push({
                  time: match[1],
                  end_time: null
                });
              }
            }

            if (htmlSlots.length > 0) {
              console.log('Successfully extracted', htmlSlots.length, 'slots from HTML');
              slots = htmlSlots;
            }
          }

          // If no slots yet, try other formats
          if (slots.length === 0) {
            // New format: Array of arrays with time objects (as seen in your example)
            if (Array.isArray(response.data.data) && response.data.data.length > 0 && Array.isArray(response.data.data[0])) {
              // Flatten the nested array and extract time values
              slots = response.data.data.flat().map(slot => ({
                time: slot.time,
                end_time: null,
                available: slot.available
              }));
              console.log('Extracted slots from nested array format:', slots);
            }
            // Format 1: Array of objects with start_time in data field
            else if (Array.isArray(response.data.data) && response.data.data.length > 0) {
              if (response.data.data[0].start_time) {
                slots = response.data.data.map(slot => ({
                  time: slot.start_time,
                  end_time: slot.end_time || null,
                  id: slot.id || null
                }));
              }
              // Format 2: Array of time strings in data field
              else if (typeof response.data.data[0] === 'string') {
                slots = response.data.data.map(time => ({
                  time: time,
                  end_time: null
                }));
              }
            }
            // Format 3: Data object with slots array
            else if (response.data.data && response.data.data.slots && Array.isArray(response.data.data.slots)) {
              slots = response.data.data.slots.map(slot => ({
                time: typeof slot === 'string' ? slot : (slot.start_time || slot.time),
                end_time: slot.end_time || null,
                id: slot.id || null
              }));
            }
            // Format 4: Direct slots array in response
            else if (response.data.slots && Array.isArray(response.data.slots)) {
              slots = response.data.slots.map(slot => ({
                time: typeof slot === 'string' ? slot : (slot.start_time || slot.time),
                end_time: slot.end_time || null,
                id: slot.id || null
              }));
            }
          }
        } else {
          console.error('Failed to get time slots:', response.data ? response.data.message : 'Unknown error');

          // No fallback time slots for production
        }

        // Set the time slots and sort by time
        this.timeSlots = slots.sort((a, b) => {
          return this.parseTime(a.time) - this.parseTime(b.time);
        });

        // Log time slots for debugging
        console.log('Total time slots:', this.timeSlots.length);

        // Ensure the slots are properly categorized and displayed
        if (this.timeSlots.length > 0) {
          // Make a copy of the array to trigger reactivity
          this.timeSlots = [...this.timeSlots];

          // Debug time slot grouping - avoid using computed properties in debug code
          this.$nextTick(() => {
            try {
              // Simple classification for debug purposes only
              const morning = this.timeSlots.filter(slot => {
                if (!slot || !slot.time) return false;
                const timeStr = String(slot.time);
                return timeStr.toLowerCase().includes('am');
              });

              const afternoon = this.timeSlots.filter(slot => {
                if (!slot || !slot.time) return false;
                const timeStr = String(slot.time);
                if (timeStr.toLowerCase().includes('pm')) {
                  const hourMatch = timeStr.match(/(\d+):/);
                  if (hourMatch && hourMatch[1]) {
                    const hour = parseInt(hourMatch[1]);
                    return hour <= 4 || hour === 12; // 12pm-4pm
                  }
                }
                return false;
              });

              const evening = this.timeSlots.filter(slot => {
                if (!slot || !slot.time) return false;
                const timeStr = String(slot.time);
                if (timeStr.toLowerCase().includes('pm')) {
                  const hourMatch = timeStr.match(/(\d+):/);
                  if (hourMatch && hourMatch[1]) {
                    const hour = parseInt(hourMatch[1]);
                    return hour >= 5 && hour <= 11; // 5pm-11pm
                  }
                }
                return false;
              });

              console.log('Debug morning slots:', morning.length, morning.map(s => s.time));
              console.log('Debug afternoon slots:', afternoon.length, afternoon.map(s => s.time));
              console.log('Debug evening slots:', evening.length, evening.map(s => s.time));
            } catch (error) {
              console.error('Error in time slot debugging:', error);
            }
          });

          // If a time slot was previously selected but is no longer available, clear it
          if (this.selectedTime && !this.timeSlots.some(slot => slot.time === this.selectedTime)) {
            this.selectedTime = null;
          }
        } else {
          console.warn('No time slots found for the selected date');
        }

      } catch (error) {
        console.error('Error fetching time slots:', error);
        this.timeSlots = []; // Clear slots on error

        // No fallback time slots for production
      } finally {
        // Ensure loading state is always reset
        this.isLoadingSlots = false;
      }
    },

    // UI Interactions
    selectTimeSlot(time) {
      this.selectedTime = time;

      // Let the parent component know that we're moving to the next step
      // This is similar to the original implementation which uses jQuery to trigger a click
      this.$nextTick(() => {
        this.$emit('time-selected', {
          date: this.formatDateForApi(this.selectedDateObj),
          time: time
        });

        // Removed auto-advance - user must click Next button
      });
    },

    resetSelection() {
      this.selectedTime = null;

      // If we're using this in multi-step form, we might want to reset additional data
      this.$emit('reset-time');
    },

    // Formatting Helpers
    formatSelectedDate(date) {
      if (!date) return '';

      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      return date.toLocaleDateString(undefined, options);
    },

    formatDateForApi(date) {
      if (!date) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },

    formatTime(timeString) {
      if (!timeString) return '';

      try {
        // Make sure timeString is a string
        const timeStr = String(timeString);

        // If the time is already in 12-hour format with am/pm, just capitalize AM/PM
        if (timeStr.toLowerCase().includes('am')) {
          return timeStr.replace(/am/i, 'AM').replace(' AM', ' AM');
        }
        if (timeStr.toLowerCase().includes('pm')) {
          return timeStr.replace(/pm/i, 'PM').replace(' PM', ' PM');
        }

        // Parse 24-hour format like "14:30:00" to display as "2:30 PM"
        if (timeStr.includes(':')) {
          const parts = timeStr.split(':');
          const hour = parseInt(parts[0], 10) || 0;
          const min = parts[1] || '00';
          const ampm = hour >= 12 ? 'PM' : 'AM';
          const hour12 = hour % 12 || 12;

          return `${hour12}:${min} ${ampm}`;
        }

        // If none of the formats match, just return the original
        return timeStr;
      } catch (error) {
        console.error('Error formatting time:', timeString, error);
        return String(timeString); // Return as string as a fallback
      }
    },

    parseTime(timeString) {
      // Parse a time string to minutes since midnight
      // Handles both "14:30:00" format and "2:30 pm" format
      if (!timeString) return 0;

      // Make sure timeString is a string
      const timeStr = String(timeString);

      let hours = 0, minutes = 0;

      try {
        // Check if the time string is in 12-hour format with am/pm
        if (timeStr.toLowerCase().includes('am') || timeStr.toLowerCase().includes('pm')) {
          // Format: "2:30 pm" or "10:00 am"
          const isPM = timeStr.toLowerCase().includes('pm');

          // Extract hours and minutes, removing the am/pm part
          const timeParts = timeStr.toLowerCase()
            .replace('am', '')
            .replace('pm', '')
            .trim()
            .split(':');

          hours = parseInt(timeParts[0], 10) || 0;
          minutes = parseInt(timeParts[1], 10) || 0;

          // Convert to 24-hour format if PM and not 12
          if (isPM && hours !== 12) {
            hours += 12;
          }
          // Handle 12 AM as 0 hours
          if (!isPM && hours === 12) {
            hours = 0;
          }
        } else if (timeStr.includes(':')) {
          // Handle 24-hour format like "14:30:00"
          const parts = timeStr.split(':');
          hours = parseInt(parts[0], 10) || 0;
          minutes = parseInt(parts[1], 10) || 0;
        } else {
          // Just in case we get a number or other invalid format
          console.warn('Invalid time format:', timeStr);
          return 0;
        }

        return hours * 60 + minutes;
      } catch (error) {
        console.error('Error parsing time:', timeStr, error);
        return 0;
      }
    }
  }
};
</script>

<style scoped>
:root {
  --primary-color: #4f46e5;
  --primary-light: rgba(79, 70, 229, 0.1);
  --primary-lighter: rgba(79, 70, 229, 0.05);
  --white: #ffffff;
  --light-gray: #f3f4f6;
  --gray: #6b7280;
  --dark-gray: #374151;
  --black: #111827;
  --danger: #ef4444;
  --success: #10b981;
  --warning: #f59e0b;
  --radius: 0.375rem;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --transition: all 0.2s ease;
}

/* Step Header Styles */
.kivi-step-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 1rem;
  color: var(--gray);
  margin-bottom: 2rem;
}

/* Main Container Layout */
.kivi-date-time-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .kivi-date-time-container {
    grid-template-columns: 1fr 1fr;
  }
}

/* Section Headers */
.kivi-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

/* Calendar Section Styles */
.kivi-date-section {
  background-color: var(--white);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.kivi-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.kivi-current-month {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--black);
}

.kivi-calendar-nav-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
}

.kivi-calendar-nav-btn:hover {
  background-color: var(--primary-lighter);
  border-color: var(--primary-color);
}

.kivi-calendar-nav-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.kivi-calendar-nav-btn svg {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--dark-gray);
}

/* Weekday Header Styles */
.kivi-calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 0.5rem;
}

.kivi-weekday {
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 0;
  color: var(--dark-gray);
}

/* Calendar Days */
.kivi-calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
}

.kivi-calendar-day {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.5rem;
  font-size: 0.875rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.kivi-calendar-day:not(.empty):hover {
  background-color: var(--primary-lighter);
}

.kivi-calendar-day.empty {
  cursor: default;
}

.kivi-calendar-day.today {
  border: 1px dashed var(--primary-color);
}

.kivi-calendar-day.selected {
  background-color: var(--primary-color);
  color: var(--white);
}

.kivi-calendar-day.unavailable {
  color: var(--gray);
  text-decoration: line-through;
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none; /* Prevent click events on unavailable dates */
}

.kivi-calendar-day.has-appointments::after {
  content: '';
  position: absolute;
  bottom: 0.25rem;
  width: 0.375rem;
  height: 0.375rem;
  background-color: var(--success);
  border-radius: 50%;
}

.kivi-availability-indicator {
  position: absolute;
  bottom: 0.25rem;
  width: 0.375rem;
  height: 0.375rem;
}

.kivi-availability-icon {
  width: 0.375rem;
  height: 0.375rem;
  color: var(--success);
}

/* Calendar Legend */
.kivi-calendar-legend {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--light-gray);
}

.kivi-legend-item {
  display: flex;
  align-items: center;
  margin: 0 0.5rem;
}

.kivi-legend-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.375rem;
}

.kivi-legend-indicator.available {
  background-color: var(--success);
}

.kivi-legend-indicator.unavailable {
  background-color: var(--gray);
  opacity: 0.5;
}

.kivi-legend-indicator.selected {
  background-color: var(--primary-color);
}

.kivi-legend-text {
  font-size: 0.75rem;
  color: var(--gray);
}

/* Time Section Styles */
.kivi-time-section {
  background-color: var(--white);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.kivi-selected-date {
  background-color: var(--primary-lighter);
  padding: 0.75rem;
  border-radius: var(--radius);
  margin-bottom: 1rem;
  font-weight: 500;
  color: var(--primary-color);
  text-align: center;
}

.kivi-time-slot-groups {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.kivi-time-group-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.75rem;
}

.kivi-time-group-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.kivi-time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(6rem, 1fr));
  gap: 0.5rem;
}

.kivi-time-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--radius);
  cursor: pointer;
  transition: var(--transition);
}

.kivi-time-slot:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-lighter);
}

.kivi-time-slot.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: var(--white);
}

.kivi-time-slot-time {
  font-size: 0.875rem;
}

/* Empty States and Prompts */
.kivi-prompt-select-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: var(--gray);
}

.kivi-prompt-icon {
  width: 3rem;
  height: 3rem;
  color: var(--gray);
  opacity: 0.5;
  margin-bottom: 1rem;
}

.kivi-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  color: var(--gray);
  background-color: var(--light-gray);
  border-radius: var(--radius);
  text-align: center;
}

.kivi-empty-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: var(--gray);
  margin-bottom: 1rem;
}

/* Selection Summary */
.kivi-selection-summary {
  background-color: var(--primary-lighter);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin-top: 2rem;
  animation: fadeIn 0.3s ease;
}

.kivi-summary-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.kivi-summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kivi-summary-item {
  display: flex;
  align-items: center;
}

.kivi-summary-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  color: var(--primary-color);
}

.kivi-reset-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  color: var(--primary-color);
  font-weight: 500;
  padding: 0.5rem;
  margin-top: 1rem;
  cursor: pointer;
  transition: var(--transition);
}

.kivi-reset-selection:hover {
  text-decoration: underline;
}

.kivi-reset-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

/* Loading States */
.kivi-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 0.3);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(1rem); }
  to { opacity: 1; transform: translateY(0); }
}
</style>