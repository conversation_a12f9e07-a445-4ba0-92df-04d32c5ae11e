<?php


namespace App\models;

use App\baseClasses\KCModel;

class KCService extends KCModel {

	public function __construct() {
		parent::__construct('services');
	}

	/**
	 * Get services with category information
	 */
	public function getServicesWithCategory($filters = []) {
		global $wpdb;

		$services_table = $wpdb->prefix . 'kc_services';
		$categories_table = $wpdb->prefix . 'kc_categories';

		$where_conditions = ['s.status = 1'];
		$params = [];

		// Add filters
		if (!empty($filters['category_id'])) {
			$where_conditions[] = 's.category_id = %d';
			$params[] = $filters['category_id'];
		}

		if (!empty($filters['visibility'])) {
			if (is_array($filters['visibility'])) {
				$placeholders = implode(',', array_fill(0, count($filters['visibility']), '%s'));
				$where_conditions[] = "s.visibility IN ($placeholders)";
				$params = array_merge($params, $filters['visibility']);
			} else {
				$where_conditions[] = 's.visibility = %s';
				$params[] = $filters['visibility'];
			}
		}

		// Add doctor/clinic filters if provided
		if (!empty($filters['doctor_id'])) {
			$where_conditions[] = 's.doctor_id = %d';
			$params[] = $filters['doctor_id'];
		}

		if (!empty($filters['clinic_id'])) {
			$where_conditions[] = 's.clinic_id = %d';
			$params[] = $filters['clinic_id'];
		}

		$where_clause = implode(' AND ', $where_conditions);

		$query = "
			SELECT
				s.*,
				c.name as category_name,
				c.slug as category_slug,
				c.visibility as category_visibility,
				s.charges as service_charges,
				s.service_name_alias,
				s.description as service_description
			FROM {$services_table} s
			LEFT JOIN {$categories_table} c ON s.category_id = c.id
			WHERE {$where_clause}
			ORDER BY s.sort_order ASC, s.name ASC
		";

		if (!empty($params)) {
			$query = $wpdb->prepare($query, $params);
		}

		return $wpdb->get_results($query);
	}

	/**
	 * Get public services (for frontend)
	 */
	public function getPublicServices($category_id = null) {
		$filters = ['visibility' => 'public'];
		if ($category_id) {
			$filters['category_id'] = $category_id;
		}
		return $this->getServicesWithCategory($filters);
	}

	/**
	 * Get backend services (public + backend_only)
	 */
	public function getBackendServices($category_id = null) {
		$filters = ['visibility' => ['public', 'backend_only']];
		if ($category_id) {
			$filters['category_id'] = $category_id;
		}
		return $this->getServicesWithCategory($filters);
	}

	/**
	 * Get services by clinic with category information
	 */
	public function getServicesByClinic($clinic_id, $visibility_filter = ['public']) {
		global $wpdb;

		$services_table = $wpdb->prefix . 'kc_services';
		$categories_table = $wpdb->prefix . 'kc_categories';

		$visibility_placeholders = implode(',', array_fill(0, count($visibility_filter), '%s'));

		$params = array_merge([$clinic_id], $visibility_filter);

		$query = $wpdb->prepare("
			SELECT
				s.*,
				c.name as category_name,
				c.slug as category_slug,
				c.visibility as category_visibility,
				s.charges,
				s.doctor_id,
				s.service_name_alias,
				s.description as service_description,
				s.duration,
				s.telemed_service
			FROM {$services_table} s
			LEFT JOIN {$categories_table} c ON s.category_id = c.id
			WHERE s.clinic_id = %d
			AND s.status = 1
			AND s.doctor_id > 0
			AND s.visibility IN ({$visibility_placeholders})
			ORDER BY s.sort_order ASC, s.name ASC
		", $params);

		return $wpdb->get_results($query);
	}

	/**
	 * Get services by doctor and clinic
	 */
	public function getServicesByDoctorAndClinic($doctor_id, $clinic_id, $visibility_filter = ['public']) {
		$filters = [
			'doctor_id' => $doctor_id,
			'clinic_id' => $clinic_id,
			'visibility' => $visibility_filter
		];

		return $this->getServicesWithCategory($filters);
	}

	/**
	 * Get service by ID with all details
	 */
	public function getServiceById($service_id) {
		global $wpdb;

		$services_table = $wpdb->prefix . 'kc_services';
		$categories_table = $wpdb->prefix . 'kc_categories';

		$query = $wpdb->prepare("
			SELECT
				s.*,
				c.name as category_name,
				c.slug as category_slug,
				c.visibility as category_visibility
			FROM {$services_table} s
			LEFT JOIN {$categories_table} c ON s.category_id = c.id
			WHERE s.id = %d
		", $service_id);

		return $wpdb->get_row($query);
	}

	/**
	 * Get all services for a specific doctor across all clinics
	 */
	public function getServicesByDoctor($doctor_id) {
		global $wpdb;

		$services_table = $wpdb->prefix . 'kc_services';
		$categories_table = $wpdb->prefix . 'kc_categories';

		$query = $wpdb->prepare("
			SELECT
				s.*,
				c.name as category_name,
				c.slug as category_slug,
				c.visibility as category_visibility
			FROM {$services_table} s
			LEFT JOIN {$categories_table} c ON s.category_id = c.id
			WHERE s.doctor_id = %d
			AND s.status = 1
			ORDER BY s.sort_order ASC, s.name ASC
		", $doctor_id);

		return $wpdb->get_results($query);
	}

	/**
	 * Check if service exists for specific doctor/clinic combination
	 */
	public function serviceExistsForDoctorClinic($service_name, $doctor_id, $clinic_id) {
		global $wpdb;

		$services_table = $wpdb->prefix . 'kc_services';

		$query = $wpdb->prepare("
			SELECT id FROM {$services_table}
			WHERE name = %s
			AND doctor_id = %d
			AND clinic_id = %d
			AND status = 1
		", $service_name, $doctor_id, $clinic_id);

		return $wpdb->get_var($query);
	}

}

