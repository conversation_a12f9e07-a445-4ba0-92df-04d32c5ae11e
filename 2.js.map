{"version": 3, "sources": ["webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/@babel/runtime/helpers/asyncToGenerator.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/@babel/runtime/helpers/defineProperty.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/@babel/runtime/helpers/toPrimitive.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/@babel/runtime/helpers/toPropertyKey.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/canvg/lib/index.es.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/a-callable.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/a-constructor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/a-possible-prototype.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/add-to-unscopables.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/advance-string-index.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/an-instance.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/an-object.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/array-includes.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/array-reduce.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/array-slice.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/check-correctness-of-iteration.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/classof-raw.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/classof.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/copy-constructor-properties.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/correct-is-regexp-logic.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/correct-prototype-getter.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/create-iter-result-object.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/create-non-enumerable-property.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/create-property-descriptor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/define-built-in-accessor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/define-built-in.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/define-global-property.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/descriptors.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/document-create-element.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/dom-iterables.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/dom-token-list-prototype.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/enum-bug-keys.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment-is-ios-pebble.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment-is-ios.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment-is-node.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment-is-webos-webkit.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment-user-agent.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment-v8-version.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/environment.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/export.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/fails.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/fix-regexp-well-known-symbol-logic.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-apply.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-bind-context.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-bind-native.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-call.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-name.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-uncurry-this-accessor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-uncurry-this-clause.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/function-uncurry-this.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/get-built-in.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/get-iterator-method.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/get-iterator.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/get-method.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/get-substitution.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/global-this.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/has-own-property.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/hidden-keys.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/host-report-errors.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/html.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/ie8-dom-define.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/indexed-object.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/inspect-source.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/internal-state.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-array-iterator-method.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-array.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-callable.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-constructor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-forced.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-null-or-undefined.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-object.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-possible-prototype.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-pure.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-regexp.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/is-symbol.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/iterate.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/iterator-close.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/iterator-create-constructor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/iterator-define.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/iterators-core.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/iterators.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/length-of-array-like.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/make-built-in.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/math-trunc.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/microtask.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/new-promise-capability.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/not-a-regexp.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-create.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-define-properties.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-define-property.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-get-own-property-descriptor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-get-own-property-names.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-get-own-property-symbols.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-get-prototype-of.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-is-prototype-of.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-keys-internal.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-keys.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-property-is-enumerable.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/object-set-prototype-of.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/ordinary-to-primitive.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/own-keys.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/perform.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/promise-constructor-detection.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/promise-native-constructor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/promise-resolve.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/promise-statics-incorrect-iteration.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/queue.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-exec-abstract.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-exec.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-flags.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-get-flags.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-sticky-helpers.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-unsupported-dot-all.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/regexp-unsupported-ncg.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/require-object-coercible.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/safe-get-built-in.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/set-species.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/set-to-string-tag.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/shared-key.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/shared-store.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/shared.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/species-constructor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/string-multibyte.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/string-trim-forced.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/string-trim.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/symbol-constructor-detection.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/task.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-absolute-index.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-indexed-object.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-integer-or-infinity.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-length.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-object.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-primitive.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-property-key.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-string-tag-support.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/to-string.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/try-to-string.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/uid.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/use-symbol-as-uid.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/v8-prototype-define-bug.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/validate-arguments-length.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/weak-map-basic-detection.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/well-known-symbol.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/internals/whitespaces.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.array.index-of.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.array.iterator.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.array.reduce.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.array.reverse.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.all.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.catch.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.constructor.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.race.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.reject.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.promise.resolve.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.regexp.exec.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.regexp.to-string.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.ends-with.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.includes.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.match.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.replace.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.split.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.starts-with.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/es.string.trim.js", "webpack:///./node_modules/html2pdf.js/node_modules/jspdf/node_modules/core-js/modules/web.dom-collections.iterator.js"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,iH;;;;;;;;;;;ACzBA,oBAAoB,mBAAO,CAAC,8HAAoB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,+G;;;;;;;;;;;ACTA,cAAc,mBAAO,CAAC,gHAAa;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2G;;;;;;;;;;;ACXA,cAAc,mBAAO,CAAC,gHAAa;AACnC,kBAAkB,mBAAO,CAAC,0HAAkB;AAC5C;AACA;AACA;AACA;AACA,6G;;;;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuC;AACiC;AAC5B;AACE;AACI;AACJ;AACW;AACW;AACxB;AACI;AACJ;AACJ;AACG;AACX;AACc;AACC;AACF;AACF;AACK;AACF;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA,aAAa,8EAAiB;AAC9B;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,mFAAmF;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E;;AAE9E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB;AAChB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,mBAAmB;;AAEnB,mBAAmB,SAAS;AAC5B;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB,gDAAQ;;AAE9B;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,mBAAmB,SAAS;AAC5B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,4BAA4B;;AAE5B,2CAA2C;;AAE3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA,KAAK,EAAE;;AAEP;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,+EAA+E;;AAE/E;AACA;AACA,uDAAuD;;AAEvD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,wBAAwB,2CAAqB;AAC7C;;AAEA;AACA;AACA;;AAEA,sBAAsB,2CAAqB;AAC3C;;AAEA;AACA;AACA,MAAM,2CAAqB;AAC3B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA,WAAW,8EAAiB;AAC5B;AACA;AACA;;AAEA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,WAAW,8EAAiB;AAC5B;AACA;AACA;AACA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;;AAE5C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA,mBAAmB,SAAS;AAC5B;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA,yBAAyB,QAAQ;AACjC;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA,KAAK;AACL;;AAEA,mBAAmB,SAAS;AAC5B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA,KAAK;AACL,wCAAwC;;AAExC;AACA,kEAAkE;AAClE;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA,KAAK;AACL,qCAAqC;;AAErC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC;AACjC,OAAO;AACP;;AAEA;AACA,kCAAkC;AAClC;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA,+BAA+B;;AAE/B;AACA;;AAEA;AACA;AACA;AACA,OAAO;;AAEP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;;AAEA,iBAAiB;AACjB;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB;AACnB;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,yCAAyC;;AAEzC,yCAAyC;;AAEzC,0CAA0C;;AAE1C,0CAA0C;;AAE1C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA,yBAAyB,yDAAW;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,4BAA4B,yDAAW,iCAAiC,yDAAW,wCAAwC,yDAAW,gCAAgC,yDAAW;AACjL;AACA,KAAK;;;AAGL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK,QAAQ;;AAEb;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA,mBAAmB,SAAS;AAC5B;AACA,2BAA2B,SAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,sBAAsB;;AAEtB;;AAEA;AACA,kEAAkE;;AAElE;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,OAAO;;;AAGP;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,oEAAoE;AACpE;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,6BAA6B;;AAE7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,uBAAuB,sBAAsB;AAC7C;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,sDAAsD;AACtD;AACA;;AAEA,qRAAqR;;AAErR;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;AACA;;AAEA,uEAAuE;;AAEvE,0OAA0O;;AAE1O,oFAAoF;AACpF;;AAEA;AACA;AACA,gCAAgC;;AAEhC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,iCAAiC;;AAEtC;AACA;AACA;AACA;AACA;AACA,yDAAyD;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA,2BAA2B;;AAE3B;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;AACA,8CAA8C;;AAE9C;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK,wBAAwB;;AAE7B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK,QAAQ;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;;AAER;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;;AAGA;AACA;AACA,sBAAsB;;AAEtB;;AAEA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA;;AAEA,qCAAqC,2BAA2B;AAChE;AACA,KAAK;;;AAGL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA,2BAA2B;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mCAAmC;;AAEnC;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,0CAA0C,kBAAkB;AAC5D;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,wBAAwB;;AAExB;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8CAA8C;AAC9C;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,sBAAsB;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,8DAA8D;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA,KAAK;;;AAGL,oBAAoB,QAAQ;AAC5B,sBAAsB,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,eAAe;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,YAAY;AACZ;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0DAA0D;AAC1D;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK,sBAAsB;;AAE3B;;AAEA;AACA,wBAAwB;AACxB;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,kCAAkC;;AAElC;AACA;AACA;AACA,KAAK;;;AAGL;AACA,+DAA+D;;AAE/D;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;;AAEA,2BAA2B;;AAE3B;;AAEA;AACA,sCAAsC;;AAEtC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,wBAAwB,gDAAQ;AAChC,sBAAsB,gDAAQ;;AAE9B;AACA;AACA;AACA;AACA,iEAAiE;;AAEjE;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK,sBAAsB;;AAE3B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,YAAY;AACZ;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP,gGAAgG;;AAEhG;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,4CAA4C,gCAAgC,oCAAoC,oDAAoD,sBAAsB,0CAA0C,gEAAgE,EAAE,EAAE,EAAE,gCAAgC,EAAE,aAAa;;AAEzV,kCAAkC,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,yDAAyD,CAAC,4EAAe,2BAA2B,EAAE,EAAE,EAAE,6CAA6C,2EAA2E,EAAE,OAAO,mDAAmD,kFAAkF,EAAE,EAAE,EAAE,EAAE,eAAe;AAC1hB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qDAAqD,SAAS;AAC9D;AACA;AACA,OAAO;AACP,qDAAqD,SAAS;AAC9D;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;;AAEA,mBAAmB,aAAa;AAChC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;;AAEA;AACA,yBAAyB;;AAEzB;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,uBAAuB;;AAEvB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,sBAAsB,QAAQ;AAC9B;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA,sBAAsB,QAAQ;AAC9B;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,gCAAgC;;AAEhC,iCAAiC;;AAEjC;AACA,oCAAoC;;AAEpC;AACA;AACA,WAAW;;;AAGX;;AAEA;AACA;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,0CAA0C;;AAE1C,2CAA2C;;AAE3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA,2CAA2C;;AAE3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;;AAER;;AAEA,qBAAqB,cAAc;AACnC;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,oCAAoC,SAAS,OAAO,KAAK,QAAQ,YAAY;AAC7E;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,WAAW,8EAAiB;AAC5B;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA,KAAK;AACL;;AAEA;AACA;;AAEA,WAAW,8EAAiB;AAC5B;;AAEA;AACA;;AAEA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;;AAEA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,aAAa;AACb;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,WAAW,8EAAiB;AAC5B;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;;AAEA;AACA,KAAK;AACL;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,8BAA8B;AAC9B;AACA;;AAEA;AACA;AACA;;AAEA,iCAAiC;AACjC;AACA,yCAAyC;AACzC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;;AAE5C;AACA;;AAEA;AACA;AACA,+DAA+D;;AAE/D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA,mBAAmB,YAAY;AAC/B,qBAAqB,WAAW;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK,QAAQ;;AAEb;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;;AAE9C;AACA;;AAEA,aAAa;AACb;;AAEA;AACA;;AAEA,kBAAkB;AAClB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,sGAAsG;;AAEtG;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,aAAa;AACb;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;;AAE3B;AACA;AACA;AACA;AACA,KAAK,EAAE;;AAEP;AACA;AACA;;AAEA,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;AACrC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;AACrC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;AACrC;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,4BAA4B;;AAE5B;;AAEA;AACA;AACA;AACA;;AAEA,IAAI,oEAAU;;AAEd;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,4CAA4C,gCAAgC,oCAAoC,oDAAoD,sBAAsB,0CAA0C,gEAAgE,EAAE,EAAE,EAAE,gCAAgC,EAAE,aAAa;;AAEzV,kCAAkC,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,yDAAyD,CAAC,4EAAe,2BAA2B,EAAE,EAAE,EAAE,6CAA6C,2EAA2E,EAAE,OAAO,mDAAmD,kFAAkF,EAAE,EAAE,EAAE,EAAE,eAAe;;AAE1hB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,8EAAiB;AAClC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;AACL,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA,0CAA0C,gCAAgC,oCAAoC,oDAAoD,sBAAsB,0CAA0C,gEAAgE,EAAE,EAAE,EAAE,gCAAgC,EAAE,aAAa;;AAEvV,gCAAgC,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,uDAAuD,CAAC,4EAAe,2BAA2B,EAAE,EAAE,EAAE,6CAA6C,2EAA2E,EAAE,OAAO,iDAAiD,kFAAkF,EAAE,EAAE,EAAE,EAAE,eAAe;AACphB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,WAAW,8EAAiB;AAC5B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,8DAA8D;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA,oEAAoE;AACpE;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA,WAAW,8EAAiB;AAC5B;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;;AAEA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;;AAE0oC;AAC1oC,2CAA2C,cAAc;;;;;;;;;;;;;;ACt5M5C;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,kBAAkB,mBAAO,CAAC,iIAA4B;;AAEtD;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,oBAAoB,mBAAO,CAAC,mIAA6B;AACzD,kBAAkB,mBAAO,CAAC,iIAA4B;;AAEtD;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,0BAA0B,mBAAO,CAAC,iJAAoC;;AAEtE;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,aAAa,mBAAO,CAAC,iIAA4B;AACjD,qBAAqB,mBAAO,CAAC,mJAAqC;;AAElE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACpBa;AACb,aAAa,mBAAO,CAAC,uIAA+B;;AAEpD;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACPa;AACb,oBAAoB,mBAAO,CAAC,mJAAqC;;AAEjE;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACRa;AACb,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,wBAAwB,mBAAO,CAAC,+IAAmC;;AAEnE,qBAAqB,oBAAoB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,YAAY,eAAe;AAChC;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjCa;AACb,YAAY,mBAAO,CAAC,iHAAoB;;AAExC;AACA;AACA;AACA;AACA,+CAA+C,UAAU,EAAE;AAC3D,GAAG;AACH;;;;;;;;;;;;;ACTa;AACb,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,oBAAoB,mBAAO,CAAC,mIAA6B;AACzD,wBAAwB,mBAAO,CAAC,+IAAmC;;AAEnE;;AAEA;;AAEA,qBAAqB,sBAAsB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,uCAAuC;AACjD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC7Ca;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;;AAE9D;;;;;;;;;;;;;ACHa;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,SAAS,EAAE;AACzD,CAAC,gBAAgB;;AAEjB;AACA;AACA;AACA,GAAG,gBAAgB,cAAc,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;;;;;;;;;;;;;ACxCa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;;AAE9D,6BAA6B;AAC7B;;AAEA;AACA;AACA;;;;;;;;;;;;;ACRa;AACb,4BAA4B,mBAAO,CAAC,iJAAoC;AACxE,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;AACA;;AAEA;AACA,gDAAgD,kBAAkB,EAAE;;AAEpE;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC7Ba;AACb,aAAa,mBAAO,CAAC,uIAA+B;AACpD,cAAc,mBAAO,CAAC,uHAAuB;AAC7C,qCAAqC,mBAAO,CAAC,2KAAiD;AAC9F,2BAA2B,mBAAO,CAAC,mJAAqC;;AAExE;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AChBa;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK,iBAAiB;AACtB,GAAG;AACH;;;;;;;;;;;;;ACfa;AACb,YAAY,mBAAO,CAAC,iHAAoB;;AAExC;AACA,gBAAgB;AAChB;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACRY;AACb;AACA;AACA;AACA,UAAU;AACV;;;;;;;;;;;;;ACLa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,2BAA2B,mBAAO,CAAC,mJAAqC;AACxE,+BAA+B,mBAAO,CAAC,2JAAyC;;AAEhF;AACA;AACA,CAAC;AACD;AACA;AACA;;;;;;;;;;;;;ACVa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRa;AACb,kBAAkB,mBAAO,CAAC,iIAA4B;AACtD,qBAAqB,mBAAO,CAAC,mJAAqC;;AAElE;AACA,yDAAyD,eAAe;AACxE,yDAAyD,eAAe;AACxE;AACA;;;;;;;;;;;;;ACRa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,2BAA2B,mBAAO,CAAC,mJAAqC;AACxE,kBAAkB,mBAAO,CAAC,iIAA4B;AACtD,2BAA2B,mBAAO,CAAC,mJAAqC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK,gBAAgB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;;;;;;;;;;;;;AC3Ba;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;;AAEA;AACA;AACA,qCAAqC,mDAAmD;AACxF,GAAG;AACH;AACA,GAAG;AACH;;;;;;;;;;;;;ACZa;AACb,YAAY,mBAAO,CAAC,iHAAoB;;AAExC;AACA;AACA;AACA,iCAAiC,MAAM,mBAAmB,UAAU,EAAE,EAAE;AACxE,CAAC;;;;;;;;;;;;;ACPY;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnCa;AACb;AACA,4BAA4B,mBAAO,CAAC,qJAAsC;;AAE1E;AACA;;AAEA;;;;;;;;;;;;;ACPa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,gBAAgB,mBAAO,CAAC,mJAAqC;;AAE7D;;;;;;;;;;;;;ACHa;AACb,gBAAgB,mBAAO,CAAC,mJAAqC;;AAE7D;AACA;;;;;;;;;;;;;ACJa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;;AAEpD;;;;;;;;;;;;;ACHa;AACb,gBAAgB,mBAAO,CAAC,mJAAqC;;AAE7D;;;;;;;;;;;;;ACHa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;;AAEA;;;;;;;;;;;;;ACNa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,gBAAgB,mBAAO,CAAC,mJAAqC;;AAE7D;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;AC3Ba;AACb;AACA,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,gBAAgB,mBAAO,CAAC,mJAAqC;AAC7D,cAAc,mBAAO,CAAC,6HAA0B;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACpBY;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,+BAA+B,mBAAO,CAAC,2KAAiD;AACxF,kCAAkC,mBAAO,CAAC,mKAA6C;AACvF,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,2BAA2B,mBAAO,CAAC,mJAAqC;AACxE,gCAAgC,mBAAO,CAAC,6JAA0C;AAClF,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,kEAAkE;AAClE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACtDa;AACb;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;;;;;ACPa;AACb;AACA,mBAAO,CAAC,+HAA2B;AACnC,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,YAAY,mBAAO,CAAC,iHAAoB;AACxC,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,kCAAkC,mBAAO,CAAC,mKAA6C;;AAEvF;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,6BAA6B,UAAU;AACvC;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,WAAW;AACxD;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA,gBAAgB;AAChB;AACA,cAAc;AACd,KAAK;;AAEL;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;AC3Ea;AACb,kBAAkB,mBAAO,CAAC,+IAAmC;;AAE7D;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACVY;AACb,kBAAkB,mBAAO,CAAC,+JAA2C;AACrE,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,kBAAkB,mBAAO,CAAC,+IAAmC;;AAE7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACba;AACb,YAAY,mBAAO,CAAC,iHAAoB;;AAExC;AACA;AACA,2BAA2B,cAAc;AACzC;AACA;AACA,CAAC;;;;;;;;;;;;;ACRY;AACb,kBAAkB,mBAAO,CAAC,+IAAmC;;AAE7D;;AAEA;AACA;AACA;;;;;;;;;;;;;ACPa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,aAAa,mBAAO,CAAC,uIAA+B;;AAEpD;AACA;AACA;;AAEA;AACA;AACA,8CAA8C,cAAc;AAC5D;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,gBAAgB,mBAAO,CAAC,2HAAyB;;AAEjD;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;;;;;;;;;;;;;ACTa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,kBAAkB,mBAAO,CAAC,iJAAoC;;AAE9D;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,kBAAkB,mBAAO,CAAC,+IAAmC;;AAE7D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACXa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,wBAAwB,mBAAO,CAAC,+IAAmC;AACnE,gBAAgB,mBAAO,CAAC,yHAAwB;AAChD,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACba;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,kBAAkB,mBAAO,CAAC,iIAA4B;AACtD,wBAAwB,mBAAO,CAAC,6IAAkC;;AAElE;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACba;AACb,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,wBAAwB,mBAAO,CAAC,+IAAmC;;AAEnE;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA,yCAAyC,IAAI;AAC7C,kDAAkD,IAAI;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;AC7CA,8CAAa;AACb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAa,EAAE;;;;;;;;;;;;;;ACflB;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C,mCAAmC;;AAEnC;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACXa;AACb;;;;;;;;;;;;;ACDa;AACb;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;;;;;;;;;;;;;ACNa;AACb,iBAAiB,mBAAO,CAAC,+HAA2B;;AAEpD;;;;;;;;;;;;;ACHa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,YAAY,mBAAO,CAAC,iHAAoB;AACxC,oBAAoB,mBAAO,CAAC,qJAAsC;;AAElE;AACA;AACA;AACA;AACA,sBAAsB,UAAU;AAChC,GAAG;AACH,CAAC;;;;;;;;;;;;;ACXY;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,YAAY,mBAAO,CAAC,iHAAoB;AACxC,cAAc,mBAAO,CAAC,6HAA0B;;AAEhD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;;;;;;;;;;;;;ACfY;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,YAAY,mBAAO,CAAC,+HAA2B;;AAE/C;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;ACda;AACb,sBAAsB,mBAAO,CAAC,uJAAuC;AACrE,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,kCAAkC,mBAAO,CAAC,mKAA6C;AACvF,aAAa,mBAAO,CAAC,uIAA+B;AACpD,aAAa,mBAAO,CAAC,+HAA2B;AAChD,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;AACA;AACA;;AAEA;AACA,uCAAuC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACtEa;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,gBAAgB,mBAAO,CAAC,yHAAwB;;AAEhD;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,cAAc,mBAAO,CAAC,6HAA0B;;AAEhD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACRa;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;;;;;;;;;;;;;ACXa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,oBAAoB,mBAAO,CAAC,mIAA6B;;AAEzD,wBAAwB;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,eAAe,EAAE;AAC1D;AACA,CAAC;;;;;;;;;;;;;ACnDY;AACb,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;;;ACtBa;AACb;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACLa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;AACA;;;;;;;;;;;;;ACLa;AACb,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;;;;;;;;;;;;;ACLa;AACb;;;;;;;;;;;;;ACDa;AACb,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,cAAc,mBAAO,CAAC,6HAA0B;AAChD,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,oBAAoB,mBAAO,CAAC,mJAAqC;AACjE,wBAAwB,mBAAO,CAAC,yIAAgC;;AAEhE;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;;;;;;;;;;;;;ACba;AACb,WAAW,mBAAO,CAAC,iJAAoC;AACvD,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,kBAAkB,mBAAO,CAAC,iIAA4B;AACtD,4BAA4B,mBAAO,CAAC,uJAAuC;AAC3E,wBAAwB,mBAAO,CAAC,+IAAmC;AACnE,oBAAoB,mBAAO,CAAC,mJAAqC;AACjE,kBAAkB,mBAAO,CAAC,+HAA2B;AACrD,wBAAwB,mBAAO,CAAC,6IAAkC;AAClE,oBAAoB,mBAAO,CAAC,mIAA6B;;AAEzD;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,2DAA2D,gBAAgB;AAC3E;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;ACpEa;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvBa;AACb,wBAAwB,mBAAO,CAAC,mIAA6B;AAC7D,aAAa,mBAAO,CAAC,iIAA4B;AACjD,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,qBAAqB,mBAAO,CAAC,yIAAgC;AAC7D,gBAAgB,mBAAO,CAAC,yHAAwB;;AAEhD,8BAA8B,aAAa;;AAE3C;AACA;AACA,6DAA6D,0DAA0D;AACvH;AACA;AACA;AACA;;;;;;;;;;;;;ACfa;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,mBAAmB,mBAAO,CAAC,iIAA4B;AACvD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,gCAAgC,mBAAO,CAAC,6JAA0C;AAClF,qBAAqB,mBAAO,CAAC,qJAAsC;AACnE,qBAAqB,mBAAO,CAAC,qJAAsC;AACnE,qBAAqB,mBAAO,CAAC,yIAAgC;AAC7D,kCAAkC,mBAAO,CAAC,mKAA6C;AACvF,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,gBAAgB,mBAAO,CAAC,yHAAwB;AAChD,oBAAoB,mBAAO,CAAC,mIAA6B;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;;AAEA;AACA;AACA;;AAEA;AACA,yCAAyC,4CAA4C;AACrF,6CAA6C,4CAA4C;AACzF,+CAA+C,4CAA4C;AAC3F;;AAEA,wBAAwB,sCAAsC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,0BAA0B,qBAAqB;AAC/C;AACA;AACA;AACA,KAAK;AACL;AACA,2CAA2C,mCAAmC;AAC9E;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,SAAS,qFAAqF;AACnG;;AAEA;AACA;AACA,iEAAiE,gBAAgB;AACjF;AACA;;AAEA;AACA;;;;;;;;;;;;;ACrGa;AACb,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,aAAa,mBAAO,CAAC,iIAA4B;AACjD,qBAAqB,mBAAO,CAAC,qJAAsC;AACnE,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,cAAc,mBAAO,CAAC,qHAAsB;;AAE5C;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;AChDa;AACb;;;;;;;;;;;;;ACDa;AACb,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACPa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,aAAa,mBAAO,CAAC,uIAA+B;AACpD,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,iCAAiC,mBAAO,CAAC,iIAA4B;AACrE,oBAAoB,mBAAO,CAAC,mIAA6B;AACzD,0BAA0B,mBAAO,CAAC,mIAA6B;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qCAAqC,cAAc,aAAa,WAAW;AAC3E,CAAC;;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,kCAAkC;AACtF;AACA;AACA;AACA,qCAAqC,uBAAuB;AAC5D;AACA;AACA;AACA,2DAA2D,kBAAkB;AAC7E;AACA,KAAK;AACL,GAAG,gBAAgB;AACnB;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACtDY;AACb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,qBAAqB,mBAAO,CAAC,yIAAgC;AAC7D,WAAW,mBAAO,CAAC,iJAAoC;AACvD,gBAAgB,mBAAO,CAAC,+GAAmB;AAC3C,YAAY,mBAAO,CAAC,iHAAoB;AACxC,aAAa,mBAAO,CAAC,2IAAiC;AACtD,oBAAoB,mBAAO,CAAC,yJAAwC;AACpE,sBAAsB,mBAAO,CAAC,6JAA0C;AACxE,cAAc,mBAAO,CAAC,6IAAkC;;AAExD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,+CAA+C,sBAAsB;AACrE;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;AC9Ea;AACb,gBAAgB,mBAAO,CAAC,2HAAyB;;AAEjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpBa;AACb,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;;AAEA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;ACTa;AACb;AACA,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,kBAAkB,mBAAO,CAAC,iIAA4B;AACtD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,WAAW,mBAAO,CAAC,+GAAmB;AACtC,4BAA4B,mBAAO,CAAC,qJAAsC;AAC1E,gBAAgB,mBAAO,CAAC,2HAAyB;;AAEjD;AACA;AACA;AACA;AACA;;AAEA,oCAAoC;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;;;;;;;;;;;ACpFa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,8BAA8B,mBAAO,CAAC,qJAAsC;AAC5E,2BAA2B,mBAAO,CAAC,mJAAqC;AACxE,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpBa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,qBAAqB,mBAAO,CAAC,mIAA6B;AAC1D,8BAA8B,mBAAO,CAAC,qJAAsC;AAC5E,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,oBAAoB,mBAAO,CAAC,qIAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;;;;;;;;;;;;;AC3Ca;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,iCAAiC,mBAAO,CAAC,iKAA4C;AACrF,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,aAAa,mBAAO,CAAC,uIAA+B;AACpD,qBAAqB,mBAAO,CAAC,mIAA6B;;AAE1D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;;;;;;;;;;;;;ACtBa;AACb,yBAAyB,mBAAO,CAAC,+IAAmC;AACpE,kBAAkB,mBAAO,CAAC,iIAA4B;;AAEtD;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACXa;AACb;AACA;;;;;;;;;;;;;ACFa;AACb,aAAa,mBAAO,CAAC,uIAA+B;AACpD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,+BAA+B,mBAAO,CAAC,uJAAuC;;AAE9E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;ACrBa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;;AAE9D,+BAA+B;;;;;;;;;;;;;ACHlB;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,aAAa,mBAAO,CAAC,uIAA+B;AACpD,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,cAAc,mBAAO,CAAC,mIAA6B;AACnD,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpBa;AACb,yBAAyB,mBAAO,CAAC,+IAAmC;AACpE,kBAAkB,mBAAO,CAAC,iIAA4B;;AAEtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,8BAA8B;AAC9B;AACA;;AAEA;AACA,2EAA2E,OAAO;;AAElF;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACbY;AACb;AACA,0BAA0B,mBAAO,CAAC,mKAA6C;AAC/E,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,yBAAyB,mBAAO,CAAC,+IAAmC;;AAEpE;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AC5BY;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACfa;AACb,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,gCAAgC,mBAAO,CAAC,iKAA4C;AACpF,kCAAkC,mBAAO,CAAC,qKAA8C;AACxF,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACda;AACb;AACA;AACA,YAAY;AACZ,GAAG;AACH,YAAY;AACZ;AACA;;;;;;;;;;;;;ACPa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,oBAAoB,mBAAO,CAAC,mIAA6B;AACzD,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,iBAAiB,mBAAO,CAAC,mJAAqC;;AAE9D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,YAAY,EAAE;AACjF;AACA,wBAAwB,cAAc,eAAe,cAAc;AACnE;AACA;AACA;AACA,4CAA4C,cAAc;AAC1D;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC9Ca;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;;;;;;;;;;;;;ACHa;AACb,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,2BAA2B,mBAAO,CAAC,mJAAqC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,kCAAkC,mBAAO,CAAC,mKAA6C;AACvF,iCAAiC,mBAAO,CAAC,iKAA4C;;AAErF;AACA,sEAAsE,cAAc;AACpF,CAAC;;;;;;;;;;;;;ACPY;AACb;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;ACxBa;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,cAAc,mBAAO,CAAC,6HAA0B;AAChD,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpBa;AACb;AACA;AACA,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,kBAAkB,mBAAO,CAAC,+HAA2B;AACrD,oBAAoB,mBAAO,CAAC,iJAAoC;AAChE,aAAa,mBAAO,CAAC,mHAAqB;AAC1C,aAAa,mBAAO,CAAC,iIAA4B;AACjD,uBAAuB,mBAAO,CAAC,mIAA6B;AAC5D,0BAA0B,mBAAO,CAAC,2JAAyC;AAC3E,sBAAsB,mBAAO,CAAC,mJAAqC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,0BAA0B;AAC7C;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;;;ACpHa;AACb,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,aAAa,mBAAO,CAAC,uIAA+B;AACpD,oBAAoB,mBAAO,CAAC,mJAAqC;AACjE,kBAAkB,mBAAO,CAAC,+HAA2B;;AAErD;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC9Ba;AACb,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;;AAEA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACVY;AACb,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACXY;AACb,wBAAwB,mBAAO,CAAC,+IAAmC;;AAEnE;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACVa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,kBAAkB,mBAAO,CAAC,6HAA0B;;AAEpD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,4BAA4B,mBAAO,CAAC,uJAAuC;AAC3E,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,kBAAkB,mBAAO,CAAC,6HAA0B;;AAEpD;;AAEA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,aAAa;AACrC,KAAK;AACL;AACA;;;;;;;;;;;;;ACjBa;AACb,qBAAqB,mBAAO,CAAC,mJAAqC;AAClE,aAAa,mBAAO,CAAC,uIAA+B;AACpD,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA,2CAA2C,iCAAiC;AAC5E;AACA;;;;;;;;;;;;;ACZa;AACb,aAAa,mBAAO,CAAC,mHAAqB;AAC1C,UAAU,mBAAO,CAAC,6GAAkB;;AAEpC;;AAEA;AACA;AACA;;;;;;;;;;;;;ACRa;AACb,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,2BAA2B,mBAAO,CAAC,mJAAqC;;AAExE;AACA,kFAAkF;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACdY;AACb,YAAY,mBAAO,CAAC,+HAA2B;;AAE/C;AACA,gDAAgD;AAChD;;;;;;;;;;;;;ACLa;AACb,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,mBAAmB,mBAAO,CAAC,iIAA4B;AACvD,wBAAwB,mBAAO,CAAC,+IAAmC;AACnE,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACda;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,0BAA0B,mBAAO,CAAC,mJAAqC;AACvE,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,6BAA6B,mBAAO,CAAC,uJAAuC;;AAE5E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpCa;AACb,2BAA2B,mBAAO,CAAC,iIAA4B;AAC/D,YAAY,mBAAO,CAAC,iHAAoB;AACxC,kBAAkB,mBAAO,CAAC,6HAA0B;;AAEpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;ACfa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,kBAAkB,mBAAO,CAAC,6HAA0B;;AAEpD;AACA;AACA;;AAEA,sBAAsB,gDAAgD;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA,wBAAwB,qBAAqB;AAC7C;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC9Ba;AACb;AACA,iBAAiB,mBAAO,CAAC,mJAAqC;AAC9D,YAAY,mBAAO,CAAC,iHAAoB;AACxC,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AClBY;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,YAAY,mBAAO,CAAC,mIAA6B;AACjD,WAAW,mBAAO,CAAC,iJAAoC;AACvD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,aAAa,mBAAO,CAAC,uIAA+B;AACpD,YAAY,mBAAO,CAAC,iHAAoB;AACxC,WAAW,mBAAO,CAAC,+GAAmB;AACtC,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,oBAAoB,mBAAO,CAAC,qJAAsC;AAClE,8BAA8B,mBAAO,CAAC,yJAAwC;AAC9E,aAAa,mBAAO,CAAC,2IAAiC;AACtD,cAAc,mBAAO,CAAC,6IAAkC;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACpHa;AACb,0BAA0B,mBAAO,CAAC,mJAAqC;;AAEvE;AACA;;AAEA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb;AACA,oBAAoB,mBAAO,CAAC,mIAA6B;AACzD,6BAA6B,mBAAO,CAAC,uJAAuC;;AAE5E;AACA;AACA;;;;;;;;;;;;;ACPa;AACb,YAAY,mBAAO,CAAC,2HAAyB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,0BAA0B,mBAAO,CAAC,mJAAqC;;AAEvE;;AAEA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;;;;;;;;;;;;;ACVa;AACb,6BAA6B,mBAAO,CAAC,uJAAuC;;AAE5E;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,0BAA0B,mBAAO,CAAC,iJAAoC;AACtE,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACzBa;AACb,kBAAkB,mBAAO,CAAC,+HAA2B;AACrD,eAAe,mBAAO,CAAC,yHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;AACA;;AAEA;;AAEA;;;;;;;;;;;;;ACRa;AACb,cAAc,mBAAO,CAAC,qHAAsB;;AAE5C;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACRa;AACb;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;;;;;ACTa;AACb,kBAAkB,mBAAO,CAAC,iJAAoC;;AAE9D;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;;ACTa;AACb;AACA,oBAAoB,mBAAO,CAAC,+JAA2C;;AAEvE;AACA;AACA;;;;;;;;;;;;;ACNa;AACb,kBAAkB,mBAAO,CAAC,6HAA0B;AACpD,YAAY,mBAAO,CAAC,iHAAoB;;AAExC;AACA;AACA;AACA;AACA,4CAA4C,cAAc;AAC1D;AACA;AACA,GAAG;AACH,CAAC;;;;;;;;;;;;;ACZY;AACb;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACNa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,iBAAiB,mBAAO,CAAC,6HAA0B;;AAEnD;;AAEA;;;;;;;;;;;;;ACNa;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,aAAa,mBAAO,CAAC,mHAAqB;AAC1C,aAAa,mBAAO,CAAC,uIAA+B;AACpD,UAAU,mBAAO,CAAC,6GAAkB;AACpC,oBAAoB,mBAAO,CAAC,+JAA2C;AACvE,wBAAwB,mBAAO,CAAC,yIAAgC;;AAEhE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;AClBa;AACb;AACA;AACA;;;;;;;;;;;;;ACHa;AACb;AACA,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,kBAAkB,mBAAO,CAAC,+JAA2C;AACrE,eAAe,mBAAO,CAAC,mIAA6B;AACpD,0BAA0B,mBAAO,CAAC,mJAAqC;;AAEvE;;AAEA;AACA;;AAEA;AACA;AACA,GAAG,+CAA+C;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACtBY;AACb,sBAAsB,mBAAO,CAAC,yIAAgC;AAC9D,uBAAuB,mBAAO,CAAC,2IAAiC;AAChE,gBAAgB,mBAAO,CAAC,yHAAwB;AAChD,0BAA0B,mBAAO,CAAC,mIAA6B;AAC/D,qBAAqB,mBAAO,CAAC,mJAAqC;AAClE,qBAAqB,mBAAO,CAAC,qIAA8B;AAC3D,6BAA6B,mBAAO,CAAC,yJAAwC;AAC7E,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,kBAAkB,mBAAO,CAAC,6HAA0B;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,kCAAkC,kBAAkB;AACpD,CAAC,gBAAgB;;;;;;;;;;;;;AC7DJ;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,cAAc,mBAAO,CAAC,+HAA2B;AACjD,0BAA0B,mBAAO,CAAC,mJAAqC;AACvE,qBAAqB,mBAAO,CAAC,mJAAqC;AAClE,cAAc,mBAAO,CAAC,6IAAkC;;AAExD;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG,+CAA+C;AAClD;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACnBY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,cAAc,mBAAO,CAAC,uHAAuB;;AAE7C;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG,gFAAgF;AACnF;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AClBY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,iCAAiC,mBAAO,CAAC,mJAAqC;AAC9E,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,0CAA0C,mBAAO,CAAC,6KAAkD;;AAEpG;AACA;AACA,GAAG,6EAA6E;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACtCY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,iCAAiC,mBAAO,CAAC,iKAA4C;AACrF,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,oBAAoB,mBAAO,CAAC,qIAA8B;;AAE1D;;AAEA;AACA;AACA,GAAG,iFAAiF;AACpF;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,4DAA4D,eAAe;AAC3E;AACA;;;;;;;;;;;;;ACzBa;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,cAAc,mBAAO,CAAC,6IAAkC;AACxD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,qBAAqB,mBAAO,CAAC,qJAAsC;AACnE,qBAAqB,mBAAO,CAAC,yIAAgC;AAC7D,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,yBAAyB,mBAAO,CAAC,6IAAkC;AACnE,WAAW,mBAAO,CAAC,+GAAmB;AACtC,gBAAgB,mBAAO,CAAC,yHAAwB;AAChD,uBAAuB,mBAAO,CAAC,2IAAiC;AAChE,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,YAAY,mBAAO,CAAC,iHAAoB;AACxC,0BAA0B,mBAAO,CAAC,mIAA6B;AAC/D,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,kCAAkC,mBAAO,CAAC,iKAA4C;AACtF,iCAAiC,mBAAO,CAAC,mJAAqC;;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,eAAe;AAClB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH,oBAAoB,cAAc;AAClC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO,GAAG,eAAe;AACzB;;AAEA;AACA;AACA;AACA,KAAK,gBAAgB;;AAErB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG,kFAAkF;AACrF;AACA,CAAC;;AAED;AACA;;;;;;;;;;;;;ACjSa;AACb;AACA,mBAAO,CAAC,+IAAmC;AAC3C,mBAAO,CAAC,+HAA2B;AACnC,mBAAO,CAAC,mIAA6B;AACrC,mBAAO,CAAC,iIAA4B;AACpC,mBAAO,CAAC,qIAA8B;AACtC,mBAAO,CAAC,uIAA+B;;;;;;;;;;;;;ACP1B;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,iCAAiC,mBAAO,CAAC,mJAAqC;AAC9E,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,0CAA0C,mBAAO,CAAC,6KAAkD;;AAEpG;AACA;AACA,GAAG,6EAA6E;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACzBY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,iCAAiC,mBAAO,CAAC,mJAAqC;AAC9E,iCAAiC,mBAAO,CAAC,iKAA4C;;AAErF;AACA;AACA,GAAG,oEAAoE;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACdY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,cAAc,mBAAO,CAAC,qHAAsB;AAC5C,+BAA+B,mBAAO,CAAC,2JAAyC;AAChF,iCAAiC,mBAAO,CAAC,iKAA4C;AACrF,qBAAqB,mBAAO,CAAC,qIAA8B;;AAE3D;AACA;;AAEA;AACA;AACA,GAAG,+EAA+E;AAClF;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACjBY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,WAAW,mBAAO,CAAC,6HAA0B;;AAE7C;AACA;AACA,GAAG,2DAA2D;AAC9D;AACA,CAAC;;;;;;;;;;;;;ACRY;AACb,2BAA2B,mBAAO,CAAC,iIAA4B;AAC/D,oBAAoB,mBAAO,CAAC,qIAA8B;AAC1D,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,gBAAgB,mBAAO,CAAC,yHAAwB;AAChD,YAAY,mBAAO,CAAC,iHAAoB;AACxC,qBAAqB,mBAAO,CAAC,uIAA+B;;AAE5D;AACA;AACA;;AAEA,qCAAqC,6BAA6B,0BAA0B,aAAa,EAAE;AAC3G;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG,eAAe;AACrB;;;;;;;;;;;;;ACzBa;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,kBAAkB,mBAAO,CAAC,+JAA2C;AACrE,+BAA+B,mBAAO,CAAC,2KAAiD;AACxF,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,2BAA2B,mBAAO,CAAC,qJAAsC;AACzE,cAAc,mBAAO,CAAC,qHAAsB;;AAE5C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,GAAG,uFAAuF;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACjCY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,2BAA2B,mBAAO,CAAC,qJAAsC;;AAEzE;;AAEA;AACA;AACA,GAAG,2EAA2E;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACpBY;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,oCAAoC,mBAAO,CAAC,2KAAiD;AAC7F,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,wBAAwB,mBAAO,CAAC,+IAAmC;AACnE,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,yBAAyB,mBAAO,CAAC,+IAAmC;AACpE,iBAAiB,mBAAO,CAAC,+IAAmC;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AC/CY;AACb,YAAY,mBAAO,CAAC,mIAA6B;AACjD,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,oCAAoC,mBAAO,CAAC,2KAAiD;AAC7F,YAAY,mBAAO,CAAC,iHAAoB;AACxC,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,wBAAwB,mBAAO,CAAC,+IAAmC;AACnE,0BAA0B,mBAAO,CAAC,mJAAqC;AACvE,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,yBAAyB,mBAAO,CAAC,+IAAmC;AACpE,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,sBAAsB,mBAAO,CAAC,uIAA+B;AAC7D,iBAAiB,mBAAO,CAAC,+IAAmC;AAC5D,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB,oBAAoB;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AC7IY;AACb,WAAW,mBAAO,CAAC,iIAA4B;AAC/C,kBAAkB,mBAAO,CAAC,iJAAoC;AAC9D,oCAAoC,mBAAO,CAAC,2KAAiD;AAC7F,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,wBAAwB,mBAAO,CAAC,+IAAmC;AACnE,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,yBAAyB,mBAAO,CAAC,6IAAkC;AACnE,yBAAyB,mBAAO,CAAC,+IAAmC;AACpE,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,gBAAgB,mBAAO,CAAC,2HAAyB;AACjD,iBAAiB,mBAAO,CAAC,+IAAmC;AAC5D,oBAAoB,mBAAO,CAAC,iJAAoC;AAChE,YAAY,mBAAO,CAAC,iHAAoB;;AAExC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,4CAA4C;AACrE;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,yBAAyB,mBAAmB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AC9GY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,kBAAkB,mBAAO,CAAC,+JAA2C;AACrE,+BAA+B,mBAAO,CAAC,2KAAiD;AACxF,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,eAAe,mBAAO,CAAC,yHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,+HAA2B;AACpD,6BAA6B,mBAAO,CAAC,uJAAuC;AAC5E,2BAA2B,mBAAO,CAAC,qJAAsC;AACzE,cAAc,mBAAO,CAAC,qHAAsB;;AAE5C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA,GAAG,uFAAuF;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;AC/BY;AACb,QAAQ,mBAAO,CAAC,mHAAqB;AACrC,YAAY,mBAAO,CAAC,6HAA0B;AAC9C,6BAA6B,mBAAO,CAAC,2IAAiC;;AAEtE;AACA;AACA,GAAG,wEAAwE;AAC3E;AACA;AACA;AACA,CAAC;;;;;;;;;;;;;ACXY;AACb,iBAAiB,mBAAO,CAAC,6HAA0B;AACnD,mBAAmB,mBAAO,CAAC,iIAA4B;AACvD,4BAA4B,mBAAO,CAAC,uJAAuC;AAC3E,2BAA2B,mBAAO,CAAC,qIAA8B;AACjE,kCAAkC,mBAAO,CAAC,mKAA6C;AACvF,qBAAqB,mBAAO,CAAC,yIAAgC;AAC7D,sBAAsB,mBAAO,CAAC,yIAAgC;;AAE9D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA", "file": "2.js", "sourcesContent": ["function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nmodule.exports = _asyncToGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import 'core-js/modules/es.promise.js';\nimport _asyncToGenerator from '@babel/runtime/helpers/asyncToGenerator';\nimport 'core-js/modules/es.string.match.js';\nimport 'core-js/modules/es.string.replace.js';\nimport 'core-js/modules/es.string.starts-with.js';\nimport 'core-js/modules/es.array.iterator.js';\nimport 'core-js/modules/web.dom-collections.iterator.js';\nimport _defineProperty from '@babel/runtime/helpers/defineProperty';\nimport 'core-js/modules/es.array.reduce.js';\nimport 'core-js/modules/es.string.ends-with.js';\nimport 'core-js/modules/es.string.split.js';\nimport requestAnimationFrame from 'raf';\nimport 'core-js/modules/es.string.trim.js';\nimport RGBColor from 'rgbcolor';\nimport 'core-js/modules/es.array.index-of.js';\nimport 'core-js/modules/es.string.includes.js';\nimport 'core-js/modules/es.array.reverse.js';\nimport { SVGPathData } from 'svg-pathdata';\nimport 'core-js/modules/es.regexp.to-string.js';\nimport { canvasRGBA } from 'stackblur-canvas';\n\n/**\r\n * Options preset for `OffscreenCanvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @returns Preset object.\r\n */\nfunction offscreen() {\n  var {\n    DOMParser: DOMParserFallback\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var preset = {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser: DOMParserFallback,\n\n    createCanvas(width, height) {\n      return new OffscreenCanvas(width, height);\n    },\n\n    createImage(url) {\n      return _asyncToGenerator(function* () {\n        var response = yield fetch(url);\n        var blob = yield response.blob();\n        var img = yield createImageBitmap(blob);\n        return img;\n      })();\n    }\n\n  };\n\n  if (typeof DOMParser !== 'undefined' || typeof DOMParserFallback === 'undefined') {\n    Reflect.deleteProperty(preset, 'DOMParser');\n  }\n\n  return preset;\n}\n\n/**\r\n * Options preset for `node-canvas`.\r\n * @param config - Preset requirements.\r\n * @param config.DOMParser - XML/HTML parser from string into DOM Document.\r\n * @param config.canvas - `node-canvas` exports.\r\n * @param config.fetch - WHATWG-compatible `fetch` function.\r\n * @returns Preset object.\r\n */\nfunction node(_ref) {\n  var {\n    DOMParser,\n    canvas,\n    fetch\n  } = _ref;\n  return {\n    window: null,\n    ignoreAnimation: true,\n    ignoreMouse: true,\n    DOMParser,\n    fetch,\n    createCanvas: canvas.createCanvas,\n    createImage: canvas.loadImage\n  };\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\toffscreen: offscreen,\n\tnode: node\n});\n\n/**\r\n * HTML-safe compress white-spaces.\r\n * @param str - String to compress.\r\n * @returns String.\r\n */\nfunction compressSpaces(str) {\n  return str.replace(/(?!\\u3000)\\s+/gm, ' ');\n}\n/**\r\n * HTML-safe left trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimLeft(str) {\n  return str.replace(/^[\\n \\t]+/, '');\n}\n/**\r\n * HTML-safe right trim.\r\n * @param str - String to trim.\r\n * @returns String.\r\n */\n\nfunction trimRight(str) {\n  return str.replace(/[\\n \\t]+$/, '');\n}\n/**\r\n * String to numbers array.\r\n * @param str - Numbers string.\r\n * @returns Numbers array.\r\n */\n\nfunction toNumbers(str) {\n  var matches = (str || '').match(/-?(\\d+(?:\\.\\d*(?:[eE][+-]?\\d+)?)?|\\.\\d+)(?=\\D|$)/gm) || [];\n  return matches.map(parseFloat);\n} // Microsoft Edge fix\n\nvar allUppercase = /^[A-Z-]+$/;\n/**\r\n * Normalize attribute name.\r\n * @param name - Attribute name.\r\n * @returns Normalized attribute name.\r\n */\n\nfunction normalizeAttributeName(name) {\n  if (allUppercase.test(name)) {\n    return name.toLowerCase();\n  }\n\n  return name;\n}\n/**\r\n * Parse external URL.\r\n * @param url - CSS url string.\r\n * @returns Parsed URL.\r\n */\n\nfunction parseExternalUrl(url) {\n  //                      single quotes [2]\n  //                      v         double quotes [3]\n  //                      v         v         no quotes [4]\n  //                      v         v         v\n  var urlMatch = /url\\(('([^']+)'|\"([^\"]+)\"|([^'\")]+))\\)/.exec(url) || [];\n  return urlMatch[2] || urlMatch[3] || urlMatch[4];\n}\n/**\r\n * Transform floats to integers in rgb colors.\r\n * @param color - Color to normalize.\r\n * @returns Normalized color.\r\n */\n\nfunction normalizeColor(color) {\n  if (!color.startsWith('rgb')) {\n    return color;\n  }\n\n  var rgbParts = 3;\n  var normalizedColor = color.replace(/\\d+(\\.\\d+)?/g, (num, isFloat) => rgbParts-- && isFloat ? String(Math.round(parseFloat(num))) : num);\n  return normalizedColor;\n}\n\n// slightly modified version of https://github.com/keeganstreet/specificity/blob/master/specificity.js\nvar attributeRegex = /(\\[[^\\]]+\\])/g;\nvar idRegex = /(#[^\\s+>~.[:]+)/g;\nvar classRegex = /(\\.[^\\s+>~.[:]+)/g;\nvar pseudoElementRegex = /(::[^\\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi;\nvar pseudoClassWithBracketsRegex = /(:[\\w-]+\\([^)]*\\))/gi;\nvar pseudoClassRegex = /(:[^\\s+>~.[:]+)/g;\nvar elementRegex = /([^\\s+>~.[:]+)/g;\n\nfunction findSelectorMatch(selector, regex) {\n  var matches = regex.exec(selector);\n\n  if (!matches) {\n    return [selector, 0];\n  }\n\n  return [selector.replace(regex, ' '), matches.length];\n}\n/**\r\n * Measure selector specificity.\r\n * @param selector - Selector to measure.\r\n * @returns Specificity.\r\n */\n\n\nfunction getSelectorSpecificity(selector) {\n  var specificity = [0, 0, 0];\n  var currentSelector = selector.replace(/:not\\(([^)]*)\\)/g, '     $1 ').replace(/{[\\s\\S]*/gm, ' ');\n  var delta = 0;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, attributeRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, idRegex);\n  specificity[0] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, classRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoElementRegex);\n  specificity[2] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassWithBracketsRegex);\n  specificity[1] += delta;\n  [currentSelector, delta] = findSelectorMatch(currentSelector, pseudoClassRegex);\n  specificity[1] += delta;\n  currentSelector = currentSelector.replace(/[*\\s+>~]/g, ' ').replace(/[#.]/g, ' ');\n  [currentSelector, delta] = findSelectorMatch(currentSelector, elementRegex); // lgtm [js/useless-assignment-to-local]\n\n  specificity[2] += delta;\n  return specificity.join('');\n}\n\nvar PSEUDO_ZERO = .00000001;\n/**\r\n * Vector magnitude.\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorMagnitude(v) {\n  return Math.sqrt(Math.pow(v[0], 2) + Math.pow(v[1], 2));\n}\n/**\r\n * Ratio between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vectorMagnitude(u) * vectorMagnitude(v));\n}\n/**\r\n * Angle between two vectors.\r\n * @param u\r\n * @param v\r\n * @returns Number result.\r\n */\n\nfunction vectorsAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vectorsRatio(u, v));\n}\nfunction CB1(t) {\n  return t * t * t;\n}\nfunction CB2(t) {\n  return 3 * t * t * (1 - t);\n}\nfunction CB3(t) {\n  return 3 * t * (1 - t) * (1 - t);\n}\nfunction CB4(t) {\n  return (1 - t) * (1 - t) * (1 - t);\n}\nfunction QB1(t) {\n  return t * t;\n}\nfunction QB2(t) {\n  return 2 * t * (1 - t);\n}\nfunction QB3(t) {\n  return (1 - t) * (1 - t);\n}\n\nclass Property {\n  constructor(document, name, value) {\n    this.document = document;\n    this.name = name;\n    this.value = value;\n    this.isNormalizedColor = false;\n  }\n\n  static empty(document) {\n    return new Property(document, 'EMPTY', '');\n  }\n\n  split() {\n    var separator = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n    var {\n      document,\n      name\n    } = this;\n    return compressSpaces(this.getString()).trim().split(separator).map(value => new Property(document, name, value));\n  }\n\n  hasValue(zeroIsValue) {\n    var {\n      value\n    } = this;\n    return value !== null && value !== '' && (zeroIsValue || value !== 0) && typeof value !== 'undefined';\n  }\n\n  isString(regexp) {\n    var {\n      value\n    } = this;\n    var result = typeof value === 'string';\n\n    if (!result || !regexp) {\n      return result;\n    }\n\n    return regexp.test(value);\n  }\n\n  isUrlDefinition() {\n    return this.isString(/^url\\(/);\n  }\n\n  isPixels() {\n    if (!this.hasValue()) {\n      return false;\n    }\n\n    var asString = this.getString();\n\n    switch (true) {\n      case asString.endsWith('px'):\n      case /^[0-9]+$/.test(asString):\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  setValue(value) {\n    this.value = value;\n    return this;\n  }\n\n  getValue(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return this.value;\n    }\n\n    return def;\n  }\n\n  getNumber(def) {\n    if (!this.hasValue()) {\n      if (typeof def === 'undefined') {\n        return 0;\n      }\n\n      return parseFloat(def);\n    }\n\n    var {\n      value\n    } = this;\n    var n = parseFloat(value);\n\n    if (this.isString(/%$/)) {\n      n /= 100.0;\n    }\n\n    return n;\n  }\n\n  getString(def) {\n    if (typeof def === 'undefined' || this.hasValue()) {\n      return typeof this.value === 'undefined' ? '' : String(this.value);\n    }\n\n    return String(def);\n  }\n\n  getColor(def) {\n    var color = this.getString(def);\n\n    if (this.isNormalizedColor) {\n      return color;\n    }\n\n    this.isNormalizedColor = true;\n    color = normalizeColor(color);\n    this.value = color;\n    return color;\n  }\n\n  getDpi() {\n    return 96.0; // TODO: compute?\n  }\n\n  getRem() {\n    return this.document.rootEmSize;\n  }\n\n  getEm() {\n    return this.document.emSize;\n  }\n\n  getUnits() {\n    return this.getString().replace(/[0-9.-]/g, '');\n  }\n\n  getPixels(axisOrIsFontSize) {\n    var processPercent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    var [axis, isFontSize] = typeof axisOrIsFontSize === 'boolean' ? [undefined, axisOrIsFontSize] : [axisOrIsFontSize];\n    var {\n      viewPort\n    } = this.document.screen;\n\n    switch (true) {\n      case this.isString(/vmin$/):\n        return this.getNumber() / 100.0 * Math.min(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vmax$/):\n        return this.getNumber() / 100.0 * Math.max(viewPort.computeSize('x'), viewPort.computeSize('y'));\n\n      case this.isString(/vw$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('x');\n\n      case this.isString(/vh$/):\n        return this.getNumber() / 100.0 * viewPort.computeSize('y');\n\n      case this.isString(/rem$/):\n        return this.getNumber() * this.getRem();\n\n      case this.isString(/em$/):\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/ex$/):\n        return this.getNumber() * this.getEm() / 2.0;\n\n      case this.isString(/px$/):\n        return this.getNumber();\n\n      case this.isString(/pt$/):\n        return this.getNumber() * this.getDpi() * (1.0 / 72.0);\n\n      case this.isString(/pc$/):\n        return this.getNumber() * 15;\n\n      case this.isString(/cm$/):\n        return this.getNumber() * this.getDpi() / 2.54;\n\n      case this.isString(/mm$/):\n        return this.getNumber() * this.getDpi() / 25.4;\n\n      case this.isString(/in$/):\n        return this.getNumber() * this.getDpi();\n\n      case this.isString(/%$/) && isFontSize:\n        return this.getNumber() * this.getEm();\n\n      case this.isString(/%$/):\n        return this.getNumber() * viewPort.computeSize(axis);\n\n      default:\n        {\n          var n = this.getNumber();\n\n          if (processPercent && n < 1.0) {\n            return n * viewPort.computeSize(axis);\n          }\n\n          return n;\n        }\n    }\n  }\n\n  getMilliseconds() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    if (this.isString(/ms$/)) {\n      return this.getNumber();\n    }\n\n    return this.getNumber() * 1000;\n  }\n\n  getRadians() {\n    if (!this.hasValue()) {\n      return 0;\n    }\n\n    switch (true) {\n      case this.isString(/deg$/):\n        return this.getNumber() * (Math.PI / 180.0);\n\n      case this.isString(/grad$/):\n        return this.getNumber() * (Math.PI / 200.0);\n\n      case this.isString(/rad$/):\n        return this.getNumber();\n\n      default:\n        return this.getNumber() * (Math.PI / 180.0);\n    }\n  }\n\n  getDefinition() {\n    var asString = this.getString();\n    var name = /#([^)'\"]+)/.exec(asString);\n\n    if (name) {\n      name = name[1];\n    }\n\n    if (!name) {\n      name = asString;\n    }\n\n    return this.document.definitions[name];\n  }\n\n  getFillStyleDefinition(element, opacity) {\n    var def = this.getDefinition();\n\n    if (!def) {\n      return null;\n    } // gradient\n\n\n    if (typeof def.createGradient === 'function') {\n      return def.createGradient(this.document.ctx, element, opacity);\n    } // pattern\n\n\n    if (typeof def.createPattern === 'function') {\n      if (def.getHrefAttribute().hasValue()) {\n        var patternTransform = def.getAttribute('patternTransform');\n        def = def.getHrefAttribute().getDefinition();\n\n        if (patternTransform.hasValue()) {\n          def.getAttribute('patternTransform', true).setValue(patternTransform.value);\n        }\n      }\n\n      return def.createPattern(this.document.ctx, element, opacity);\n    }\n\n    return null;\n  }\n\n  getTextBaseline() {\n    if (!this.hasValue()) {\n      return null;\n    }\n\n    return Property.textBaselineMapping[this.getString()];\n  }\n\n  addOpacity(opacity) {\n    var value = this.getColor();\n    var len = value.length;\n    var commas = 0; // Simulate old RGBColor version, which can't parse rgba.\n\n    for (var i = 0; i < len; i++) {\n      if (value[i] === ',') {\n        commas++;\n      }\n\n      if (commas === 3) {\n        break;\n      }\n    }\n\n    if (opacity.hasValue() && this.isString() && commas !== 3) {\n      var color = new RGBColor(value);\n\n      if (color.ok) {\n        color.alpha = opacity.getNumber();\n        value = color.toRGBA();\n      }\n    }\n\n    return new Property(this.document, this.name, value);\n  }\n\n}\nProperty.textBaselineMapping = {\n  'baseline': 'alphabetic',\n  'before-edge': 'top',\n  'text-before-edge': 'top',\n  'middle': 'middle',\n  'central': 'middle',\n  'after-edge': 'bottom',\n  'text-after-edge': 'bottom',\n  'ideographic': 'ideographic',\n  'alphabetic': 'alphabetic',\n  'hanging': 'hanging',\n  'mathematical': 'alphabetic'\n};\n\nclass ViewPort {\n  constructor() {\n    this.viewPorts = [];\n  }\n\n  clear() {\n    this.viewPorts = [];\n  }\n\n  setCurrent(width, height) {\n    this.viewPorts.push({\n      width,\n      height\n    });\n  }\n\n  removeCurrent() {\n    this.viewPorts.pop();\n  }\n\n  getCurrent() {\n    var {\n      viewPorts\n    } = this;\n    return viewPorts[viewPorts.length - 1];\n  }\n\n  get width() {\n    return this.getCurrent().width;\n  }\n\n  get height() {\n    return this.getCurrent().height;\n  }\n\n  computeSize(d) {\n    if (typeof d === 'number') {\n      return d;\n    }\n\n    if (d === 'x') {\n      return this.width;\n    }\n\n    if (d === 'y') {\n      return this.height;\n    }\n\n    return Math.sqrt(Math.pow(this.width, 2) + Math.pow(this.height, 2)) / Math.sqrt(2);\n  }\n\n}\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  static parse(point) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var [x = defaultValue, y = defaultValue] = toNumbers(point);\n    return new Point(x, y);\n  }\n\n  static parseScale(scale) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var [x = defaultValue, y = x] = toNumbers(scale);\n    return new Point(x, y);\n  }\n\n  static parsePath(path) {\n    var points = toNumbers(path);\n    var len = points.length;\n    var pathPoints = [];\n\n    for (var i = 0; i < len; i += 2) {\n      pathPoints.push(new Point(points[i], points[i + 1]));\n    }\n\n    return pathPoints;\n  }\n\n  angleTo(point) {\n    return Math.atan2(point.y - this.y, point.x - this.x);\n  }\n\n  applyTransform(transform) {\n    var {\n      x,\n      y\n    } = this;\n    var xp = x * transform[0] + y * transform[2] + transform[4];\n    var yp = x * transform[1] + y * transform[3] + transform[5];\n    this.x = xp;\n    this.y = yp;\n  }\n\n}\n\nclass Mouse {\n  constructor(screen) {\n    this.screen = screen;\n    this.working = false;\n    this.events = [];\n    this.eventElements = []; // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onClick = this.onClick.bind(this); // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n\n    this.onMouseMove = this.onMouseMove.bind(this);\n  }\n\n  isWorking() {\n    return this.working;\n  }\n\n  start() {\n    if (this.working) {\n      return;\n    }\n\n    var {\n      screen,\n      onClick,\n      onMouseMove\n    } = this;\n    var canvas = screen.ctx.canvas;\n    canvas.onclick = onClick;\n    canvas.onmousemove = onMouseMove;\n    this.working = true;\n  }\n\n  stop() {\n    if (!this.working) {\n      return;\n    }\n\n    var canvas = this.screen.ctx.canvas;\n    this.working = false;\n    canvas.onclick = null;\n    canvas.onmousemove = null;\n  }\n\n  hasEvents() {\n    return this.working && this.events.length > 0;\n  }\n\n  runEvents() {\n    if (!this.working) {\n      return;\n    }\n\n    var {\n      screen: document,\n      events,\n      eventElements\n    } = this;\n    var {\n      style\n    } = document.ctx.canvas;\n\n    if (style) {\n      style.cursor = '';\n    }\n\n    events.forEach((_ref, i) => {\n      var {\n        run\n      } = _ref;\n      var element = eventElements[i];\n\n      while (element) {\n        run(element);\n        element = element.parent;\n      }\n    }); // done running, clear\n\n    this.events = [];\n    this.eventElements = [];\n  }\n\n  checkPath(element, ctx) {\n    if (!this.working || !ctx) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref2, i) => {\n      var {\n        x,\n        y\n      } = _ref2;\n\n      if (!eventElements[i] && ctx.isPointInPath && ctx.isPointInPath(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  checkBoundingBox(element, boundingBox) {\n    if (!this.working || !boundingBox) {\n      return;\n    }\n\n    var {\n      events,\n      eventElements\n    } = this;\n    events.forEach((_ref3, i) => {\n      var {\n        x,\n        y\n      } = _ref3;\n\n      if (!eventElements[i] && boundingBox.isPointInBox(x, y)) {\n        eventElements[i] = element;\n      }\n    });\n  }\n\n  mapXY(x, y) {\n    var {\n      window,\n      ctx\n    } = this.screen;\n    var point = new Point(x, y);\n    var element = ctx.canvas;\n\n    while (element) {\n      point.x -= element.offsetLeft;\n      point.y -= element.offsetTop;\n      element = element.offsetParent;\n    }\n\n    if (window.scrollX) {\n      point.x += window.scrollX;\n    }\n\n    if (window.scrollY) {\n      point.y += window.scrollY;\n    }\n\n    return point;\n  }\n\n  onClick(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onclick',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onClick) {\n          eventTarget.onClick();\n        }\n      }\n\n    });\n  }\n\n  onMouseMove(event) {\n    var {\n      x,\n      y\n    } = this.mapXY(event.clientX, event.clientY);\n    this.events.push({\n      type: 'onmousemove',\n      x,\n      y,\n\n      run(eventTarget) {\n        if (eventTarget.onMouseMove) {\n          eventTarget.onMouseMove();\n        }\n      }\n\n    });\n  }\n\n}\n\nvar defaultWindow = typeof window !== 'undefined' ? window : null;\nvar defaultFetch$1 = typeof fetch !== 'undefined' ? fetch.bind(undefined) // `fetch` depends on context: `someObject.fetch(...)` will throw error.\n: null;\nclass Screen {\n  constructor(ctx) {\n    var {\n      fetch = defaultFetch$1,\n      window = defaultWindow\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.ctx = ctx;\n    this.FRAMERATE = 30;\n    this.MAX_VIRTUAL_PIXELS = 30000;\n    this.CLIENT_WIDTH = 800;\n    this.CLIENT_HEIGHT = 600;\n    this.viewPort = new ViewPort();\n    this.mouse = new Mouse(this);\n    this.animations = [];\n    this.waits = [];\n    this.frameDuration = 0;\n    this.isReadyLock = false;\n    this.isFirstRender = true;\n    this.intervalId = null;\n    this.window = window;\n    this.fetch = fetch;\n  }\n\n  wait(checker) {\n    this.waits.push(checker);\n  }\n\n  ready() {\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    if (!this.readyPromise) {\n      return Promise.resolve();\n    }\n\n    return this.readyPromise;\n  }\n\n  isReady() {\n    if (this.isReadyLock) {\n      return true;\n    }\n\n    var isReadyLock = this.waits.every(_ => _());\n\n    if (isReadyLock) {\n      this.waits = [];\n\n      if (this.resolveReady) {\n        this.resolveReady();\n      }\n    }\n\n    this.isReadyLock = isReadyLock;\n    return isReadyLock;\n  }\n\n  setDefaults(ctx) {\n    // initial values and defaults\n    ctx.strokeStyle = 'rgba(0,0,0,0)';\n    ctx.lineCap = 'butt';\n    ctx.lineJoin = 'miter';\n    ctx.miterLimit = 4;\n  }\n\n  setViewBox(_ref) {\n    var {\n      document,\n      ctx,\n      aspectRatio,\n      width,\n      desiredWidth,\n      height,\n      desiredHeight,\n      minX = 0,\n      minY = 0,\n      refX,\n      refY,\n      clip = false,\n      clipX = 0,\n      clipY = 0\n    } = _ref;\n    // aspect ratio - http://www.w3.org/TR/SVG/coords.html#PreserveAspectRatioAttribute\n    var cleanAspectRatio = compressSpaces(aspectRatio).replace(/^defer\\s/, ''); // ignore defer\n\n    var [aspectRatioAlign, aspectRatioMeetOrSlice] = cleanAspectRatio.split(' ');\n    var align = aspectRatioAlign || 'xMidYMid';\n    var meetOrSlice = aspectRatioMeetOrSlice || 'meet'; // calculate scale\n\n    var scaleX = width / desiredWidth;\n    var scaleY = height / desiredHeight;\n    var scaleMin = Math.min(scaleX, scaleY);\n    var scaleMax = Math.max(scaleX, scaleY);\n    var finalDesiredWidth = desiredWidth;\n    var finalDesiredHeight = desiredHeight;\n\n    if (meetOrSlice === 'meet') {\n      finalDesiredWidth *= scaleMin;\n      finalDesiredHeight *= scaleMin;\n    }\n\n    if (meetOrSlice === 'slice') {\n      finalDesiredWidth *= scaleMax;\n      finalDesiredHeight *= scaleMax;\n    }\n\n    var refXProp = new Property(document, 'refX', refX);\n    var refYProp = new Property(document, 'refY', refY);\n    var hasRefs = refXProp.hasValue() && refYProp.hasValue();\n\n    if (hasRefs) {\n      ctx.translate(-scaleMin * refXProp.getPixels('x'), -scaleMin * refYProp.getPixels('y'));\n    }\n\n    if (clip) {\n      var scaledClipX = scaleMin * clipX;\n      var scaledClipY = scaleMin * clipY;\n      ctx.beginPath();\n      ctx.moveTo(scaledClipX, scaledClipY);\n      ctx.lineTo(width, scaledClipY);\n      ctx.lineTo(width, height);\n      ctx.lineTo(scaledClipX, height);\n      ctx.closePath();\n      ctx.clip();\n    }\n\n    if (!hasRefs) {\n      var isMeetMinY = meetOrSlice === 'meet' && scaleMin === scaleY;\n      var isSliceMaxY = meetOrSlice === 'slice' && scaleMax === scaleY;\n      var isMeetMinX = meetOrSlice === 'meet' && scaleMin === scaleX;\n      var isSliceMaxX = meetOrSlice === 'slice' && scaleMax === scaleX;\n\n      if (align.startsWith('xMid') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width / 2.0 - finalDesiredWidth / 2.0, 0);\n      }\n\n      if (align.endsWith('YMid') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height / 2.0 - finalDesiredHeight / 2.0);\n      }\n\n      if (align.startsWith('xMax') && (isMeetMinY || isSliceMaxY)) {\n        ctx.translate(width - finalDesiredWidth, 0);\n      }\n\n      if (align.endsWith('YMax') && (isMeetMinX || isSliceMaxX)) {\n        ctx.translate(0, height - finalDesiredHeight);\n      }\n    } // scale\n\n\n    switch (true) {\n      case align === 'none':\n        ctx.scale(scaleX, scaleY);\n        break;\n\n      case meetOrSlice === 'meet':\n        ctx.scale(scaleMin, scaleMin);\n        break;\n\n      case meetOrSlice === 'slice':\n        ctx.scale(scaleMax, scaleMax);\n        break;\n    } // translate\n\n\n    ctx.translate(-minX, -minY);\n  }\n\n  start(element) {\n    var {\n      enableRedraw = false,\n      ignoreMouse = false,\n      ignoreAnimation = false,\n      ignoreDimensions = false,\n      ignoreClear = false,\n      forceRedraw,\n      scaleWidth,\n      scaleHeight,\n      offsetX,\n      offsetY\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var {\n      FRAMERATE,\n      mouse\n    } = this;\n    var frameDuration = 1000 / FRAMERATE;\n    this.frameDuration = frameDuration;\n    this.readyPromise = new Promise(resolve => {\n      this.resolveReady = resolve;\n    });\n\n    if (this.isReady()) {\n      this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n    }\n\n    if (!enableRedraw) {\n      return;\n    }\n\n    var now = Date.now();\n    var then = now;\n    var delta = 0;\n\n    var tick = () => {\n      now = Date.now();\n      delta = now - then;\n\n      if (delta >= frameDuration) {\n        then = now - delta % frameDuration;\n\n        if (this.shouldUpdate(ignoreAnimation, forceRedraw)) {\n          this.render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY);\n          mouse.runEvents();\n        }\n      }\n\n      this.intervalId = requestAnimationFrame(tick);\n    };\n\n    if (!ignoreMouse) {\n      mouse.start();\n    }\n\n    this.intervalId = requestAnimationFrame(tick);\n  }\n\n  stop() {\n    if (this.intervalId) {\n      requestAnimationFrame.cancel(this.intervalId);\n      this.intervalId = null;\n    }\n\n    this.mouse.stop();\n  }\n\n  shouldUpdate(ignoreAnimation, forceRedraw) {\n    // need update from animations?\n    if (!ignoreAnimation) {\n      var {\n        frameDuration\n      } = this;\n      var shouldUpdate = this.animations.reduce((shouldUpdate, animation) => animation.update(frameDuration) || shouldUpdate, false);\n\n      if (shouldUpdate) {\n        return true;\n      }\n    } // need update from redraw?\n\n\n    if (typeof forceRedraw === 'function' && forceRedraw()) {\n      return true;\n    }\n\n    if (!this.isReadyLock && this.isReady()) {\n      return true;\n    } // need update from mouse events?\n\n\n    if (this.mouse.hasEvents()) {\n      return true;\n    }\n\n    return false;\n  }\n\n  render(element, ignoreDimensions, ignoreClear, scaleWidth, scaleHeight, offsetX, offsetY) {\n    var {\n      CLIENT_WIDTH,\n      CLIENT_HEIGHT,\n      viewPort,\n      ctx,\n      isFirstRender\n    } = this;\n    var canvas = ctx.canvas;\n    viewPort.clear();\n\n    if (canvas.width && canvas.height) {\n      viewPort.setCurrent(canvas.width, canvas.height);\n    } else {\n      viewPort.setCurrent(CLIENT_WIDTH, CLIENT_HEIGHT);\n    }\n\n    var widthStyle = element.getStyle('width');\n    var heightStyle = element.getStyle('height');\n\n    if (!ignoreDimensions && (isFirstRender || typeof scaleWidth !== 'number' && typeof scaleHeight !== 'number')) {\n      // set canvas size\n      if (widthStyle.hasValue()) {\n        canvas.width = widthStyle.getPixels('x');\n\n        if (canvas.style) {\n          canvas.style.width = \"\".concat(canvas.width, \"px\");\n        }\n      }\n\n      if (heightStyle.hasValue()) {\n        canvas.height = heightStyle.getPixels('y');\n\n        if (canvas.style) {\n          canvas.style.height = \"\".concat(canvas.height, \"px\");\n        }\n      }\n    }\n\n    var cWidth = canvas.clientWidth || canvas.width;\n    var cHeight = canvas.clientHeight || canvas.height;\n\n    if (ignoreDimensions && widthStyle.hasValue() && heightStyle.hasValue()) {\n      cWidth = widthStyle.getPixels('x');\n      cHeight = heightStyle.getPixels('y');\n    }\n\n    viewPort.setCurrent(cWidth, cHeight);\n\n    if (typeof offsetX === 'number') {\n      element.getAttribute('x', true).setValue(offsetX);\n    }\n\n    if (typeof offsetY === 'number') {\n      element.getAttribute('y', true).setValue(offsetY);\n    }\n\n    if (typeof scaleWidth === 'number' || typeof scaleHeight === 'number') {\n      var viewBox = toNumbers(element.getAttribute('viewBox').getString());\n      var xRatio = 0;\n      var yRatio = 0;\n\n      if (typeof scaleWidth === 'number') {\n        var _widthStyle = element.getStyle('width');\n\n        if (_widthStyle.hasValue()) {\n          xRatio = _widthStyle.getPixels('x') / scaleWidth;\n        } else if (!isNaN(viewBox[2])) {\n          xRatio = viewBox[2] / scaleWidth;\n        }\n      }\n\n      if (typeof scaleHeight === 'number') {\n        var _heightStyle = element.getStyle('height');\n\n        if (_heightStyle.hasValue()) {\n          yRatio = _heightStyle.getPixels('y') / scaleHeight;\n        } else if (!isNaN(viewBox[3])) {\n          yRatio = viewBox[3] / scaleHeight;\n        }\n      }\n\n      if (!xRatio) {\n        xRatio = yRatio;\n      }\n\n      if (!yRatio) {\n        yRatio = xRatio;\n      }\n\n      element.getAttribute('width', true).setValue(scaleWidth);\n      element.getAttribute('height', true).setValue(scaleHeight);\n      var transformStyle = element.getStyle('transform', true, true);\n      transformStyle.setValue(\"\".concat(transformStyle.getString(), \" scale(\").concat(1.0 / xRatio, \", \").concat(1.0 / yRatio, \")\"));\n    } // clear and render\n\n\n    if (!ignoreClear) {\n      ctx.clearRect(0, 0, cWidth, cHeight);\n    }\n\n    element.render(ctx);\n\n    if (isFirstRender) {\n      this.isFirstRender = false;\n    }\n  }\n\n}\nScreen.defaultWindow = defaultWindow;\nScreen.defaultFetch = defaultFetch$1;\n\nvar {\n  defaultFetch\n} = Screen;\nvar DefaultDOMParser = typeof DOMParser !== 'undefined' ? DOMParser : null;\nclass Parser {\n  constructor() {\n    var {\n      fetch = defaultFetch,\n      DOMParser = DefaultDOMParser\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.fetch = fetch;\n    this.DOMParser = DOMParser;\n  }\n\n  parse(resource) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (resource.startsWith('<')) {\n        return _this.parseFromString(resource);\n      }\n\n      return _this.load(resource);\n    })();\n  }\n\n  parseFromString(xml) {\n    var parser = new this.DOMParser();\n\n    try {\n      return this.checkDocument(parser.parseFromString(xml, 'image/svg+xml'));\n    } catch (err) {\n      return this.checkDocument(parser.parseFromString(xml, 'text/xml'));\n    }\n  }\n\n  checkDocument(document) {\n    var parserError = document.getElementsByTagName('parsererror')[0];\n\n    if (parserError) {\n      throw new Error(parserError.textContent);\n    }\n\n    return document;\n  }\n\n  load(url) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var response = yield _this2.fetch(url);\n      var xml = yield response.text();\n      return _this2.parseFromString(xml);\n    })();\n  }\n\n}\n\nclass Translate {\n  constructor(_, point) {\n    this.type = 'translate';\n    this.point = null;\n    this.point = Point.parse(point);\n  }\n\n  apply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(x || 0.0, y || 0.0);\n  }\n\n  unapply(ctx) {\n    var {\n      x,\n      y\n    } = this.point;\n    ctx.translate(-1.0 * x || 0.0, -1.0 * y || 0.0);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.point;\n    point.applyTransform([1, 0, 0, 1, x || 0.0, y || 0.0]);\n  }\n\n}\n\nclass Rotate {\n  constructor(document, rotate, transformOrigin) {\n    this.type = 'rotate';\n    this.angle = null;\n    this.originX = null;\n    this.originY = null;\n    this.cx = 0;\n    this.cy = 0;\n    var numbers = toNumbers(rotate);\n    this.angle = new Property(document, 'angle', numbers[0]);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n    this.cx = numbers[1] || 0;\n    this.cy = numbers[2] || 0;\n  }\n\n  apply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      cx,\n      cy,\n      originX,\n      originY,\n      angle\n    } = this;\n    var tx = cx + originX.getPixels('x');\n    var ty = cy + originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.rotate(-1.0 * angle.getRadians());\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      cx,\n      cy,\n      angle\n    } = this;\n    var rad = angle.getRadians();\n    point.applyTransform([1, 0, 0, 1, cx || 0.0, cy || 0.0 // this.p.y\n    ]);\n    point.applyTransform([Math.cos(rad), Math.sin(rad), -Math.sin(rad), Math.cos(rad), 0, 0]);\n    point.applyTransform([1, 0, 0, 1, -cx || 0.0, -cy || 0.0 // -this.p.y\n    ]);\n  }\n\n}\n\nclass Scale {\n  constructor(_, scale, transformOrigin) {\n    this.type = 'scale';\n    this.scale = null;\n    this.originX = null;\n    this.originY = null;\n    var scaleSize = Point.parseScale(scale); // Workaround for node-canvas\n\n    if (scaleSize.x === 0 || scaleSize.y === 0) {\n      scaleSize.x = PSEUDO_ZERO;\n      scaleSize.y = PSEUDO_ZERO;\n    }\n\n    this.scale = scaleSize;\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(x, y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      scale: {\n        x,\n        y\n      },\n      originX,\n      originY\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.scale(1.0 / x, 1.0 / y || x);\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    var {\n      x,\n      y\n    } = this.scale;\n    point.applyTransform([x || 0.0, 0, 0, y || 0.0, 0, 0]);\n  }\n\n}\n\nclass Matrix {\n  constructor(_, matrix, transformOrigin) {\n    this.type = 'matrix';\n    this.matrix = [];\n    this.originX = null;\n    this.originY = null;\n    this.matrix = toNumbers(matrix);\n    this.originX = transformOrigin[0];\n    this.originY = transformOrigin[1];\n  }\n\n  apply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n    ctx.translate(-tx, -ty);\n  }\n\n  unapply(ctx) {\n    var {\n      originX,\n      originY,\n      matrix\n    } = this;\n    var a = matrix[0];\n    var b = matrix[2];\n    var c = matrix[4];\n    var d = matrix[1];\n    var e = matrix[3];\n    var f = matrix[5];\n    var g = 0.0;\n    var h = 0.0;\n    var i = 1.0;\n    var det = 1 / (a * (e * i - f * h) - b * (d * i - f * g) + c * (d * h - e * g));\n    var tx = originX.getPixels('x');\n    var ty = originY.getPixels('y');\n    ctx.translate(tx, ty);\n    ctx.transform(det * (e * i - f * h), det * (f * g - d * i), det * (c * h - b * i), det * (a * i - c * g), det * (b * f - c * e), det * (c * d - a * f));\n    ctx.translate(-tx, -ty);\n  }\n\n  applyToPoint(point) {\n    point.applyTransform(this.matrix);\n  }\n\n}\n\nclass Skew extends Matrix {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skew';\n    this.angle = null;\n    this.angle = new Property(document, 'angle', skew);\n  }\n\n}\n\nclass SkewX extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewX';\n    this.matrix = [1, 0, Math.tan(this.angle.getRadians()), 1, 0, 0];\n  }\n\n}\n\nclass SkewY extends Skew {\n  constructor(document, skew, transformOrigin) {\n    super(document, skew, transformOrigin);\n    this.type = 'skewY';\n    this.matrix = [1, Math.tan(this.angle.getRadians()), 0, 1, 0, 0];\n  }\n\n}\n\nfunction parseTransforms(transform) {\n  return compressSpaces(transform).trim().replace(/\\)([a-zA-Z])/g, ') $1').replace(/\\)(\\s?,\\s?)/g, ') ').split(/\\s(?=[a-z])/);\n}\n\nfunction parseTransform(transform) {\n  var [type, value] = transform.split('(');\n  return [type.trim(), value.trim().replace(')', '')];\n}\n\nclass Transform {\n  constructor(document, transform, transformOrigin) {\n    this.document = document;\n    this.transforms = [];\n    var data = parseTransforms(transform);\n    data.forEach(transform => {\n      if (transform === 'none') {\n        return;\n      }\n\n      var [type, value] = parseTransform(transform);\n      var TransformType = Transform.transformTypes[type];\n\n      if (typeof TransformType !== 'undefined') {\n        this.transforms.push(new TransformType(this.document, value, transformOrigin));\n      }\n    });\n  }\n\n  static fromElement(document, element) {\n    var transformStyle = element.getStyle('transform', false, true);\n    var [transformOriginXProperty, transformOriginYProperty = transformOriginXProperty] = element.getStyle('transform-origin', false, true).split();\n    var transformOrigin = [transformOriginXProperty, transformOriginYProperty];\n\n    if (transformStyle.hasValue()) {\n      return new Transform(document, transformStyle.getString(), transformOrigin);\n    }\n\n    return null;\n  }\n\n  apply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].apply(ctx);\n    }\n  }\n\n  unapply(ctx) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = len - 1; i >= 0; i--) {\n      transforms[i].unapply(ctx);\n    }\n  } // TODO: applyToPoint unused ... remove?\n\n\n  applyToPoint(point) {\n    var {\n      transforms\n    } = this;\n    var len = transforms.length;\n\n    for (var i = 0; i < len; i++) {\n      transforms[i].applyToPoint(point);\n    }\n  }\n\n}\nTransform.transformTypes = {\n  translate: Translate,\n  rotate: Rotate,\n  scale: Scale,\n  matrix: Matrix,\n  skewX: SkewX,\n  skewY: SkewY\n};\n\nclass Element {\n  constructor(document, node) {\n    var captureTextNodes = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.document = document;\n    this.node = node;\n    this.captureTextNodes = captureTextNodes;\n    this.attributes = {};\n    this.styles = {};\n    this.stylesSpecificity = {};\n    this.animationFrozen = false;\n    this.animationFrozenValue = '';\n    this.parent = null;\n    this.children = [];\n\n    if (!node || node.nodeType !== 1) {\n      // ELEMENT_NODE\n      return;\n    } // add attributes\n\n\n    Array.from(node.attributes).forEach(attribute => {\n      var nodeName = normalizeAttributeName(attribute.nodeName);\n      this.attributes[nodeName] = new Property(document, nodeName, attribute.value);\n    });\n    this.addStylesFromStyleDefinition(); // add inline styles\n\n    if (this.getAttribute('style').hasValue()) {\n      var styles = this.getAttribute('style').getString().split(';').map(_ => _.trim());\n      styles.forEach(style => {\n        if (!style) {\n          return;\n        }\n\n        var [name, value] = style.split(':').map(_ => _.trim());\n        this.styles[name] = new Property(document, name, value);\n      });\n    }\n\n    var {\n      definitions\n    } = document;\n    var id = this.getAttribute('id'); // add id\n\n    if (id.hasValue()) {\n      if (!definitions[id.getString()]) {\n        definitions[id.getString()] = this;\n      }\n    }\n\n    Array.from(node.childNodes).forEach(childNode => {\n      if (childNode.nodeType === 1) {\n        this.addChild(childNode); // ELEMENT_NODE\n      } else if (captureTextNodes && (childNode.nodeType === 3 || childNode.nodeType === 4)) {\n        var textNode = document.createTextNode(childNode);\n\n        if (textNode.getText().length > 0) {\n          this.addChild(textNode); // TEXT_NODE\n        }\n      }\n    });\n  }\n\n  getAttribute(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var attr = this.attributes[name];\n\n    if (!attr && createIfNotExists) {\n      var _attr = new Property(this.document, name, '');\n\n      this.attributes[name] = _attr;\n      return _attr;\n    }\n\n    return attr || Property.empty(this.document);\n  }\n\n  getHrefAttribute() {\n    for (var key in this.attributes) {\n      if (key === 'href' || key.endsWith(':href')) {\n        return this.attributes[key];\n      }\n    }\n\n    return Property.empty(this.document);\n  }\n\n  getStyle(name) {\n    var createIfNotExists = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var skipAncestors = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var style = this.styles[name];\n\n    if (style) {\n      return style;\n    }\n\n    var attr = this.getAttribute(name);\n\n    if (attr !== null && attr !== void 0 && attr.hasValue()) {\n      this.styles[name] = attr; // move up to me to cache\n\n      return attr;\n    }\n\n    if (!skipAncestors) {\n      var {\n        parent\n      } = this;\n\n      if (parent) {\n        var parentStyle = parent.getStyle(name);\n\n        if (parentStyle !== null && parentStyle !== void 0 && parentStyle.hasValue()) {\n          return parentStyle;\n        }\n      }\n    }\n\n    if (createIfNotExists) {\n      var _style = new Property(this.document, name, '');\n\n      this.styles[name] = _style;\n      return _style;\n    }\n\n    return style || Property.empty(this.document);\n  }\n\n  render(ctx) {\n    // don't render display=none\n    // don't render visibility=hidden\n    if (this.getStyle('display').getString() === 'none' || this.getStyle('visibility').getString() === 'hidden') {\n      return;\n    }\n\n    ctx.save();\n\n    if (this.getStyle('mask').hasValue()) {\n      // mask\n      var mask = this.getStyle('mask').getDefinition();\n\n      if (mask) {\n        this.applyEffects(ctx);\n        mask.apply(ctx, this);\n      }\n    } else if (this.getStyle('filter').getValue('none') !== 'none') {\n      // filter\n      var filter = this.getStyle('filter').getDefinition();\n\n      if (filter) {\n        this.applyEffects(ctx);\n        filter.apply(ctx, this);\n      }\n    } else {\n      this.setContext(ctx);\n      this.renderChildren(ctx);\n      this.clearContext(ctx);\n    }\n\n    ctx.restore();\n  }\n\n  setContext(_) {// NO RENDER\n  }\n\n  applyEffects(ctx) {\n    // transform\n    var transform = Transform.fromElement(this.document, this);\n\n    if (transform) {\n      transform.apply(ctx);\n    } // clip\n\n\n    var clipPathStyleProp = this.getStyle('clip-path', false, true);\n\n    if (clipPathStyleProp.hasValue()) {\n      var clip = clipPathStyleProp.getDefinition();\n\n      if (clip) {\n        clip.apply(ctx);\n      }\n    }\n  }\n\n  clearContext(_) {// NO RENDER\n  }\n\n  renderChildren(ctx) {\n    this.children.forEach(child => {\n      child.render(ctx);\n    });\n  }\n\n  addChild(childNode) {\n    var child = childNode instanceof Element ? childNode : this.document.createElement(childNode);\n    child.parent = this;\n\n    if (!Element.ignoreChildTypes.includes(child.type)) {\n      this.children.push(child);\n    }\n  }\n\n  matchesSelector(selector) {\n    var _node$getAttribute;\n\n    var {\n      node\n    } = this;\n\n    if (typeof node.matches === 'function') {\n      return node.matches(selector);\n    }\n\n    var styleClasses = (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'class');\n\n    if (!styleClasses || styleClasses === '') {\n      return false;\n    }\n\n    return styleClasses.split(' ').some(styleClass => \".\".concat(styleClass) === selector);\n  }\n\n  addStylesFromStyleDefinition() {\n    var {\n      styles,\n      stylesSpecificity\n    } = this.document;\n\n    for (var selector in styles) {\n      if (!selector.startsWith('@') && this.matchesSelector(selector)) {\n        var style = styles[selector];\n        var specificity = stylesSpecificity[selector];\n\n        if (style) {\n          for (var name in style) {\n            var existingSpecificity = this.stylesSpecificity[name];\n\n            if (typeof existingSpecificity === 'undefined') {\n              existingSpecificity = '000';\n            }\n\n            if (specificity >= existingSpecificity) {\n              this.styles[name] = style[name];\n              this.stylesSpecificity[name] = specificity;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  removeStyles(element, ignoreStyles) {\n    var toRestore = ignoreStyles.reduce((toRestore, name) => {\n      var styleProp = element.getStyle(name);\n\n      if (!styleProp.hasValue()) {\n        return toRestore;\n      }\n\n      var value = styleProp.getString();\n      styleProp.setValue('');\n      return [...toRestore, [name, value]];\n    }, []);\n    return toRestore;\n  }\n\n  restoreStyles(element, styles) {\n    styles.forEach(_ref => {\n      var [name, value] = _ref;\n      element.getStyle(name, true).setValue(value);\n    });\n  }\n\n  isFirstChild() {\n    var _this$parent;\n\n    return ((_this$parent = this.parent) === null || _this$parent === void 0 ? void 0 : _this$parent.children.indexOf(this)) === 0;\n  }\n\n}\nElement.ignoreChildTypes = ['title'];\n\nclass UnknownElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n  }\n\n}\n\nfunction wrapFontFamily(fontFamily) {\n  var trimmed = fontFamily.trim();\n  return /^('|\")/.test(trimmed) ? trimmed : \"\\\"\".concat(trimmed, \"\\\"\");\n}\n\nfunction prepareFontFamily(fontFamily) {\n  return typeof process === 'undefined' ? fontFamily : fontFamily.trim().split(',').map(wrapFontFamily).join(',');\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-style\r\n * @param fontStyle\r\n * @returns CSS font style.\r\n */\n\n\nfunction prepareFontStyle(fontStyle) {\n  if (!fontStyle) {\n    return '';\n  }\n\n  var targetFontStyle = fontStyle.trim().toLowerCase();\n\n  switch (targetFontStyle) {\n    case 'normal':\n    case 'italic':\n    case 'oblique':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontStyle;\n\n    default:\n      if (/^oblique\\s+(-|)\\d+deg$/.test(targetFontStyle)) {\n        return targetFontStyle;\n      }\n\n      return '';\n  }\n}\n/**\r\n * https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight\r\n * @param fontWeight\r\n * @returns CSS font weight.\r\n */\n\n\nfunction prepareFontWeight(fontWeight) {\n  if (!fontWeight) {\n    return '';\n  }\n\n  var targetFontWeight = fontWeight.trim().toLowerCase();\n\n  switch (targetFontWeight) {\n    case 'normal':\n    case 'bold':\n    case 'lighter':\n    case 'bolder':\n    case 'inherit':\n    case 'initial':\n    case 'unset':\n      return targetFontWeight;\n\n    default:\n      if (/^[\\d.]+$/.test(targetFontWeight)) {\n        return targetFontWeight;\n      }\n\n      return '';\n  }\n}\n\nclass Font {\n  constructor(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit) {\n    var inheritFont = inherit ? typeof inherit === 'string' ? Font.parse(inherit) : inherit : {};\n    this.fontFamily = fontFamily || inheritFont.fontFamily;\n    this.fontSize = fontSize || inheritFont.fontSize;\n    this.fontStyle = fontStyle || inheritFont.fontStyle;\n    this.fontWeight = fontWeight || inheritFont.fontWeight;\n    this.fontVariant = fontVariant || inheritFont.fontVariant;\n  }\n\n  static parse() {\n    var font = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    var inherit = arguments.length > 1 ? arguments[1] : undefined;\n    var fontStyle = '';\n    var fontVariant = '';\n    var fontWeight = '';\n    var fontSize = '';\n    var fontFamily = '';\n    var parts = compressSpaces(font).trim().split(' ');\n    var set = {\n      fontSize: false,\n      fontStyle: false,\n      fontWeight: false,\n      fontVariant: false\n    };\n    parts.forEach(part => {\n      switch (true) {\n        case !set.fontStyle && Font.styles.includes(part):\n          if (part !== 'inherit') {\n            fontStyle = part;\n          }\n\n          set.fontStyle = true;\n          break;\n\n        case !set.fontVariant && Font.variants.includes(part):\n          if (part !== 'inherit') {\n            fontVariant = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          break;\n\n        case !set.fontWeight && Font.weights.includes(part):\n          if (part !== 'inherit') {\n            fontWeight = part;\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          break;\n\n        case !set.fontSize:\n          if (part !== 'inherit') {\n            [fontSize] = part.split('/');\n          }\n\n          set.fontStyle = true;\n          set.fontVariant = true;\n          set.fontWeight = true;\n          set.fontSize = true;\n          break;\n\n        default:\n          if (part !== 'inherit') {\n            fontFamily += part;\n          }\n\n      }\n    });\n    return new Font(fontStyle, fontVariant, fontWeight, fontSize, fontFamily, inherit);\n  }\n\n  toString() {\n    return [prepareFontStyle(this.fontStyle), this.fontVariant, prepareFontWeight(this.fontWeight), this.fontSize, // Wrap fontFamily only on nodejs and only for canvas.ctx\n    prepareFontFamily(this.fontFamily)].join(' ').trim();\n  }\n\n}\nFont.styles = 'normal|italic|oblique|inherit';\nFont.variants = 'normal|small-caps|inherit';\nFont.weights = 'normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit';\n\nclass BoundingBox {\n  constructor() {\n    var x1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Number.NaN;\n    var y1 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.NaN;\n    var x2 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.NaN;\n    var y2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Number.NaN;\n    this.x1 = x1;\n    this.y1 = y1;\n    this.x2 = x2;\n    this.y2 = y2;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  get x() {\n    return this.x1;\n  }\n\n  get y() {\n    return this.y1;\n  }\n\n  get width() {\n    return this.x2 - this.x1;\n  }\n\n  get height() {\n    return this.y2 - this.y1;\n  }\n\n  addPoint(x, y) {\n    if (typeof x !== 'undefined') {\n      if (isNaN(this.x1) || isNaN(this.x2)) {\n        this.x1 = x;\n        this.x2 = x;\n      }\n\n      if (x < this.x1) {\n        this.x1 = x;\n      }\n\n      if (x > this.x2) {\n        this.x2 = x;\n      }\n    }\n\n    if (typeof y !== 'undefined') {\n      if (isNaN(this.y1) || isNaN(this.y2)) {\n        this.y1 = y;\n        this.y2 = y;\n      }\n\n      if (y < this.y1) {\n        this.y1 = y;\n      }\n\n      if (y > this.y2) {\n        this.y2 = y;\n      }\n    }\n  }\n\n  addX(x) {\n    this.addPoint(x, null);\n  }\n\n  addY(y) {\n    this.addPoint(null, y);\n  }\n\n  addBoundingBox(boundingBox) {\n    if (!boundingBox) {\n      return;\n    }\n\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = boundingBox;\n    this.addPoint(x1, y1);\n    this.addPoint(x2, y2);\n  }\n\n  sumCubic(t, p0, p1, p2, p3) {\n    return Math.pow(1 - t, 3) * p0 + 3 * Math.pow(1 - t, 2) * t * p1 + 3 * (1 - t) * Math.pow(t, 2) * p2 + Math.pow(t, 3) * p3;\n  }\n\n  bezierCurveAdd(forX, p0, p1, p2, p3) {\n    var b = 6 * p0 - 12 * p1 + 6 * p2;\n    var a = -3 * p0 + 9 * p1 - 9 * p2 + 3 * p3;\n    var c = 3 * p1 - 3 * p0;\n\n    if (a === 0) {\n      if (b === 0) {\n        return;\n      }\n\n      var t = -c / b;\n\n      if (0 < t && t < 1) {\n        if (forX) {\n          this.addX(this.sumCubic(t, p0, p1, p2, p3));\n        } else {\n          this.addY(this.sumCubic(t, p0, p1, p2, p3));\n        }\n      }\n\n      return;\n    }\n\n    var b2ac = Math.pow(b, 2) - 4 * c * a;\n\n    if (b2ac < 0) {\n      return;\n    }\n\n    var t1 = (-b + Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t1 && t1 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t1, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t1, p0, p1, p2, p3));\n      }\n    }\n\n    var t2 = (-b - Math.sqrt(b2ac)) / (2 * a);\n\n    if (0 < t2 && t2 < 1) {\n      if (forX) {\n        this.addX(this.sumCubic(t2, p0, p1, p2, p3));\n      } else {\n        this.addY(this.sumCubic(t2, p0, p1, p2, p3));\n      }\n    }\n  } // from http://blog.hackers-cafe.net/2009/06/how-to-calculate-bezier-curves-bounding.html\n\n\n  addBezierCurve(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y) {\n    this.addPoint(p0x, p0y);\n    this.addPoint(p3x, p3y);\n    this.bezierCurveAdd(true, p0x, p1x, p2x, p3x);\n    this.bezierCurveAdd(false, p0y, p1y, p2y, p3y);\n  }\n\n  addQuadraticCurve(p0x, p0y, p1x, p1y, p2x, p2y) {\n    var cp1x = p0x + 2 / 3 * (p1x - p0x); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp1y = p0y + 2 / 3 * (p1y - p0y); // CP1 = QP0 + 2/3 *(QP1-QP0)\n\n    var cp2x = cp1x + 1 / 3 * (p2x - p0x); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    var cp2y = cp1y + 1 / 3 * (p2y - p0y); // CP2 = CP1 + 1/3 *(QP2-QP0)\n\n    this.addBezierCurve(p0x, p0y, cp1x, cp2x, cp1y, cp2y, p2x, p2y);\n  }\n\n  isPointInBox(x, y) {\n    var {\n      x1,\n      y1,\n      x2,\n      y2\n    } = this;\n    return x1 <= x && x <= x2 && y1 <= y && y <= y2;\n  }\n\n}\n\nclass PathParser extends SVGPathData {\n  constructor(path) {\n    super(path // Fix spaces after signs.\n    .replace(/([+\\-.])\\s+/gm, '$1') // Remove invalid part.\n    .replace(/[^MmZzLlHhVvCcSsQqTtAae\\d\\s.,+-].*/g, ''));\n    this.control = null;\n    this.start = null;\n    this.current = null;\n    this.command = null;\n    this.commands = this.commands;\n    this.i = -1;\n    this.previousCommand = null;\n    this.points = [];\n    this.angles = [];\n  }\n\n  reset() {\n    this.i = -1;\n    this.command = null;\n    this.previousCommand = null;\n    this.start = new Point(0, 0);\n    this.control = new Point(0, 0);\n    this.current = new Point(0, 0);\n    this.points = [];\n    this.angles = [];\n  }\n\n  isEnd() {\n    var {\n      i,\n      commands\n    } = this;\n    return i >= commands.length - 1;\n  }\n\n  next() {\n    var command = this.commands[++this.i];\n    this.previousCommand = this.command;\n    this.command = command;\n    return command;\n  }\n\n  getPoint() {\n    var xProp = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'x';\n    var yProp = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'y';\n    var point = new Point(this.command[xProp], this.command[yProp]);\n    return this.makeAbsolute(point);\n  }\n\n  getAsControlPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.control = point;\n    return point;\n  }\n\n  getAsCurrentPoint(xProp, yProp) {\n    var point = this.getPoint(xProp, yProp);\n    this.current = point;\n    return point;\n  }\n\n  getReflectedControlPoint() {\n    var previousCommand = this.previousCommand.type;\n\n    if (previousCommand !== SVGPathData.CURVE_TO && previousCommand !== SVGPathData.SMOOTH_CURVE_TO && previousCommand !== SVGPathData.QUAD_TO && previousCommand !== SVGPathData.SMOOTH_QUAD_TO) {\n      return this.current;\n    } // reflect point\n\n\n    var {\n      current: {\n        x: cx,\n        y: cy\n      },\n      control: {\n        x: ox,\n        y: oy\n      }\n    } = this;\n    var point = new Point(2 * cx - ox, 2 * cy - oy);\n    return point;\n  }\n\n  makeAbsolute(point) {\n    if (this.command.relative) {\n      var {\n        x,\n        y\n      } = this.current;\n      point.x += x;\n      point.y += y;\n    }\n\n    return point;\n  }\n\n  addMarker(point, from, priorTo) {\n    var {\n      points,\n      angles\n    } = this; // if the last angle isn't filled in because we didn't have this point yet ...\n\n    if (priorTo && angles.length > 0 && !angles[angles.length - 1]) {\n      angles[angles.length - 1] = points[points.length - 1].angleTo(priorTo);\n    }\n\n    this.addMarkerAngle(point, from ? from.angleTo(point) : null);\n  }\n\n  addMarkerAngle(point, angle) {\n    this.points.push(point);\n    this.angles.push(angle);\n  }\n\n  getMarkerPoints() {\n    return this.points;\n  }\n\n  getMarkerAngles() {\n    var {\n      angles\n    } = this;\n    var len = angles.length;\n\n    for (var i = 0; i < len; i++) {\n      if (!angles[i]) {\n        for (var j = i + 1; j < len; j++) {\n          if (angles[j]) {\n            angles[i] = angles[j];\n            break;\n          }\n        }\n      }\n    }\n\n    return angles;\n  }\n\n}\n\nclass RenderedElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.modifiedEmSizeStack = false;\n  }\n\n  calculateOpacity() {\n    var opacity = 1.0; // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n\n    var element = this;\n\n    while (element) {\n      var opacityStyle = element.getStyle('opacity', false, true); // no ancestors on style call\n\n      if (opacityStyle.hasValue(true)) {\n        opacity *= opacityStyle.getNumber();\n      }\n\n      element = element.parent;\n    }\n\n    return opacity;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n    if (!fromMeasure) {\n      // causes stack overflow when measuring text with gradients\n      // fill\n      var fillStyleProp = this.getStyle('fill');\n      var fillOpacityStyleProp = this.getStyle('fill-opacity');\n      var strokeStyleProp = this.getStyle('stroke');\n      var strokeOpacityProp = this.getStyle('stroke-opacity');\n\n      if (fillStyleProp.isUrlDefinition()) {\n        var fillStyle = fillStyleProp.getFillStyleDefinition(this, fillOpacityStyleProp);\n\n        if (fillStyle) {\n          ctx.fillStyle = fillStyle;\n        }\n      } else if (fillStyleProp.hasValue()) {\n        if (fillStyleProp.getString() === 'currentColor') {\n          fillStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _fillStyle = fillStyleProp.getColor();\n\n        if (_fillStyle !== 'inherit') {\n          ctx.fillStyle = _fillStyle === 'none' ? 'rgba(0,0,0,0)' : _fillStyle;\n        }\n      }\n\n      if (fillOpacityStyleProp.hasValue()) {\n        var _fillStyle2 = new Property(this.document, 'fill', ctx.fillStyle).addOpacity(fillOpacityStyleProp).getColor();\n\n        ctx.fillStyle = _fillStyle2;\n      } // stroke\n\n\n      if (strokeStyleProp.isUrlDefinition()) {\n        var strokeStyle = strokeStyleProp.getFillStyleDefinition(this, strokeOpacityProp);\n\n        if (strokeStyle) {\n          ctx.strokeStyle = strokeStyle;\n        }\n      } else if (strokeStyleProp.hasValue()) {\n        if (strokeStyleProp.getString() === 'currentColor') {\n          strokeStyleProp.setValue(this.getStyle('color').getColor());\n        }\n\n        var _strokeStyle = strokeStyleProp.getString();\n\n        if (_strokeStyle !== 'inherit') {\n          ctx.strokeStyle = _strokeStyle === 'none' ? 'rgba(0,0,0,0)' : _strokeStyle;\n        }\n      }\n\n      if (strokeOpacityProp.hasValue()) {\n        var _strokeStyle2 = new Property(this.document, 'stroke', ctx.strokeStyle).addOpacity(strokeOpacityProp).getString();\n\n        ctx.strokeStyle = _strokeStyle2;\n      }\n\n      var strokeWidthStyleProp = this.getStyle('stroke-width');\n\n      if (strokeWidthStyleProp.hasValue()) {\n        var newLineWidth = strokeWidthStyleProp.getPixels();\n        ctx.lineWidth = !newLineWidth ? PSEUDO_ZERO // browsers don't respect 0 (or node-canvas? :-)\n        : newLineWidth;\n      }\n\n      var strokeLinecapStyleProp = this.getStyle('stroke-linecap');\n      var strokeLinejoinStyleProp = this.getStyle('stroke-linejoin');\n      var strokeMiterlimitProp = this.getStyle('stroke-miterlimit'); // NEED TEST\n      // const pointOrderStyleProp = this.getStyle('paint-order');\n\n      var strokeDasharrayStyleProp = this.getStyle('stroke-dasharray');\n      var strokeDashoffsetProp = this.getStyle('stroke-dashoffset');\n\n      if (strokeLinecapStyleProp.hasValue()) {\n        ctx.lineCap = strokeLinecapStyleProp.getString();\n      }\n\n      if (strokeLinejoinStyleProp.hasValue()) {\n        ctx.lineJoin = strokeLinejoinStyleProp.getString();\n      }\n\n      if (strokeMiterlimitProp.hasValue()) {\n        ctx.miterLimit = strokeMiterlimitProp.getNumber();\n      } // NEED TEST\n      // if (pointOrderStyleProp.hasValue()) {\n      // \t// ?\n      // \tctx.paintOrder = pointOrderStyleProp.getValue();\n      // }\n\n\n      if (strokeDasharrayStyleProp.hasValue() && strokeDasharrayStyleProp.getString() !== 'none') {\n        var gaps = toNumbers(strokeDasharrayStyleProp.getString());\n\n        if (typeof ctx.setLineDash !== 'undefined') {\n          ctx.setLineDash(gaps);\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDash !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDash = gaps;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDash !== 'undefined' && !(gaps.length === 1 && gaps[0] === 0)) {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDash = gaps;\n            }\n\n        var offset = strokeDashoffsetProp.getPixels();\n\n        if (typeof ctx.lineDashOffset !== 'undefined') {\n          ctx.lineDashOffset = offset;\n        } else // @ts-expect-error Handle browser prefix.\n          if (typeof ctx.webkitLineDashOffset !== 'undefined') {\n            // @ts-expect-error Handle browser prefix.\n            ctx.webkitLineDashOffset = offset;\n          } else // @ts-expect-error Handle browser prefix.\n            if (typeof ctx.mozDashOffset !== 'undefined') {\n              // @ts-expect-error Handle browser prefix.\n              ctx.mozDashOffset = offset;\n            }\n      }\n    } // font\n\n\n    this.modifiedEmSizeStack = false;\n\n    if (typeof ctx.font !== 'undefined') {\n      var fontStyleProp = this.getStyle('font');\n      var fontStyleStyleProp = this.getStyle('font-style');\n      var fontVariantStyleProp = this.getStyle('font-variant');\n      var fontWeightStyleProp = this.getStyle('font-weight');\n      var fontSizeStyleProp = this.getStyle('font-size');\n      var fontFamilyStyleProp = this.getStyle('font-family');\n      var font = new Font(fontStyleStyleProp.getString(), fontVariantStyleProp.getString(), fontWeightStyleProp.getString(), fontSizeStyleProp.hasValue() ? \"\".concat(fontSizeStyleProp.getPixels(true), \"px\") : '', fontFamilyStyleProp.getString(), Font.parse(fontStyleProp.getString(), ctx.font));\n      fontStyleStyleProp.setValue(font.fontStyle);\n      fontVariantStyleProp.setValue(font.fontVariant);\n      fontWeightStyleProp.setValue(font.fontWeight);\n      fontSizeStyleProp.setValue(font.fontSize);\n      fontFamilyStyleProp.setValue(font.fontFamily);\n      ctx.font = font.toString();\n\n      if (fontSizeStyleProp.isPixels()) {\n        this.document.emSize = fontSizeStyleProp.getPixels();\n        this.modifiedEmSizeStack = true;\n      }\n    }\n\n    if (!fromMeasure) {\n      // effects\n      this.applyEffects(ctx); // opacity\n\n      ctx.globalAlpha = this.calculateOpacity();\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n\n    if (this.modifiedEmSizeStack) {\n      this.document.popEmSize();\n    }\n  }\n\n}\n\nclass PathElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'path';\n    this.pathParser = null;\n    this.pathParser = new PathParser(this.getAttribute('d').getString());\n  }\n\n  path(ctx) {\n    var {\n      pathParser\n    } = this;\n    var boundingBox = new BoundingBox();\n    pathParser.reset();\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    while (!pathParser.isEnd()) {\n      switch (pathParser.next().type) {\n        case PathParser.MOVE_TO:\n          this.pathM(ctx, boundingBox);\n          break;\n\n        case PathParser.LINE_TO:\n          this.pathL(ctx, boundingBox);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          this.pathH(ctx, boundingBox);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          this.pathV(ctx, boundingBox);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          this.pathS(ctx, boundingBox);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(ctx, boundingBox);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          this.pathT(ctx, boundingBox);\n          break;\n\n        case PathParser.ARC:\n          this.pathA(ctx, boundingBox);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          this.pathZ(ctx, boundingBox);\n          break;\n      }\n    }\n\n    return boundingBox;\n  }\n\n  getBoundingBox(_) {\n    return this.path();\n  }\n\n  getMarkers() {\n    var {\n      pathParser\n    } = this;\n    var points = pathParser.getMarkerPoints();\n    var angles = pathParser.getMarkerAngles();\n    var markers = points.map((point, i) => [point, angles[i]]);\n    return markers;\n  }\n\n  renderChildren(ctx) {\n    this.path(ctx);\n    this.document.screen.mouse.checkPath(this, ctx);\n    var fillRuleStyleProp = this.getStyle('fill-rule');\n\n    if (ctx.fillStyle !== '') {\n      if (fillRuleStyleProp.getString('inherit') !== 'inherit') {\n        ctx.fill(fillRuleStyleProp.getString());\n      } else {\n        ctx.fill();\n      }\n    }\n\n    if (ctx.strokeStyle !== '') {\n      if (this.getAttribute('vector-effect').getString() === 'non-scaling-stroke') {\n        ctx.save();\n        ctx.setTransform(1, 0, 0, 1, 0, 0);\n        ctx.stroke();\n        ctx.restore();\n      } else {\n        ctx.stroke();\n      }\n    }\n\n    var markers = this.getMarkers();\n\n    if (markers) {\n      var markersLastIndex = markers.length - 1;\n      var markerStartStyleProp = this.getStyle('marker-start');\n      var markerMidStyleProp = this.getStyle('marker-mid');\n      var markerEndStyleProp = this.getStyle('marker-end');\n\n      if (markerStartStyleProp.isUrlDefinition()) {\n        var marker = markerStartStyleProp.getDefinition();\n        var [point, angle] = markers[0];\n        marker.render(ctx, point, angle);\n      }\n\n      if (markerMidStyleProp.isUrlDefinition()) {\n        var _marker = markerMidStyleProp.getDefinition();\n\n        for (var i = 1; i < markersLastIndex; i++) {\n          var [_point, _angle] = markers[i];\n\n          _marker.render(ctx, _point, _angle);\n        }\n      }\n\n      if (markerEndStyleProp.isUrlDefinition()) {\n        var _marker2 = markerEndStyleProp.getDefinition();\n\n        var [_point2, _angle2] = markers[markersLastIndex];\n\n        _marker2.render(ctx, _point2, _angle2);\n      }\n    }\n  }\n\n  static pathM(pathParser) {\n    var point = pathParser.getAsCurrentPoint();\n    pathParser.start = pathParser.current;\n    return {\n      point\n    };\n  }\n\n  pathM(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      point\n    } = PathElement.pathM(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.moveTo(x, y);\n    }\n  }\n\n  static pathL(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point\n    };\n  }\n\n  pathL(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathL(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathH(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point((command.relative ? current.x : 0) + command.x, current.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathH(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathH(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathV(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var point = new Point(current.x, (command.relative ? current.y : 0) + command.y);\n    pathParser.current = point;\n    return {\n      current,\n      point\n    };\n  }\n\n  pathV(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point\n    } = PathElement.pathV(pathParser);\n    var {\n      x,\n      y\n    } = point;\n    pathParser.addMarker(point, current);\n    boundingBox.addPoint(x, y);\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n    }\n  }\n\n  static pathC(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getPoint('x1', 'y1');\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathC(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathS(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var point = pathParser.getReflectedControlPoint();\n    var controlPoint = pathParser.getAsControlPoint('x2', 'y2');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathS(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, point);\n    boundingBox.addBezierCurve(current.x, current.y, point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.bezierCurveTo(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathQ(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getAsControlPoint('x1', 'y1');\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathQ(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathT(pathParser) {\n    var {\n      current\n    } = pathParser;\n    var controlPoint = pathParser.getReflectedControlPoint();\n    pathParser.control = controlPoint;\n    var currentPoint = pathParser.getAsCurrentPoint();\n    return {\n      current,\n      controlPoint,\n      currentPoint\n    };\n  }\n\n  pathT(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      current,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    pathParser.addMarker(currentPoint, controlPoint, controlPoint);\n    boundingBox.addQuadraticCurve(current.x, current.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n\n    if (ctx) {\n      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    }\n  }\n\n  static pathA(pathParser) {\n    var {\n      current,\n      command\n    } = pathParser;\n    var {\n      rX,\n      rY,\n      xRot,\n      lArcFlag,\n      sweepFlag\n    } = command;\n    var xAxisRotation = xRot * (Math.PI / 180.0);\n    var currentPoint = pathParser.getAsCurrentPoint(); // Conversion from endpoint to center parameterization\n    // http://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes\n    // x1', y1'\n\n    var currp = new Point(Math.cos(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.sin(xAxisRotation) * (current.y - currentPoint.y) / 2.0, -Math.sin(xAxisRotation) * (current.x - currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * (current.y - currentPoint.y) / 2.0); // adjust radii\n\n    var l = Math.pow(currp.x, 2) / Math.pow(rX, 2) + Math.pow(currp.y, 2) / Math.pow(rY, 2);\n\n    if (l > 1) {\n      rX *= Math.sqrt(l);\n      rY *= Math.sqrt(l);\n    } // cx', cy'\n\n\n    var s = (lArcFlag === sweepFlag ? -1 : 1) * Math.sqrt((Math.pow(rX, 2) * Math.pow(rY, 2) - Math.pow(rX, 2) * Math.pow(currp.y, 2) - Math.pow(rY, 2) * Math.pow(currp.x, 2)) / (Math.pow(rX, 2) * Math.pow(currp.y, 2) + Math.pow(rY, 2) * Math.pow(currp.x, 2)));\n\n    if (isNaN(s)) {\n      s = 0;\n    }\n\n    var cpp = new Point(s * rX * currp.y / rY, s * -rY * currp.x / rX); // cx, cy\n\n    var centp = new Point((current.x + currentPoint.x) / 2.0 + Math.cos(xAxisRotation) * cpp.x - Math.sin(xAxisRotation) * cpp.y, (current.y + currentPoint.y) / 2.0 + Math.sin(xAxisRotation) * cpp.x + Math.cos(xAxisRotation) * cpp.y); // initial angle\n\n    var a1 = vectorsAngle([1, 0], [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY]); // θ1\n    // angle delta\n\n    var u = [(currp.x - cpp.x) / rX, (currp.y - cpp.y) / rY];\n    var v = [(-currp.x - cpp.x) / rX, (-currp.y - cpp.y) / rY];\n    var ad = vectorsAngle(u, v); // Δθ\n\n    if (vectorsRatio(u, v) <= -1) {\n      ad = Math.PI;\n    }\n\n    if (vectorsRatio(u, v) >= 1) {\n      ad = 0;\n    }\n\n    return {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    };\n  }\n\n  pathA(ctx, boundingBox) {\n    var {\n      pathParser\n    } = this;\n    var {\n      currentPoint,\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser); // for markers\n\n    var dir = 1 - sweepFlag ? 1.0 : -1.0;\n    var ah = a1 + dir * (ad / 2.0);\n    var halfWay = new Point(centp.x + rX * Math.cos(ah), centp.y + rY * Math.sin(ah));\n    pathParser.addMarkerAngle(halfWay, ah - dir * Math.PI / 2);\n    pathParser.addMarkerAngle(currentPoint, ah - dir * Math.PI);\n    boundingBox.addPoint(currentPoint.x, currentPoint.y); // TODO: this is too naive, make it better\n\n    if (ctx && !isNaN(a1) && !isNaN(ad)) {\n      var r = rX > rY ? rX : rY;\n      var sx = rX > rY ? 1 : rX / rY;\n      var sy = rX > rY ? rY / rX : 1;\n      ctx.translate(centp.x, centp.y);\n      ctx.rotate(xAxisRotation);\n      ctx.scale(sx, sy);\n      ctx.arc(0, 0, r, a1, a1 + ad, Boolean(1 - sweepFlag));\n      ctx.scale(1 / sx, 1 / sy);\n      ctx.rotate(-xAxisRotation);\n      ctx.translate(-centp.x, -centp.y);\n    }\n  }\n\n  static pathZ(pathParser) {\n    pathParser.current = pathParser.start;\n  }\n\n  pathZ(ctx, boundingBox) {\n    PathElement.pathZ(this.pathParser);\n\n    if (ctx) {\n      // only close path if it is not a straight line\n      if (boundingBox.x1 !== boundingBox.x2 && boundingBox.y1 !== boundingBox.y2) {\n        ctx.closePath();\n      }\n    }\n  }\n\n}\n\nclass GlyphElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'glyph';\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    this.unicode = this.getAttribute('unicode').getString();\n    this.arabicForm = this.getAttribute('arabic-form').getString();\n  }\n\n}\n\nclass TextElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TextElement ? true : captureTextNodes);\n    this.type = 'text';\n    this.x = 0;\n    this.y = 0;\n    this.measureCache = -1;\n  }\n\n  setContext(ctx) {\n    var fromMeasure = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    super.setContext(ctx, fromMeasure);\n    var textBaseline = this.getStyle('dominant-baseline').getTextBaseline() || this.getStyle('alignment-baseline').getTextBaseline();\n\n    if (textBaseline) {\n      ctx.textBaseline = textBaseline;\n    }\n  }\n\n  initializeCoordinates() {\n    this.x = 0;\n    this.y = 0;\n    this.leafTexts = [];\n    this.textChunkStart = 0;\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n  }\n\n  getBoundingBox(ctx) {\n    if (this.type !== 'text') {\n      return this.getTElementBoundingBox(ctx);\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx);\n    var boundingBox = null; // then calculate bounding box\n\n    this.children.forEach((_, i) => {\n      var childBoundingBox = this.getChildBoundingBox(ctx, this, this, i);\n\n      if (!boundingBox) {\n        boundingBox = childBoundingBox;\n      } else {\n        boundingBox.addBoundingBox(childBoundingBox);\n      }\n    });\n    return boundingBox;\n  }\n\n  getFontSize() {\n    var {\n      document,\n      parent\n    } = this;\n    var inheritFontSize = Font.parse(document.ctx.font).fontSize;\n    var fontSize = parent.getStyle('font-size').getNumber(inheritFontSize);\n    return fontSize;\n  }\n\n  getTElementBoundingBox(ctx) {\n    var fontSize = this.getFontSize();\n    return new BoundingBox(this.x, this.y - fontSize, this.x + this.measureText(ctx), this.y);\n  }\n\n  getGlyph(font, text, i) {\n    var char = text[i];\n    var glyph = null;\n\n    if (font.isArabic) {\n      var len = text.length;\n      var prevChar = text[i - 1];\n      var nextChar = text[i + 1];\n      var arabicForm = 'isolated';\n\n      if ((i === 0 || prevChar === ' ') && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'terminal';\n      }\n\n      if (i > 0 && prevChar !== ' ' && i < len - 1 && nextChar !== ' ') {\n        arabicForm = 'medial';\n      }\n\n      if (i > 0 && prevChar !== ' ' && (i === len - 1 || nextChar === ' ')) {\n        arabicForm = 'initial';\n      }\n\n      if (typeof font.glyphs[char] !== 'undefined') {\n        // NEED TEST\n        var maybeGlyph = font.glyphs[char];\n        glyph = maybeGlyph instanceof GlyphElement ? maybeGlyph : maybeGlyph[arabicForm];\n      }\n    } else {\n      glyph = font.glyphs[char];\n    }\n\n    if (!glyph) {\n      glyph = font.missingGlyph;\n    }\n\n    return glyph;\n  }\n\n  getText() {\n    return '';\n  }\n\n  getTextFromNode(node) {\n    var textNode = node || this.node;\n    var childNodes = Array.from(textNode.parentNode.childNodes);\n    var index = childNodes.indexOf(textNode);\n    var lastIndex = childNodes.length - 1;\n    var text = compressSpaces( // textNode.value\n    // || textNode.text\n    textNode.textContent || '');\n\n    if (index === 0) {\n      text = trimLeft(text);\n    }\n\n    if (index === lastIndex) {\n      text = trimRight(text);\n    }\n\n    return text;\n  }\n\n  renderChildren(ctx) {\n    if (this.type !== 'text') {\n      this.renderTElementChildren(ctx);\n      return;\n    } // first, calculate child positions\n\n\n    this.initializeCoordinates();\n    this.adjustChildCoordinatesRecursive(ctx); // then render\n\n    this.children.forEach((_, i) => {\n      this.renderChild(ctx, this, this, i);\n    });\n    var {\n      mouse\n    } = this.document.screen; // Do not calc bounding box if mouse is not working.\n\n    if (mouse.isWorking()) {\n      mouse.checkBoundingBox(this, this.getBoundingBox(ctx));\n    }\n  }\n\n  renderTElementChildren(ctx) {\n    var {\n      document,\n      parent\n    } = this;\n    var renderText = this.getText();\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var {\n        unitsPerEm\n      } = customFont.fontFace;\n      var ctxFont = Font.parse(document.ctx.font);\n      var fontSize = parent.getStyle('font-size').getNumber(ctxFont.fontSize);\n      var fontStyle = parent.getStyle('font-style').getString(ctxFont.fontStyle);\n      var scale = fontSize / unitsPerEm;\n      var text = customFont.isRTL ? renderText.split('').reverse().join('') : renderText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        ctx.translate(this.x, this.y);\n        ctx.scale(scale, -scale);\n        var lw = ctx.lineWidth;\n        ctx.lineWidth = ctx.lineWidth * unitsPerEm / fontSize;\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, .4, 1, 0, 0);\n        }\n\n        glyph.render(ctx);\n\n        if (fontStyle === 'italic') {\n          ctx.transform(1, 0, -.4, 1, 0, 0);\n        }\n\n        ctx.lineWidth = lw;\n        ctx.scale(1 / scale, -1 / scale);\n        ctx.translate(-this.x, -this.y);\n        this.x += fontSize * (glyph.horizAdvX || customFont.horizAdvX) / unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          this.x += dx[i];\n        }\n      }\n\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = this; // NEED TEST\n    // if (ctx.paintOrder === 'stroke') {\n    // \tif (ctx.strokeStyle) {\n    // \t\tctx.strokeText(renderText, x, y);\n    // \t}\n    // \tif (ctx.fillStyle) {\n    // \t\tctx.fillText(renderText, x, y);\n    // \t}\n    // } else {\n\n    if (ctx.fillStyle) {\n      ctx.fillText(renderText, x, y);\n    }\n\n    if (ctx.strokeStyle) {\n      ctx.strokeText(renderText, x, y);\n    } // }\n\n  }\n\n  applyAnchoring() {\n    if (this.textChunkStart >= this.leafTexts.length) {\n      return;\n    } // This is basically the \"Apply anchoring\" part of https://www.w3.org/TR/SVG2/text.html#TextLayoutAlgorithm.\n    // The difference is that we apply the anchoring as soon as a chunk is finished. This saves some extra looping.\n    // Vertical text is not supported.\n\n\n    var firstElement = this.leafTexts[this.textChunkStart];\n    var textAnchor = firstElement.getStyle('text-anchor').getString('start');\n    var isRTL = false; // we treat RTL like LTR\n\n    var shift = 0;\n\n    if (textAnchor === 'start' && !isRTL || textAnchor === 'end' && isRTL) {\n      shift = firstElement.x - this.minX;\n    } else if (textAnchor === 'end' && !isRTL || textAnchor === 'start' && isRTL) {\n      shift = firstElement.x - this.maxX;\n    } else {\n      shift = firstElement.x - (this.minX + this.maxX) / 2;\n    }\n\n    for (var i = this.textChunkStart; i < this.leafTexts.length; i++) {\n      this.leafTexts[i].x += shift;\n    } // start new chunk\n\n\n    this.minX = Number.POSITIVE_INFINITY;\n    this.maxX = Number.NEGATIVE_INFINITY;\n    this.textChunkStart = this.leafTexts.length;\n  }\n\n  adjustChildCoordinatesRecursive(ctx) {\n    this.children.forEach((_, i) => {\n      this.adjustChildCoordinatesRecursiveCore(ctx, this, this, i);\n    });\n    this.applyAnchoring();\n  }\n\n  adjustChildCoordinatesRecursiveCore(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (child.children.length > 0) {\n      child.children.forEach((_, i) => {\n        textParent.adjustChildCoordinatesRecursiveCore(ctx, textParent, child, i);\n      });\n    } else {\n      // only leafs are relevant\n      this.adjustChildCoordinates(ctx, textParent, parent, i);\n    }\n  }\n\n  adjustChildCoordinates(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n\n    if (typeof child.measureText !== 'function') {\n      return child;\n    }\n\n    ctx.save();\n    child.setContext(ctx, true);\n    var xAttr = child.getAttribute('x');\n    var yAttr = child.getAttribute('y');\n    var dxAttr = child.getAttribute('dx');\n    var dyAttr = child.getAttribute('dy');\n    var customFont = child.getStyle('font-family').getDefinition();\n    var isRTL = Boolean(customFont) && customFont.isRTL;\n\n    if (i === 0) {\n      // First children inherit attributes from parent(s). Positional attributes\n      // are only inherited from a parent to it's first child.\n      if (!xAttr.hasValue()) {\n        xAttr.setValue(child.getInheritedAttribute('x'));\n      }\n\n      if (!yAttr.hasValue()) {\n        yAttr.setValue(child.getInheritedAttribute('y'));\n      }\n\n      if (!dxAttr.hasValue()) {\n        dxAttr.setValue(child.getInheritedAttribute('dx'));\n      }\n\n      if (!dyAttr.hasValue()) {\n        dyAttr.setValue(child.getInheritedAttribute('dy'));\n      }\n    }\n\n    var width = child.measureText(ctx);\n\n    if (isRTL) {\n      textParent.x -= width;\n    }\n\n    if (xAttr.hasValue()) {\n      // an \"x\" attribute marks the start of a new chunk\n      textParent.applyAnchoring();\n      child.x = xAttr.getPixels('x');\n\n      if (dxAttr.hasValue()) {\n        child.x += dxAttr.getPixels('x');\n      }\n    } else {\n      if (dxAttr.hasValue()) {\n        textParent.x += dxAttr.getPixels('x');\n      }\n\n      child.x = textParent.x;\n    }\n\n    textParent.x = child.x;\n\n    if (!isRTL) {\n      textParent.x += width;\n    }\n\n    if (yAttr.hasValue()) {\n      child.y = yAttr.getPixels('y');\n\n      if (dyAttr.hasValue()) {\n        child.y += dyAttr.getPixels('y');\n      }\n    } else {\n      if (dyAttr.hasValue()) {\n        textParent.y += dyAttr.getPixels('y');\n      }\n\n      child.y = textParent.y;\n    }\n\n    textParent.y = child.y; // update the current chunk and it's bounds\n\n    textParent.leafTexts.push(child);\n    textParent.minX = Math.min(textParent.minX, child.x, child.x + width);\n    textParent.maxX = Math.max(textParent.maxX, child.x, child.x + width);\n    child.clearContext(ctx);\n    ctx.restore();\n    return child;\n  }\n\n  getChildBoundingBox(ctx, textParent, parent, i) {\n    var child = parent.children[i]; // not a text node?\n\n    if (typeof child.getBoundingBox !== 'function') {\n      return null;\n    }\n\n    var boundingBox = child.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return null;\n    }\n\n    child.children.forEach((_, i) => {\n      var childBoundingBox = textParent.getChildBoundingBox(ctx, textParent, child, i);\n      boundingBox.addBoundingBox(childBoundingBox);\n    });\n    return boundingBox;\n  }\n\n  renderChild(ctx, textParent, parent, i) {\n    var child = parent.children[i];\n    child.render(ctx);\n    child.children.forEach((_, i) => {\n      textParent.renderChild(ctx, textParent, child, i);\n    });\n  }\n\n  measureText(ctx) {\n    var {\n      measureCache\n    } = this;\n\n    if (~measureCache) {\n      return measureCache;\n    }\n\n    var renderText = this.getText();\n    var measure = this.measureTargetText(ctx, renderText);\n    this.measureCache = measure;\n    return measure;\n  }\n\n  measureTargetText(ctx, targetText) {\n    if (!targetText.length) {\n      return 0;\n    }\n\n    var {\n      parent\n    } = this;\n    var customFont = parent.getStyle('font-family').getDefinition();\n\n    if (customFont) {\n      var fontSize = this.getFontSize();\n      var text = customFont.isRTL ? targetText.split('').reverse().join('') : targetText;\n      var dx = toNumbers(parent.getAttribute('dx').getString());\n      var len = text.length;\n      var _measure = 0;\n\n      for (var i = 0; i < len; i++) {\n        var glyph = this.getGlyph(customFont, text, i);\n        _measure += (glyph.horizAdvX || customFont.horizAdvX) * fontSize / customFont.fontFace.unitsPerEm;\n\n        if (typeof dx[i] !== 'undefined' && !isNaN(dx[i])) {\n          _measure += dx[i];\n        }\n      }\n\n      return _measure;\n    }\n\n    if (!ctx.measureText) {\n      return targetText.length * 10;\n    }\n\n    ctx.save();\n    this.setContext(ctx, true);\n    var {\n      width: measure\n    } = ctx.measureText(targetText);\n    this.clearContext(ctx);\n    ctx.restore();\n    return measure;\n  }\n  /**\r\n   * Inherits positional attributes from {@link TextElement} parent(s). Attributes\r\n   * are only inherited from a parent to its first child.\r\n   * @param name - The attribute name.\r\n   * @returns The attribute value or null.\r\n   */\n\n\n  getInheritedAttribute(name) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias,consistent-this\n    var current = this;\n\n    while (current instanceof TextElement && current.isFirstChild()) {\n      var parentAttr = current.parent.getAttribute(name);\n\n      if (parentAttr.hasValue(true)) {\n        return parentAttr.getValue('0');\n      }\n\n      current = current.parent;\n    }\n\n    return null;\n  }\n\n}\n\nclass TSpanElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, new.target === TSpanElement ? true : captureTextNodes);\n    this.type = 'tspan'; // if this node has children, then they own the text\n\n    this.text = this.children.length > 0 ? '' : this.getTextFromNode();\n  }\n\n  getText() {\n    return this.text;\n  }\n\n}\n\nclass TextNode extends TSpanElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'textNode';\n  }\n\n}\n\nclass SVGElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'svg';\n    this.root = false;\n  }\n\n  setContext(ctx) {\n    var _this$node$parentNode;\n\n    var {\n      document\n    } = this;\n    var {\n      screen,\n      window\n    } = document;\n    var canvas = ctx.canvas;\n    screen.setDefaults(ctx);\n\n    if (canvas.style && typeof ctx.font !== 'undefined' && window && typeof window.getComputedStyle !== 'undefined') {\n      ctx.font = window.getComputedStyle(canvas).getPropertyValue('font');\n      var fontSizeProp = new Property(document, 'fontSize', Font.parse(ctx.font).fontSize);\n\n      if (fontSizeProp.hasValue()) {\n        document.rootEmSize = fontSizeProp.getPixels('y');\n        document.emSize = document.rootEmSize;\n      }\n    } // create new view port\n\n\n    if (!this.getAttribute('x').hasValue()) {\n      this.getAttribute('x', true).setValue(0);\n    }\n\n    if (!this.getAttribute('y').hasValue()) {\n      this.getAttribute('y', true).setValue(0);\n    }\n\n    var {\n      width,\n      height\n    } = screen.viewPort;\n\n    if (!this.getStyle('width').hasValue()) {\n      this.getStyle('width', true).setValue('100%');\n    }\n\n    if (!this.getStyle('height').hasValue()) {\n      this.getStyle('height', true).setValue('100%');\n    }\n\n    if (!this.getStyle('color').hasValue()) {\n      this.getStyle('color', true).setValue('black');\n    }\n\n    var refXAttr = this.getAttribute('refX');\n    var refYAttr = this.getAttribute('refY');\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var viewBox = viewBoxAttr.hasValue() ? toNumbers(viewBoxAttr.getString()) : null;\n    var clip = !this.root && this.getStyle('overflow').getValue('hidden') !== 'visible';\n    var minX = 0;\n    var minY = 0;\n    var clipX = 0;\n    var clipY = 0;\n\n    if (viewBox) {\n      minX = viewBox[0];\n      minY = viewBox[1];\n    }\n\n    if (!this.root) {\n      width = this.getStyle('width').getPixels('x');\n      height = this.getStyle('height').getPixels('y');\n\n      if (this.type === 'marker') {\n        clipX = minX;\n        clipY = minY;\n        minX = 0;\n        minY = 0;\n      }\n    }\n\n    screen.viewPort.setCurrent(width, height); // Default value of transform-origin is center only for root SVG elements\n    // https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/transform-origin\n\n    if (this.node // is not temporary SVGElement\n    && (!this.parent || ((_this$node$parentNode = this.node.parentNode) === null || _this$node$parentNode === void 0 ? void 0 : _this$node$parentNode.nodeName) === 'foreignObject') && this.getStyle('transform', false, true).hasValue() && !this.getStyle('transform-origin', false, true).hasValue()) {\n      this.getStyle('transform-origin', true, true).setValue('50% 50%');\n    }\n\n    super.setContext(ctx);\n    ctx.translate(this.getAttribute('x').getPixels('x'), this.getAttribute('y').getPixels('y'));\n\n    if (viewBox) {\n      width = viewBox[2];\n      height = viewBox[3];\n    }\n\n    document.setViewBox({\n      ctx,\n      aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n      width: screen.viewPort.width,\n      desiredWidth: width,\n      height: screen.viewPort.height,\n      desiredHeight: height,\n      minX,\n      minY,\n      refX: refXAttr.getValue(),\n      refY: refYAttr.getValue(),\n      clip,\n      clipX,\n      clipY\n    });\n\n    if (viewBox) {\n      screen.viewPort.removeCurrent();\n      screen.viewPort.setCurrent(width, height);\n    }\n  }\n\n  clearContext(ctx) {\n    super.clearContext(ctx);\n    this.document.screen.viewPort.removeCurrent();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var widthAttr = this.getAttribute('width', true);\n    var heightAttr = this.getAttribute('height', true);\n    var viewBoxAttr = this.getAttribute('viewBox');\n    var styleAttr = this.getAttribute('style');\n    var originWidth = widthAttr.getNumber(0);\n    var originHeight = heightAttr.getNumber(0);\n\n    if (preserveAspectRatio) {\n      if (typeof preserveAspectRatio === 'string') {\n        this.getAttribute('preserveAspectRatio', true).setValue(preserveAspectRatio);\n      } else {\n        var preserveAspectRatioAttr = this.getAttribute('preserveAspectRatio');\n\n        if (preserveAspectRatioAttr.hasValue()) {\n          preserveAspectRatioAttr.setValue(preserveAspectRatioAttr.getString().replace(/^\\s*(\\S.*\\S)\\s*$/, '$1'));\n        }\n      }\n    }\n\n    widthAttr.setValue(width);\n    heightAttr.setValue(height);\n\n    if (!viewBoxAttr.hasValue()) {\n      viewBoxAttr.setValue(\"0 0 \".concat(originWidth || width, \" \").concat(originHeight || height));\n    }\n\n    if (styleAttr.hasValue()) {\n      var widthStyle = this.getStyle('width');\n      var heightStyle = this.getStyle('height');\n\n      if (widthStyle.hasValue()) {\n        widthStyle.setValue(\"\".concat(width, \"px\"));\n      }\n\n      if (heightStyle.hasValue()) {\n        heightStyle.setValue(\"\".concat(height, \"px\"));\n      }\n    }\n  }\n\n}\n\nclass RectElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'rect';\n  }\n\n  path(ctx) {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width', false, true).getPixels('x');\n    var height = this.getStyle('height', false, true).getPixels('y');\n    var rxAttr = this.getAttribute('rx');\n    var ryAttr = this.getAttribute('ry');\n    var rx = rxAttr.getPixels('x');\n    var ry = ryAttr.getPixels('y');\n\n    if (rxAttr.hasValue() && !ryAttr.hasValue()) {\n      ry = rx;\n    }\n\n    if (ryAttr.hasValue() && !rxAttr.hasValue()) {\n      rx = ry;\n    }\n\n    rx = Math.min(rx, width / 2.0);\n    ry = Math.min(ry, height / 2.0);\n\n    if (ctx) {\n      var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n      ctx.beginPath(); // always start the path so we don't fill prior paths\n\n      if (height > 0 && width > 0) {\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x + width - rx, y);\n        ctx.bezierCurveTo(x + width - rx + KAPPA * rx, y, x + width, y + ry - KAPPA * ry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + KAPPA * ry, x + width - rx + KAPPA * rx, y + height, x + width - rx, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - KAPPA * rx, y + height, x, y + height - ry + KAPPA * ry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - KAPPA * ry, x + rx - KAPPA * rx, y, x + rx, y);\n        ctx.closePath();\n      }\n    }\n\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass CircleElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'circle';\n  }\n\n  path(ctx) {\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n    var r = this.getAttribute('r').getPixels();\n\n    if (ctx && r > 0) {\n      ctx.beginPath();\n      ctx.arc(cx, cy, r, 0, Math.PI * 2, false);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - r, cy - r, cx + r, cy + r);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass EllipseElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'ellipse';\n  }\n\n  path(ctx) {\n    var KAPPA = 4 * ((Math.sqrt(2) - 1) / 3);\n    var rx = this.getAttribute('rx').getPixels('x');\n    var ry = this.getAttribute('ry').getPixels('y');\n    var cx = this.getAttribute('cx').getPixels('x');\n    var cy = this.getAttribute('cy').getPixels('y');\n\n    if (ctx && rx > 0 && ry > 0) {\n      ctx.beginPath();\n      ctx.moveTo(cx + rx, cy);\n      ctx.bezierCurveTo(cx + rx, cy + KAPPA * ry, cx + KAPPA * rx, cy + ry, cx, cy + ry);\n      ctx.bezierCurveTo(cx - KAPPA * rx, cy + ry, cx - rx, cy + KAPPA * ry, cx - rx, cy);\n      ctx.bezierCurveTo(cx - rx, cy - KAPPA * ry, cx - KAPPA * rx, cy - ry, cx, cy - ry);\n      ctx.bezierCurveTo(cx + KAPPA * rx, cy - ry, cx + rx, cy - KAPPA * ry, cx + rx, cy);\n      ctx.closePath();\n    }\n\n    return new BoundingBox(cx - rx, cy - ry, cx + rx, cy + ry);\n  }\n\n  getMarkers() {\n    return null;\n  }\n\n}\n\nclass LineElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'line';\n  }\n\n  getPoints() {\n    return [new Point(this.getAttribute('x1').getPixels('x'), this.getAttribute('y1').getPixels('y')), new Point(this.getAttribute('x2').getPixels('x'), this.getAttribute('y2').getPixels('y'))];\n  }\n\n  path(ctx) {\n    var [{\n      x: x0,\n      y: y0\n    }, {\n      x: x1,\n      y: y1\n    }] = this.getPoints();\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n      ctx.lineTo(x1, y1);\n    }\n\n    return new BoundingBox(x0, y0, x1, y1);\n  }\n\n  getMarkers() {\n    var [p0, p1] = this.getPoints();\n    var a = p0.angleTo(p1);\n    return [[p0, a], [p1, a]];\n  }\n\n}\n\nclass PolylineElement extends PathElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'polyline';\n    this.points = [];\n    this.points = Point.parsePath(this.getAttribute('points').getString());\n  }\n\n  path(ctx) {\n    var {\n      points\n    } = this;\n    var [{\n      x: x0,\n      y: y0\n    }] = points;\n    var boundingBox = new BoundingBox(x0, y0);\n\n    if (ctx) {\n      ctx.beginPath();\n      ctx.moveTo(x0, y0);\n    }\n\n    points.forEach(_ref => {\n      var {\n        x,\n        y\n      } = _ref;\n      boundingBox.addPoint(x, y);\n\n      if (ctx) {\n        ctx.lineTo(x, y);\n      }\n    });\n    return boundingBox;\n  }\n\n  getMarkers() {\n    var {\n      points\n    } = this;\n    var lastIndex = points.length - 1;\n    var markers = [];\n    points.forEach((point, i) => {\n      if (i === lastIndex) {\n        return;\n      }\n\n      markers.push([point, point.angleTo(points[i + 1])]);\n    });\n\n    if (markers.length > 0) {\n      markers.push([points[points.length - 1], markers[markers.length - 1][1]]);\n    }\n\n    return markers;\n  }\n\n}\n\nclass PolygonElement extends PolylineElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'polygon';\n  }\n\n  path(ctx) {\n    var boundingBox = super.path(ctx);\n    var [{\n      x,\n      y\n    }] = this.points;\n\n    if (ctx) {\n      ctx.lineTo(x, y);\n      ctx.closePath();\n    }\n\n    return boundingBox;\n  }\n\n}\n\nclass PatternElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'pattern';\n  }\n\n  createPattern(ctx, _, parentOpacityProp) {\n    var width = this.getStyle('width').getPixels('x', true);\n    var height = this.getStyle('height').getPixels('y', true); // render me using a temporary svg element\n\n    var patternSvg = new SVGElement(this.document, null);\n    patternSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    patternSvg.attributes.width = new Property(this.document, 'width', \"\".concat(width, \"px\"));\n    patternSvg.attributes.height = new Property(this.document, 'height', \"\".concat(height, \"px\"));\n    patternSvg.attributes.transform = new Property(this.document, 'transform', this.getAttribute('patternTransform').getValue());\n    patternSvg.children = this.children;\n    var patternCanvas = this.document.createCanvas(width, height);\n    var patternCtx = patternCanvas.getContext('2d');\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue() && yAttr.hasValue()) {\n      patternCtx.translate(xAttr.getPixels('x', true), yAttr.getPixels('y', true));\n    }\n\n    if (parentOpacityProp.hasValue()) {\n      this.styles['fill-opacity'] = parentOpacityProp;\n    } else {\n      Reflect.deleteProperty(this.styles, 'fill-opacity');\n    } // render 3x3 grid so when we transform there's no white space on edges\n\n\n    for (var x = -1; x <= 1; x++) {\n      for (var y = -1; y <= 1; y++) {\n        patternCtx.save();\n        patternSvg.attributes.x = new Property(this.document, 'x', x * patternCanvas.width);\n        patternSvg.attributes.y = new Property(this.document, 'y', y * patternCanvas.height);\n        patternSvg.render(patternCtx);\n        patternCtx.restore();\n      }\n    }\n\n    var pattern = ctx.createPattern(patternCanvas, 'repeat');\n    return pattern;\n  }\n\n}\n\nclass MarkerElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'marker';\n  }\n\n  render(ctx, point, angle) {\n    if (!point) {\n      return;\n    }\n\n    var {\n      x,\n      y\n    } = point;\n    var orient = this.getAttribute('orient').getString('auto');\n    var markerUnits = this.getAttribute('markerUnits').getString('strokeWidth');\n    ctx.translate(x, y);\n\n    if (orient === 'auto') {\n      ctx.rotate(angle);\n    }\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(ctx.lineWidth, ctx.lineWidth);\n    }\n\n    ctx.save(); // render me using a temporary svg element\n\n    var markerSvg = new SVGElement(this.document, null);\n    markerSvg.type = this.type;\n    markerSvg.attributes.viewBox = new Property(this.document, 'viewBox', this.getAttribute('viewBox').getValue());\n    markerSvg.attributes.refX = new Property(this.document, 'refX', this.getAttribute('refX').getValue());\n    markerSvg.attributes.refY = new Property(this.document, 'refY', this.getAttribute('refY').getValue());\n    markerSvg.attributes.width = new Property(this.document, 'width', this.getAttribute('markerWidth').getValue());\n    markerSvg.attributes.height = new Property(this.document, 'height', this.getAttribute('markerHeight').getValue());\n    markerSvg.attributes.overflow = new Property(this.document, 'overflow', this.getAttribute('overflow').getValue());\n    markerSvg.attributes.fill = new Property(this.document, 'fill', this.getAttribute('fill').getColor('black'));\n    markerSvg.attributes.stroke = new Property(this.document, 'stroke', this.getAttribute('stroke').getValue('none'));\n    markerSvg.children = this.children;\n    markerSvg.render(ctx);\n    ctx.restore();\n\n    if (markerUnits === 'strokeWidth') {\n      ctx.scale(1 / ctx.lineWidth, 1 / ctx.lineWidth);\n    }\n\n    if (orient === 'auto') {\n      ctx.rotate(-angle);\n    }\n\n    ctx.translate(-x, -y);\n  }\n\n}\n\nclass DefsElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'defs';\n  }\n\n  render() {// NOOP\n  }\n\n}\n\nclass GElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'g';\n  }\n\n  getBoundingBox(ctx) {\n    var boundingBox = new BoundingBox();\n    this.children.forEach(child => {\n      boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n    });\n    return boundingBox;\n  }\n\n}\n\nclass GradientElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.attributesToInherit = ['gradientUnits'];\n    this.stops = [];\n    var {\n      stops,\n      children\n    } = this;\n    children.forEach(child => {\n      if (child.type === 'stop') {\n        stops.push(child);\n      }\n    });\n  }\n\n  getGradientUnits() {\n    return this.getAttribute('gradientUnits').getString('objectBoundingBox');\n  }\n\n  createGradient(ctx, element, parentOpacityProp) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias, consistent-this\n    var stopsContainer = this;\n\n    if (this.getHrefAttribute().hasValue()) {\n      stopsContainer = this.getHrefAttribute().getDefinition();\n      this.inheritStopContainer(stopsContainer);\n    }\n\n    var {\n      stops\n    } = stopsContainer;\n    var gradient = this.getGradient(ctx, element);\n\n    if (!gradient) {\n      return this.addParentOpacity(parentOpacityProp, stops[stops.length - 1].color);\n    }\n\n    stops.forEach(stop => {\n      gradient.addColorStop(stop.offset, this.addParentOpacity(parentOpacityProp, stop.color));\n    });\n\n    if (this.getAttribute('gradientTransform').hasValue()) {\n      // render as transformed pattern on temporary canvas\n      var {\n        document\n      } = this;\n      var {\n        MAX_VIRTUAL_PIXELS,\n        viewPort\n      } = document.screen;\n      var [rootView] = viewPort.viewPorts;\n      var rect = new RectElement(document, null);\n      rect.attributes.x = new Property(document, 'x', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.y = new Property(document, 'y', -MAX_VIRTUAL_PIXELS / 3.0);\n      rect.attributes.width = new Property(document, 'width', MAX_VIRTUAL_PIXELS);\n      rect.attributes.height = new Property(document, 'height', MAX_VIRTUAL_PIXELS);\n      var group = new GElement(document, null);\n      group.attributes.transform = new Property(document, 'transform', this.getAttribute('gradientTransform').getValue());\n      group.children = [rect];\n      var patternSvg = new SVGElement(document, null);\n      patternSvg.attributes.x = new Property(document, 'x', 0);\n      patternSvg.attributes.y = new Property(document, 'y', 0);\n      patternSvg.attributes.width = new Property(document, 'width', rootView.width);\n      patternSvg.attributes.height = new Property(document, 'height', rootView.height);\n      patternSvg.children = [group];\n      var patternCanvas = document.createCanvas(rootView.width, rootView.height);\n      var patternCtx = patternCanvas.getContext('2d');\n      patternCtx.fillStyle = gradient;\n      patternSvg.render(patternCtx);\n      return patternCtx.createPattern(patternCanvas, 'no-repeat');\n    }\n\n    return gradient;\n  }\n\n  inheritStopContainer(stopsContainer) {\n    this.attributesToInherit.forEach(attributeToInherit => {\n      if (!this.getAttribute(attributeToInherit).hasValue() && stopsContainer.getAttribute(attributeToInherit).hasValue()) {\n        this.getAttribute(attributeToInherit, true).setValue(stopsContainer.getAttribute(attributeToInherit).getValue());\n      }\n    });\n  }\n\n  addParentOpacity(parentOpacityProp, color) {\n    if (parentOpacityProp.hasValue()) {\n      var colorProp = new Property(this.document, 'color', color);\n      return colorProp.addOpacity(parentOpacityProp).getColor();\n    }\n\n    return color;\n  }\n\n}\n\nclass LinearGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'linearGradient';\n    this.attributesToInherit.push('x1', 'y1', 'x2', 'y2');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = isBoundingBoxUnits ? element.getBoundingBox(ctx) : null;\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('x1').hasValue() && !this.getAttribute('y1').hasValue() && !this.getAttribute('x2').hasValue() && !this.getAttribute('y2').hasValue()) {\n      this.getAttribute('x1', true).setValue(0);\n      this.getAttribute('y1', true).setValue(0);\n      this.getAttribute('x2', true).setValue(1);\n      this.getAttribute('y2', true).setValue(0);\n    }\n\n    var x1 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x1').getNumber() : this.getAttribute('x1').getPixels('x');\n    var y1 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y1').getNumber() : this.getAttribute('y1').getPixels('y');\n    var x2 = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('x2').getNumber() : this.getAttribute('x2').getPixels('x');\n    var y2 = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('y2').getNumber() : this.getAttribute('y2').getPixels('y');\n\n    if (x1 === x2 && y1 === y2) {\n      return null;\n    }\n\n    return ctx.createLinearGradient(x1, y1, x2, y2);\n  }\n\n}\n\nclass RadialGradientElement extends GradientElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'radialGradient';\n    this.attributesToInherit.push('cx', 'cy', 'r', 'fx', 'fy', 'fr');\n  }\n\n  getGradient(ctx, element) {\n    var isBoundingBoxUnits = this.getGradientUnits() === 'objectBoundingBox';\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (isBoundingBoxUnits && !boundingBox) {\n      return null;\n    }\n\n    if (!this.getAttribute('cx').hasValue()) {\n      this.getAttribute('cx', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('cy').hasValue()) {\n      this.getAttribute('cy', true).setValue('50%');\n    }\n\n    if (!this.getAttribute('r').hasValue()) {\n      this.getAttribute('r', true).setValue('50%');\n    }\n\n    var cx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('cx').getNumber() : this.getAttribute('cx').getPixels('x');\n    var cy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('cy').getNumber() : this.getAttribute('cy').getPixels('y');\n    var fx = cx;\n    var fy = cy;\n\n    if (this.getAttribute('fx').hasValue()) {\n      fx = isBoundingBoxUnits ? boundingBox.x + boundingBox.width * this.getAttribute('fx').getNumber() : this.getAttribute('fx').getPixels('x');\n    }\n\n    if (this.getAttribute('fy').hasValue()) {\n      fy = isBoundingBoxUnits ? boundingBox.y + boundingBox.height * this.getAttribute('fy').getNumber() : this.getAttribute('fy').getPixels('y');\n    }\n\n    var r = isBoundingBoxUnits ? (boundingBox.width + boundingBox.height) / 2.0 * this.getAttribute('r').getNumber() : this.getAttribute('r').getPixels();\n    var fr = this.getAttribute('fr').getPixels();\n    return ctx.createRadialGradient(fx, fy, fr, cx, cy, r);\n  }\n\n}\n\nclass StopElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'stop';\n    var offset = Math.max(0, Math.min(1, this.getAttribute('offset').getNumber()));\n    var stopOpacity = this.getStyle('stop-opacity');\n    var stopColor = this.getStyle('stop-color', true);\n\n    if (stopColor.getString() === '') {\n      stopColor.setValue('#000');\n    }\n\n    if (stopOpacity.hasValue()) {\n      stopColor = stopColor.addOpacity(stopOpacity);\n    }\n\n    this.offset = offset;\n    this.color = stopColor.getColor();\n  }\n\n}\n\nclass AnimateElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'animate';\n    this.duration = 0;\n    this.initialValue = null;\n    this.initialUnits = '';\n    this.removed = false;\n    this.frozen = false;\n    document.screen.animations.push(this);\n    this.begin = this.getAttribute('begin').getMilliseconds();\n    this.maxDuration = this.begin + this.getAttribute('dur').getMilliseconds();\n    this.from = this.getAttribute('from');\n    this.to = this.getAttribute('to');\n    this.values = new Property(document, 'values', null);\n    var valuesAttr = this.getAttribute('values');\n\n    if (valuesAttr.hasValue()) {\n      this.values.setValue(valuesAttr.getString().split(';'));\n    }\n  }\n\n  getProperty() {\n    var attributeType = this.getAttribute('attributeType').getString();\n    var attributeName = this.getAttribute('attributeName').getString();\n\n    if (attributeType === 'CSS') {\n      return this.parent.getStyle(attributeName, true);\n    }\n\n    return this.parent.getAttribute(attributeName, true);\n  }\n\n  calcValue() {\n    var {\n      initialUnits\n    } = this;\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var newValue = from.getNumber() + (to.getNumber() - from.getNumber()) * progress;\n\n    if (initialUnits === '%') {\n      newValue *= 100.0; // numValue() returns 0-1 whereas properties are 0-100\n    }\n\n    return \"\".concat(newValue).concat(initialUnits);\n  }\n\n  update(delta) {\n    var {\n      parent\n    } = this;\n    var prop = this.getProperty(); // set initial value\n\n    if (!this.initialValue) {\n      this.initialValue = prop.getString();\n      this.initialUnits = prop.getUnits();\n    } // if we're past the end time\n\n\n    if (this.duration > this.maxDuration) {\n      var fill = this.getAttribute('fill').getString('remove'); // loop for indefinitely repeating animations\n\n      if (this.getAttribute('repeatCount').getString() === 'indefinite' || this.getAttribute('repeatDur').getString() === 'indefinite') {\n        this.duration = 0;\n      } else if (fill === 'freeze' && !this.frozen) {\n        this.frozen = true;\n        parent.animationFrozen = true;\n        parent.animationFrozenValue = prop.getString();\n      } else if (fill === 'remove' && !this.removed) {\n        this.removed = true;\n        prop.setValue(parent.animationFrozen ? parent.animationFrozenValue : this.initialValue);\n        return true;\n      }\n\n      return false;\n    }\n\n    this.duration += delta; // if we're past the begin time\n\n    var updated = false;\n\n    if (this.begin < this.duration) {\n      var newValue = this.calcValue(); // tween\n\n      var typeAttr = this.getAttribute('type');\n\n      if (typeAttr.hasValue()) {\n        // for transform, etc.\n        var type = typeAttr.getString();\n        newValue = \"\".concat(type, \"(\").concat(newValue, \")\");\n      }\n\n      prop.setValue(newValue);\n      updated = true;\n    }\n\n    return updated;\n  }\n\n  getProgress() {\n    var {\n      document,\n      values\n    } = this;\n    var result = {\n      progress: (this.duration - this.begin) / (this.maxDuration - this.begin)\n    };\n\n    if (values.hasValue()) {\n      var p = result.progress * (values.getValue().length - 1);\n      var lb = Math.floor(p);\n      var ub = Math.ceil(p);\n      result.from = new Property(document, 'from', parseFloat(values.getValue()[lb]));\n      result.to = new Property(document, 'to', parseFloat(values.getValue()[ub]));\n      result.progress = (p - lb) / (ub - lb);\n    } else {\n      result.from = this.from;\n      result.to = this.to;\n    }\n\n    return result;\n  }\n\n}\n\nclass AnimateColorElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateColor';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress();\n    var colorFrom = new RGBColor(from.getColor());\n    var colorTo = new RGBColor(to.getColor());\n\n    if (colorFrom.ok && colorTo.ok) {\n      // tween color linearly\n      var r = colorFrom.r + (colorTo.r - colorFrom.r) * progress;\n      var g = colorFrom.g + (colorTo.g - colorFrom.g) * progress;\n      var b = colorFrom.b + (colorTo.b - colorFrom.b) * progress; // ? alpha\n\n      return \"rgb(\".concat(Math.floor(r), \", \").concat(Math.floor(g), \", \").concat(Math.floor(b), \")\");\n    }\n\n    return this.getAttribute('from').getColor();\n  }\n\n}\n\nclass AnimateTransformElement extends AnimateElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'animateTransform';\n  }\n\n  calcValue() {\n    var {\n      progress,\n      from,\n      to\n    } = this.getProgress(); // tween value linearly\n\n    var transformFrom = toNumbers(from.getString());\n    var transformTo = toNumbers(to.getString());\n    var newValue = transformFrom.map((from, i) => {\n      var to = transformTo[i];\n      return from + (to - from) * progress;\n    }).join(' ');\n    return newValue;\n  }\n\n}\n\nclass FontElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font';\n    this.glyphs = {};\n    this.horizAdvX = this.getAttribute('horiz-adv-x').getNumber();\n    var {\n      definitions\n    } = document;\n    var {\n      children\n    } = this;\n\n    for (var child of children) {\n      switch (child.type) {\n        case 'font-face':\n          {\n            this.fontFace = child;\n            var fontFamilyStyle = child.getStyle('font-family');\n\n            if (fontFamilyStyle.hasValue()) {\n              definitions[fontFamilyStyle.getString()] = this;\n            }\n\n            break;\n          }\n\n        case 'missing-glyph':\n          this.missingGlyph = child;\n          break;\n\n        case 'glyph':\n          {\n            var glyph = child;\n\n            if (glyph.arabicForm) {\n              this.isRTL = true;\n              this.isArabic = true;\n\n              if (typeof this.glyphs[glyph.unicode] === 'undefined') {\n                this.glyphs[glyph.unicode] = {};\n              }\n\n              this.glyphs[glyph.unicode][glyph.arabicForm] = glyph;\n            } else {\n              this.glyphs[glyph.unicode] = glyph;\n            }\n\n            break;\n          }\n      }\n    }\n  }\n\n  render() {// NO RENDER\n  }\n\n}\n\nclass FontFaceElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'font-face';\n    this.ascent = this.getAttribute('ascent').getNumber();\n    this.descent = this.getAttribute('descent').getNumber();\n    this.unitsPerEm = this.getAttribute('units-per-em').getNumber();\n  }\n\n}\n\nclass MissingGlyphElement extends PathElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'missing-glyph';\n    this.horizAdvX = 0;\n  }\n\n}\n\nclass TRefElement extends TextElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'tref';\n  }\n\n  getText() {\n    var element = this.getHrefAttribute().getDefinition();\n\n    if (element) {\n      var firstChild = element.children[0];\n\n      if (firstChild) {\n        return firstChild.getText();\n      }\n    }\n\n    return '';\n  }\n\n}\n\nclass AElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'a';\n    var {\n      childNodes\n    } = node;\n    var firstChild = childNodes[0];\n    var hasText = childNodes.length > 0 && Array.from(childNodes).every(node => node.nodeType === 3);\n    this.hasText = hasText;\n    this.text = hasText ? this.getTextFromNode(firstChild) : '';\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  renderChildren(ctx) {\n    if (this.hasText) {\n      // render as text element\n      super.renderChildren(ctx);\n      var {\n        document,\n        x,\n        y\n      } = this;\n      var {\n        mouse\n      } = document.screen;\n      var fontSize = new Property(document, 'fontSize', Font.parse(document.ctx.font).fontSize); // Do not calc bounding box if mouse is not working.\n\n      if (mouse.isWorking()) {\n        mouse.checkBoundingBox(this, new BoundingBox(x, y - fontSize.getPixels('y'), x + this.measureText(ctx), y));\n      }\n    } else if (this.children.length > 0) {\n      // render as temporary group\n      var g = new GElement(this.document, null);\n      g.children = this.children;\n      g.parent = this;\n      g.render(ctx);\n    }\n  }\n\n  onClick() {\n    var {\n      window\n    } = this.document;\n\n    if (window) {\n      window.open(this.getHrefAttribute().getString());\n    }\n  }\n\n  onMouseMove() {\n    var ctx = this.document.ctx;\n    ctx.canvas.style.cursor = 'pointer';\n  }\n\n}\n\nfunction ownKeys$2(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$2(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$2(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$2(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\nclass TextPathElement extends TextElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'textPath';\n    this.textWidth = 0;\n    this.textHeight = 0;\n    this.pathLength = -1;\n    this.glyphInfo = null;\n    this.letterSpacingCache = [];\n    this.measuresCache = new Map([['', 0]]);\n    var pathElement = this.getHrefAttribute().getDefinition();\n    this.text = this.getTextFromNode();\n    this.dataArray = this.parsePathData(pathElement);\n  }\n\n  getText() {\n    return this.text;\n  }\n\n  path(ctx) {\n    var {\n      dataArray\n    } = this;\n\n    if (ctx) {\n      ctx.beginPath();\n    }\n\n    dataArray.forEach(_ref => {\n      var {\n        type,\n        points\n      } = _ref;\n\n      switch (type) {\n        case PathParser.LINE_TO:\n          if (ctx) {\n            ctx.lineTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.MOVE_TO:\n          if (ctx) {\n            ctx.moveTo(points[0], points[1]);\n          }\n\n          break;\n\n        case PathParser.CURVE_TO:\n          if (ctx) {\n            ctx.bezierCurveTo(points[0], points[1], points[2], points[3], points[4], points[5]);\n          }\n\n          break;\n\n        case PathParser.QUAD_TO:\n          if (ctx) {\n            ctx.quadraticCurveTo(points[0], points[1], points[2], points[3]);\n          }\n\n          break;\n\n        case PathParser.ARC:\n          {\n            var [cx, cy, rx, ry, theta, dTheta, psi, fs] = points;\n            var r = rx > ry ? rx : ry;\n            var scaleX = rx > ry ? 1 : rx / ry;\n            var scaleY = rx > ry ? ry / rx : 1;\n\n            if (ctx) {\n              ctx.translate(cx, cy);\n              ctx.rotate(psi);\n              ctx.scale(scaleX, scaleY);\n              ctx.arc(0, 0, r, theta, theta + dTheta, Boolean(1 - fs));\n              ctx.scale(1 / scaleX, 1 / scaleY);\n              ctx.rotate(-psi);\n              ctx.translate(-cx, -cy);\n            }\n\n            break;\n          }\n\n        case PathParser.CLOSE_PATH:\n          if (ctx) {\n            ctx.closePath();\n          }\n\n          break;\n      }\n    });\n  }\n\n  renderChildren(ctx) {\n    this.setTextData(ctx);\n    ctx.save();\n    var textDecoration = this.parent.getStyle('text-decoration').getString();\n    var fontSize = this.getFontSize();\n    var {\n      glyphInfo\n    } = this;\n    var fill = ctx.fillStyle;\n\n    if (textDecoration === 'underline') {\n      ctx.beginPath();\n    }\n\n    glyphInfo.forEach((glyph, i) => {\n      var {\n        p0,\n        p1,\n        rotation,\n        text: partialText\n      } = glyph;\n      ctx.save();\n      ctx.translate(p0.x, p0.y);\n      ctx.rotate(rotation);\n\n      if (ctx.fillStyle) {\n        ctx.fillText(partialText, 0, 0);\n      }\n\n      if (ctx.strokeStyle) {\n        ctx.strokeText(partialText, 0, 0);\n      }\n\n      ctx.restore();\n\n      if (textDecoration === 'underline') {\n        if (i === 0) {\n          ctx.moveTo(p0.x, p0.y + fontSize / 8);\n        }\n\n        ctx.lineTo(p1.x, p1.y + fontSize / 5);\n      } // // To assist with debugging visually, uncomment following\n      //\n      // ctx.beginPath();\n      // if (i % 2)\n      // \tctx.strokeStyle = 'red';\n      // else\n      // \tctx.strokeStyle = 'green';\n      // ctx.moveTo(p0.x, p0.y);\n      // ctx.lineTo(p1.x, p1.y);\n      // ctx.stroke();\n      // ctx.closePath();\n\n    });\n\n    if (textDecoration === 'underline') {\n      ctx.lineWidth = fontSize / 20;\n      ctx.strokeStyle = fill;\n      ctx.stroke();\n      ctx.closePath();\n    }\n\n    ctx.restore();\n  }\n\n  getLetterSpacingAt() {\n    var idx = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return this.letterSpacingCache[idx] || 0;\n  }\n\n  findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, inputOffset, dy, c, charI) {\n    var offset = inputOffset;\n    var glyphWidth = this.measureText(ctx, c);\n\n    if (c === ' ' && anchor === 'justify' && textFullWidth < fullPathWidth) {\n      glyphWidth += (fullPathWidth - textFullWidth) / spacesNumber;\n    }\n\n    if (charI > -1) {\n      offset += this.getLetterSpacingAt(charI);\n    }\n\n    var splineStep = this.textHeight / 20;\n    var p0 = this.getEquidistantPointOnPath(offset, splineStep, 0);\n    var p1 = this.getEquidistantPointOnPath(offset + glyphWidth, splineStep, 0);\n    var segment = {\n      p0,\n      p1\n    };\n    var rotation = p0 && p1 ? Math.atan2(p1.y - p0.y, p1.x - p0.x) : 0;\n\n    if (dy) {\n      var dyX = Math.cos(Math.PI / 2 + rotation) * dy;\n      var dyY = Math.cos(-rotation) * dy;\n      segment.p0 = _objectSpread$2(_objectSpread$2({}, p0), {}, {\n        x: p0.x + dyX,\n        y: p0.y + dyY\n      });\n      segment.p1 = _objectSpread$2(_objectSpread$2({}, p1), {}, {\n        x: p1.x + dyX,\n        y: p1.y + dyY\n      });\n    }\n\n    offset += glyphWidth;\n    return {\n      offset,\n      segment,\n      rotation\n    };\n  }\n\n  measureText(ctx, text) {\n    var {\n      measuresCache\n    } = this;\n    var targetText = text || this.getText();\n\n    if (measuresCache.has(targetText)) {\n      return measuresCache.get(targetText);\n    }\n\n    var measure = this.measureTargetText(ctx, targetText);\n    measuresCache.set(targetText, measure);\n    return measure;\n  } // This method supposes what all custom fonts already loaded.\n  // If some font will be loaded after this method call, <textPath> will not be rendered correctly.\n  // You need to call this method manually to update glyphs cache.\n\n\n  setTextData(ctx) {\n    if (this.glyphInfo) {\n      return;\n    }\n\n    var renderText = this.getText();\n    var chars = renderText.split('');\n    var spacesNumber = renderText.split(' ').length - 1;\n    var dx = this.parent.getAttribute('dx').split().map(_ => _.getPixels('x'));\n    var dy = this.parent.getAttribute('dy').getPixels('y');\n    var anchor = this.parent.getStyle('text-anchor').getString('start');\n    var thisSpacing = this.getStyle('letter-spacing');\n    var parentSpacing = this.parent.getStyle('letter-spacing');\n    var letterSpacing = 0;\n\n    if (!thisSpacing.hasValue() || thisSpacing.getValue() === 'inherit') {\n      letterSpacing = parentSpacing.getPixels();\n    } else if (thisSpacing.hasValue()) {\n      if (thisSpacing.getValue() !== 'initial' && thisSpacing.getValue() !== 'unset') {\n        letterSpacing = thisSpacing.getPixels();\n      }\n    } // fill letter-spacing cache\n\n\n    var letterSpacingCache = [];\n    var textLen = renderText.length;\n    this.letterSpacingCache = letterSpacingCache;\n\n    for (var i = 0; i < textLen; i++) {\n      letterSpacingCache.push(typeof dx[i] !== 'undefined' ? dx[i] : letterSpacing);\n    }\n\n    var dxSum = letterSpacingCache.reduce((acc, cur, i) => i === 0 ? 0 : acc + cur || 0, 0);\n    var textWidth = this.measureText(ctx);\n    var textFullWidth = Math.max(textWidth + dxSum, 0);\n    this.textWidth = textWidth;\n    this.textHeight = this.getFontSize();\n    this.glyphInfo = [];\n    var fullPathWidth = this.getPathLength();\n    var startOffset = this.getStyle('startOffset').getNumber(0) * fullPathWidth;\n    var offset = 0;\n\n    if (anchor === 'middle' || anchor === 'center') {\n      offset = -textFullWidth / 2;\n    }\n\n    if (anchor === 'end' || anchor === 'right') {\n      offset = -textFullWidth;\n    }\n\n    offset += startOffset;\n    chars.forEach((char, i) => {\n      // Find such segment what distance between p0 and p1 is approx. width of glyph\n      var {\n        offset: nextOffset,\n        segment,\n        rotation\n      } = this.findSegmentToFitChar(ctx, anchor, textFullWidth, fullPathWidth, spacesNumber, offset, dy, char, i);\n      offset = nextOffset;\n\n      if (!segment.p0 || !segment.p1) {\n        return;\n      } // const width = this.getLineLength(\n      // \tsegment.p0.x,\n      // \tsegment.p0.y,\n      // \tsegment.p1.x,\n      // \tsegment.p1.y\n      // );\n      // Note: Since glyphs are rendered one at a time, any kerning pair data built into the font will not be used.\n      // Can foresee having a rough pair table built in that the developer can override as needed.\n      // Or use \"dx\" attribute of the <text> node as a naive replacement\n      // const kern = 0;\n      // placeholder for future implementation\n      // const midpoint = this.getPointOnLine(\n      // \tkern + width / 2.0,\n      // \tsegment.p0.x, segment.p0.y, segment.p1.x, segment.p1.y\n      // );\n\n\n      this.glyphInfo.push({\n        // transposeX: midpoint.x,\n        // transposeY: midpoint.y,\n        text: chars[i],\n        p0: segment.p0,\n        p1: segment.p1,\n        rotation\n      });\n    });\n  }\n\n  parsePathData(path) {\n    this.pathLength = -1; // reset path length\n\n    if (!path) {\n      return [];\n    }\n\n    var pathCommands = [];\n    var {\n      pathParser\n    } = path;\n    pathParser.reset(); // convert l, H, h, V, and v to L\n\n    while (!pathParser.isEnd()) {\n      var {\n        current\n      } = pathParser;\n      var startX = current ? current.x : 0;\n      var startY = current ? current.y : 0;\n      var command = pathParser.next();\n      var nextCommandType = command.type;\n      var points = [];\n\n      switch (command.type) {\n        case PathParser.MOVE_TO:\n          this.pathM(pathParser, points);\n          break;\n\n        case PathParser.LINE_TO:\n          nextCommandType = this.pathL(pathParser, points);\n          break;\n\n        case PathParser.HORIZ_LINE_TO:\n          nextCommandType = this.pathH(pathParser, points);\n          break;\n\n        case PathParser.VERT_LINE_TO:\n          nextCommandType = this.pathV(pathParser, points);\n          break;\n\n        case PathParser.CURVE_TO:\n          this.pathC(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_CURVE_TO:\n          nextCommandType = this.pathS(pathParser, points);\n          break;\n\n        case PathParser.QUAD_TO:\n          this.pathQ(pathParser, points);\n          break;\n\n        case PathParser.SMOOTH_QUAD_TO:\n          nextCommandType = this.pathT(pathParser, points);\n          break;\n\n        case PathParser.ARC:\n          points = this.pathA(pathParser);\n          break;\n\n        case PathParser.CLOSE_PATH:\n          PathElement.pathZ(pathParser);\n          break;\n      }\n\n      if (command.type !== PathParser.CLOSE_PATH) {\n        pathCommands.push({\n          type: nextCommandType,\n          points,\n          start: {\n            x: startX,\n            y: startY\n          },\n          pathLength: this.calcLength(startX, startY, nextCommandType, points)\n        });\n      } else {\n        pathCommands.push({\n          type: PathParser.CLOSE_PATH,\n          points: [],\n          pathLength: 0\n        });\n      }\n    }\n\n    return pathCommands;\n  }\n\n  pathM(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathM(pathParser).point;\n    points.push(x, y);\n  }\n\n  pathL(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathL(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathH(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathH(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathV(pathParser, points) {\n    var {\n      x,\n      y\n    } = PathElement.pathV(pathParser).point;\n    points.push(x, y);\n    return PathParser.LINE_TO;\n  }\n\n  pathC(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathC(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathS(pathParser, points) {\n    var {\n      point,\n      controlPoint,\n      currentPoint\n    } = PathElement.pathS(pathParser);\n    points.push(point.x, point.y, controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.CURVE_TO;\n  }\n\n  pathQ(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathQ(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n  }\n\n  pathT(pathParser, points) {\n    var {\n      controlPoint,\n      currentPoint\n    } = PathElement.pathT(pathParser);\n    points.push(controlPoint.x, controlPoint.y, currentPoint.x, currentPoint.y);\n    return PathParser.QUAD_TO;\n  }\n\n  pathA(pathParser) {\n    var {\n      rX,\n      rY,\n      sweepFlag,\n      xAxisRotation,\n      centp,\n      a1,\n      ad\n    } = PathElement.pathA(pathParser);\n\n    if (sweepFlag === 0 && ad > 0) {\n      ad -= 2 * Math.PI;\n    }\n\n    if (sweepFlag === 1 && ad < 0) {\n      ad += 2 * Math.PI;\n    }\n\n    return [centp.x, centp.y, rX, rY, a1, ad, xAxisRotation, sweepFlag];\n  }\n\n  calcLength(x, y, commandType, points) {\n    var len = 0;\n    var p1 = null;\n    var p2 = null;\n    var t = 0;\n\n    switch (commandType) {\n      case PathParser.LINE_TO:\n        return this.getLineLength(x, y, points[0], points[1]);\n\n      case PathParser.CURVE_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnCubicBezier(0, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnCubicBezier(t, x, y, points[0], points[1], points[2], points[3], points[4], points[5]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.QUAD_TO:\n        // Approximates by breaking curve into 100 line segments\n        len = 0.0;\n        p1 = this.getPointOnQuadraticBezier(0, x, y, points[0], points[1], points[2], points[3]);\n\n        for (t = 0.01; t <= 1; t += 0.01) {\n          p2 = this.getPointOnQuadraticBezier(t, x, y, points[0], points[1], points[2], points[3]);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          p1 = p2;\n        }\n\n        return len;\n\n      case PathParser.ARC:\n        {\n          // Approximates by breaking curve into line segments\n          len = 0.0;\n          var start = points[4]; // 4 = theta\n\n          var dTheta = points[5]; // 5 = dTheta\n\n          var end = points[4] + dTheta;\n          var inc = Math.PI / 180.0; // 1 degree resolution\n\n          if (Math.abs(start - end) < inc) {\n            inc = Math.abs(start - end);\n          } // Note: for purpose of calculating arc length, not going to worry about rotating X-axis by angle psi\n\n\n          p1 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], start, 0);\n\n          if (dTheta < 0) {\n            // clockwise\n            for (t = start - inc; t > end; t -= inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          } else {\n            // counter-clockwise\n            for (t = start + inc; t < end; t += inc) {\n              p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], t, 0);\n              len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n              p1 = p2;\n            }\n          }\n\n          p2 = this.getPointOnEllipticalArc(points[0], points[1], points[2], points[3], end, 0);\n          len += this.getLineLength(p1.x, p1.y, p2.x, p2.y);\n          return len;\n        }\n    }\n\n    return 0;\n  }\n\n  getPointOnLine(dist, p1x, p1y, p2x, p2y) {\n    var fromX = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : p1x;\n    var fromY = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : p1y;\n    var m = (p2y - p1y) / (p2x - p1x + PSEUDO_ZERO);\n    var run = Math.sqrt(dist * dist / (1 + m * m));\n\n    if (p2x < p1x) {\n      run *= -1;\n    }\n\n    var rise = m * run;\n    var pt = null;\n\n    if (p2x === p1x) {\n      // vertical line\n      pt = {\n        x: fromX,\n        y: fromY + rise\n      };\n    } else if ((fromY - p1y) / (fromX - p1x + PSEUDO_ZERO) === m) {\n      pt = {\n        x: fromX + run,\n        y: fromY + rise\n      };\n    } else {\n      var ix = 0;\n      var iy = 0;\n      var len = this.getLineLength(p1x, p1y, p2x, p2y);\n\n      if (len < PSEUDO_ZERO) {\n        return null;\n      }\n\n      var u = (fromX - p1x) * (p2x - p1x) + (fromY - p1y) * (p2y - p1y);\n      u /= len * len;\n      ix = p1x + u * (p2x - p1x);\n      iy = p1y + u * (p2y - p1y);\n      var pRise = this.getLineLength(fromX, fromY, ix, iy);\n      var pRun = Math.sqrt(dist * dist - pRise * pRise);\n      run = Math.sqrt(pRun * pRun / (1 + m * m));\n\n      if (p2x < p1x) {\n        run *= -1;\n      }\n\n      rise = m * run;\n      pt = {\n        x: ix + run,\n        y: iy + rise\n      };\n    }\n\n    return pt;\n  }\n\n  getPointOnPath(distance) {\n    var fullLen = this.getPathLength();\n    var cumulativePathLength = 0;\n    var p = null;\n\n    if (distance < -0.00005 || distance - 0.00005 > fullLen) {\n      return null;\n    }\n\n    var {\n      dataArray\n    } = this;\n\n    for (var command of dataArray) {\n      if (command && (command.pathLength < 0.00005 || cumulativePathLength + command.pathLength + 0.00005 < distance)) {\n        cumulativePathLength += command.pathLength;\n        continue;\n      }\n\n      var delta = distance - cumulativePathLength;\n      var currentT = 0;\n\n      switch (command.type) {\n        case PathParser.LINE_TO:\n          p = this.getPointOnLine(delta, command.start.x, command.start.y, command.points[0], command.points[1], command.start.x, command.start.y);\n          break;\n\n        case PathParser.ARC:\n          {\n            var start = command.points[4]; // 4 = theta\n\n            var dTheta = command.points[5]; // 5 = dTheta\n\n            var end = command.points[4] + dTheta;\n            currentT = start + delta / command.pathLength * dTheta;\n\n            if (dTheta < 0 && currentT < end || dTheta >= 0 && currentT > end) {\n              break;\n            }\n\n            p = this.getPointOnEllipticalArc(command.points[0], command.points[1], command.points[2], command.points[3], currentT, command.points[6]);\n            break;\n          }\n\n        case PathParser.CURVE_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnCubicBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3], command.points[4], command.points[5]);\n          break;\n\n        case PathParser.QUAD_TO:\n          currentT = delta / command.pathLength;\n\n          if (currentT > 1) {\n            currentT = 1;\n          }\n\n          p = this.getPointOnQuadraticBezier(currentT, command.start.x, command.start.y, command.points[0], command.points[1], command.points[2], command.points[3]);\n          break;\n      }\n\n      if (p) {\n        return p;\n      }\n\n      break;\n    }\n\n    return null;\n  }\n\n  getLineLength(x1, y1, x2, y2) {\n    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n  }\n\n  getPathLength() {\n    if (this.pathLength === -1) {\n      this.pathLength = this.dataArray.reduce((length, command) => command.pathLength > 0 ? length + command.pathLength : length, 0);\n    }\n\n    return this.pathLength;\n  }\n\n  getPointOnCubicBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y, p4x, p4y) {\n    var x = p4x * CB1(pct) + p3x * CB2(pct) + p2x * CB3(pct) + p1x * CB4(pct);\n    var y = p4y * CB1(pct) + p3y * CB2(pct) + p2y * CB3(pct) + p1y * CB4(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnQuadraticBezier(pct, p1x, p1y, p2x, p2y, p3x, p3y) {\n    var x = p3x * QB1(pct) + p2x * QB2(pct) + p1x * QB3(pct);\n    var y = p3y * QB1(pct) + p2y * QB2(pct) + p1y * QB3(pct);\n    return {\n      x,\n      y\n    };\n  }\n\n  getPointOnEllipticalArc(cx, cy, rx, ry, theta, psi) {\n    var cosPsi = Math.cos(psi);\n    var sinPsi = Math.sin(psi);\n    var pt = {\n      x: rx * Math.cos(theta),\n      y: ry * Math.sin(theta)\n    };\n    return {\n      x: cx + (pt.x * cosPsi - pt.y * sinPsi),\n      y: cy + (pt.x * sinPsi + pt.y * cosPsi)\n    };\n  } // TODO need some optimisations. possibly build cache only for curved segments?\n\n\n  buildEquidistantCache(inputStep, inputPrecision) {\n    var fullLen = this.getPathLength();\n    var precision = inputPrecision || 0.25; // accuracy vs performance\n\n    var step = inputStep || fullLen / 100;\n\n    if (!this.equidistantCache || this.equidistantCache.step !== step || this.equidistantCache.precision !== precision) {\n      // Prepare cache\n      this.equidistantCache = {\n        step,\n        precision,\n        points: []\n      }; // Calculate points\n\n      var s = 0;\n\n      for (var l = 0; l <= fullLen; l += precision) {\n        var p0 = this.getPointOnPath(l);\n        var p1 = this.getPointOnPath(l + precision);\n\n        if (!p0 || !p1) {\n          continue;\n        }\n\n        s += this.getLineLength(p0.x, p0.y, p1.x, p1.y);\n\n        if (s >= step) {\n          this.equidistantCache.points.push({\n            x: p0.x,\n            y: p0.y,\n            distance: l\n          });\n          s -= step;\n        }\n      }\n    }\n  }\n\n  getEquidistantPointOnPath(targetDistance, step, precision) {\n    this.buildEquidistantCache(step, precision);\n\n    if (targetDistance < 0 || targetDistance - this.getPathLength() > 0.00005) {\n      return null;\n    }\n\n    var idx = Math.round(targetDistance / this.getPathLength() * (this.equidistantCache.points.length - 1));\n    return this.equidistantCache.points[idx] || null;\n  }\n\n}\n\nvar dataUriRegex = /^\\s*data:(([^/,;]+\\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;\nclass ImageElement extends RenderedElement {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'image';\n    this.loaded = false;\n    var href = this.getHrefAttribute().getString();\n\n    if (!href) {\n      return;\n    }\n\n    var isSvg = href.endsWith('.svg') || /^\\s*data:image\\/svg\\+xml/i.test(href);\n    document.images.push(this);\n\n    if (!isSvg) {\n      void this.loadImage(href);\n    } else {\n      void this.loadSvg(href);\n    }\n\n    this.isSvg = isSvg;\n  }\n\n  loadImage(href) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        var image = yield _this.document.createImage(href);\n        _this.image = image;\n      } catch (err) {\n        console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n  loadSvg(href) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var match = dataUriRegex.exec(href);\n\n      if (match) {\n        var data = match[5];\n\n        if (match[4] === 'base64') {\n          _this2.image = atob(data);\n        } else {\n          _this2.image = decodeURIComponent(data);\n        }\n      } else {\n        try {\n          var response = yield _this2.document.fetch(href);\n          var svg = yield response.text();\n          _this2.image = svg;\n        } catch (err) {\n          console.error(\"Error while loading image \\\"\".concat(href, \"\\\":\"), err);\n        }\n      }\n\n      _this2.loaded = true;\n    })();\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      image,\n      loaded\n    } = this;\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!loaded || !image || !width || !height) {\n      return;\n    }\n\n    ctx.save();\n    ctx.translate(x, y);\n\n    if (this.isSvg) {\n      var subDocument = document.canvg.forkString(ctx, this.image, {\n        ignoreMouse: true,\n        ignoreAnimation: true,\n        ignoreDimensions: true,\n        ignoreClear: true,\n        offsetX: 0,\n        offsetY: 0,\n        scaleWidth: width,\n        scaleHeight: height\n      });\n      subDocument.document.documentElement.parent = this;\n      void subDocument.render();\n    } else {\n      var _image = this.image;\n      document.setViewBox({\n        ctx,\n        aspectRatio: this.getAttribute('preserveAspectRatio').getString(),\n        width,\n        desiredWidth: _image.width,\n        height,\n        desiredHeight: _image.height\n      });\n\n      if (this.loaded) {\n        if (typeof _image.complete === 'undefined' || _image.complete) {\n          ctx.drawImage(_image, 0, 0);\n        }\n      }\n    }\n\n    ctx.restore();\n  }\n\n  getBoundingBox() {\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n    return new BoundingBox(x, y, x + width, y + height);\n  }\n\n}\n\nclass SymbolElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'symbol';\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass SVGFontLoader {\n  constructor(document) {\n    this.document = document;\n    this.loaded = false;\n    document.fonts.push(this);\n  }\n\n  load(fontFamily, url) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        var {\n          document\n        } = _this;\n        var svgDocument = yield document.canvg.parser.load(url);\n        var fonts = svgDocument.getElementsByTagName('font');\n        Array.from(fonts).forEach(fontNode => {\n          var font = document.createElement(fontNode);\n          document.definitions[fontFamily] = font;\n        });\n      } catch (err) {\n        console.error(\"Error while loading font \\\"\".concat(url, \"\\\":\"), err);\n      }\n\n      _this.loaded = true;\n    })();\n  }\n\n}\n\nclass StyleElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'style';\n    var css = compressSpaces(Array.from(node.childNodes) // NEED TEST\n    .map(_ => _.textContent).join('').replace(/(\\/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+\\/)|(^[\\s]*\\/\\/.*)/gm, '') // remove comments\n    .replace(/@import.*;/g, '') // remove imports\n    );\n    var cssDefs = css.split('}');\n    cssDefs.forEach(_ => {\n      var def = _.trim();\n\n      if (!def) {\n        return;\n      }\n\n      var cssParts = def.split('{');\n      var cssClasses = cssParts[0].split(',');\n      var cssProps = cssParts[1].split(';');\n      cssClasses.forEach(_ => {\n        var cssClass = _.trim();\n\n        if (!cssClass) {\n          return;\n        }\n\n        var props = document.styles[cssClass] || {};\n        cssProps.forEach(cssProp => {\n          var prop = cssProp.indexOf(':');\n          var name = cssProp.substr(0, prop).trim();\n          var value = cssProp.substr(prop + 1, cssProp.length - prop).trim();\n\n          if (name && value) {\n            props[name] = new Property(document, name, value);\n          }\n        });\n        document.styles[cssClass] = props;\n        document.stylesSpecificity[cssClass] = getSelectorSpecificity(cssClass);\n\n        if (cssClass === '@font-face') {\n          //  && !nodeEnv\n          var fontFamily = props['font-family'].getString().replace(/\"|'/g, '');\n          var srcs = props.src.getString().split(',');\n          srcs.forEach(src => {\n            if (src.indexOf('format(\"svg\")') > 0) {\n              var url = parseExternalUrl(src);\n\n              if (url) {\n                void new SVGFontLoader(document).load(fontFamily, url);\n              }\n            }\n          });\n        }\n      });\n    });\n  }\n\n}\nStyleElement.parseExternalUrl = parseExternalUrl;\n\nclass UseElement extends RenderedElement {\n  constructor() {\n    super(...arguments);\n    this.type = 'use';\n  }\n\n  setContext(ctx) {\n    super.setContext(ctx);\n    var xAttr = this.getAttribute('x');\n    var yAttr = this.getAttribute('y');\n\n    if (xAttr.hasValue()) {\n      ctx.translate(xAttr.getPixels('x'), 0);\n    }\n\n    if (yAttr.hasValue()) {\n      ctx.translate(0, yAttr.getPixels('y'));\n    }\n  }\n\n  path(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      element.path(ctx);\n    }\n  }\n\n  renderChildren(ctx) {\n    var {\n      document,\n      element\n    } = this;\n\n    if (element) {\n      var tempSvg = element;\n\n      if (element.type === 'symbol') {\n        // render me using a temporary svg element in symbol cases (http://www.w3.org/TR/SVG/struct.html#UseElement)\n        tempSvg = new SVGElement(document, null);\n        tempSvg.attributes.viewBox = new Property(document, 'viewBox', element.getAttribute('viewBox').getString());\n        tempSvg.attributes.preserveAspectRatio = new Property(document, 'preserveAspectRatio', element.getAttribute('preserveAspectRatio').getString());\n        tempSvg.attributes.overflow = new Property(document, 'overflow', element.getAttribute('overflow').getString());\n        tempSvg.children = element.children; // element is still the parent of the children\n\n        element.styles.opacity = new Property(document, 'opacity', this.calculateOpacity());\n      }\n\n      if (tempSvg.type === 'svg') {\n        var widthStyle = this.getStyle('width', false, true);\n        var heightStyle = this.getStyle('height', false, true); // if symbol or svg, inherit width/height from me\n\n        if (widthStyle.hasValue()) {\n          tempSvg.attributes.width = new Property(document, 'width', widthStyle.getString());\n        }\n\n        if (heightStyle.hasValue()) {\n          tempSvg.attributes.height = new Property(document, 'height', heightStyle.getString());\n        }\n      }\n\n      var oldParent = tempSvg.parent;\n      tempSvg.parent = this;\n      tempSvg.render(ctx);\n      tempSvg.parent = oldParent;\n    }\n  }\n\n  getBoundingBox(ctx) {\n    var {\n      element\n    } = this;\n\n    if (element) {\n      return element.getBoundingBox(ctx);\n    }\n\n    return null;\n  }\n\n  elementTransform() {\n    var {\n      document,\n      element\n    } = this;\n    return Transform.fromElement(document, element);\n  }\n\n  get element() {\n    if (!this.cachedElement) {\n      this.cachedElement = this.getHrefAttribute().getDefinition();\n    }\n\n    return this.cachedElement;\n  }\n\n}\n\nfunction imGet(img, x, y, width, _height, rgba) {\n  return img[y * width * 4 + x * 4 + rgba];\n}\n\nfunction imSet(img, x, y, width, _height, rgba, val) {\n  img[y * width * 4 + x * 4 + rgba] = val;\n}\n\nfunction m(matrix, i, v) {\n  var mi = matrix[i];\n  return mi * v;\n}\n\nfunction c(a, m1, m2, m3) {\n  return m1 + Math.cos(a) * m2 + Math.sin(a) * m3;\n}\n\nclass FeColorMatrixElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feColorMatrix';\n    var matrix = toNumbers(this.getAttribute('values').getString());\n\n    switch (this.getAttribute('type').getString('matrix')) {\n      // http://www.w3.org/TR/SVG/filters.html#feColorMatrixElement\n      case 'saturate':\n        {\n          var s = matrix[0];\n          /* eslint-disable array-element-newline */\n\n          matrix = [0.213 + 0.787 * s, 0.715 - 0.715 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 + 0.285 * s, 0.072 - 0.072 * s, 0, 0, 0.213 - 0.213 * s, 0.715 - 0.715 * s, 0.072 + 0.928 * s, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'hueRotate':\n        {\n          var a = matrix[0] * Math.PI / 180.0;\n          /* eslint-disable array-element-newline */\n\n          matrix = [c(a, 0.213, 0.787, -0.213), c(a, 0.715, -0.715, -0.715), c(a, 0.072, -0.072, 0.928), 0, 0, c(a, 0.213, -0.213, 0.143), c(a, 0.715, 0.285, 0.140), c(a, 0.072, -0.072, -0.283), 0, 0, c(a, 0.213, -0.213, -0.787), c(a, 0.715, -0.715, 0.715), c(a, 0.072, 0.928, 0.072), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1];\n          /* eslint-enable array-element-newline */\n\n          break;\n        }\n\n      case 'luminanceToAlpha':\n        /* eslint-disable array-element-newline */\n        matrix = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2125, 0.7154, 0.0721, 0, 0, 0, 0, 0, 0, 1];\n        /* eslint-enable array-element-newline */\n\n        break;\n    }\n\n    this.matrix = matrix;\n    this.includeOpacity = this.getAttribute('includeOpacity').hasValue();\n  }\n\n  apply(ctx, _x, _y, width, height) {\n    // assuming x==0 && y==0 for now\n    var {\n      includeOpacity,\n      matrix\n    } = this;\n    var srcData = ctx.getImageData(0, 0, width, height);\n\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var r = imGet(srcData.data, x, y, width, height, 0);\n        var g = imGet(srcData.data, x, y, width, height, 1);\n        var b = imGet(srcData.data, x, y, width, height, 2);\n        var a = imGet(srcData.data, x, y, width, height, 3);\n        var nr = m(matrix, 0, r) + m(matrix, 1, g) + m(matrix, 2, b) + m(matrix, 3, a) + m(matrix, 4, 1);\n        var ng = m(matrix, 5, r) + m(matrix, 6, g) + m(matrix, 7, b) + m(matrix, 8, a) + m(matrix, 9, 1);\n        var nb = m(matrix, 10, r) + m(matrix, 11, g) + m(matrix, 12, b) + m(matrix, 13, a) + m(matrix, 14, 1);\n        var na = m(matrix, 15, r) + m(matrix, 16, g) + m(matrix, 17, b) + m(matrix, 18, a) + m(matrix, 19, 1);\n\n        if (includeOpacity) {\n          nr = 0;\n          ng = 0;\n          nb = 0;\n          na *= a / 255;\n        }\n\n        imSet(srcData.data, x, y, width, height, 0, nr);\n        imSet(srcData.data, x, y, width, height, 1, ng);\n        imSet(srcData.data, x, y, width, height, 2, nb);\n        imSet(srcData.data, x, y, width, height, 3, na);\n      }\n    }\n\n    ctx.clearRect(0, 0, width, height);\n    ctx.putImageData(srcData, 0, 0);\n  }\n\n}\n\nclass MaskElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'mask';\n  }\n\n  apply(ctx, element) {\n    var {\n      document\n    } = this; // render as temp svg\n\n    var x = this.getAttribute('x').getPixels('x');\n    var y = this.getAttribute('y').getPixels('y');\n    var width = this.getStyle('width').getPixels('x');\n    var height = this.getStyle('height').getPixels('y');\n\n    if (!width && !height) {\n      var boundingBox = new BoundingBox();\n      this.children.forEach(child => {\n        boundingBox.addBoundingBox(child.getBoundingBox(ctx));\n      });\n      x = Math.floor(boundingBox.x1);\n      y = Math.floor(boundingBox.y1);\n      width = Math.floor(boundingBox.width);\n      height = Math.floor(boundingBox.height);\n    }\n\n    var ignoredStyles = this.removeStyles(element, MaskElement.ignoreStyles);\n    var maskCanvas = document.createCanvas(x + width, y + height);\n    var maskCtx = maskCanvas.getContext('2d');\n    document.screen.setDefaults(maskCtx);\n    this.renderChildren(maskCtx); // convert mask to alpha with a fake node\n    // TODO: refactor out apply from feColorMatrix\n\n    new FeColorMatrixElement(document, {\n      nodeType: 1,\n      childNodes: [],\n      attributes: [{\n        nodeName: 'type',\n        value: 'luminanceToAlpha'\n      }, {\n        nodeName: 'includeOpacity',\n        value: 'true'\n      }]\n    }).apply(maskCtx, 0, 0, x + width, y + height);\n    var tmpCanvas = document.createCanvas(x + width, y + height);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    element.render(tmpCtx);\n    tmpCtx.globalCompositeOperation = 'destination-in';\n    tmpCtx.fillStyle = maskCtx.createPattern(maskCanvas, 'no-repeat');\n    tmpCtx.fillRect(0, 0, x + width, y + height);\n    ctx.fillStyle = tmpCtx.createPattern(tmpCanvas, 'no-repeat');\n    ctx.fillRect(0, 0, x + width, y + height); // reassign mask\n\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nMaskElement.ignoreStyles = ['mask', 'transform', 'clip-path'];\n\nvar noop = () => {// NOOP\n};\n\nclass ClipPathElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'clipPath';\n  }\n\n  apply(ctx) {\n    var {\n      document\n    } = this;\n    var contextProto = Reflect.getPrototypeOf(ctx);\n    var {\n      beginPath,\n      closePath\n    } = ctx;\n\n    if (contextProto) {\n      contextProto.beginPath = noop;\n      contextProto.closePath = noop;\n    }\n\n    Reflect.apply(beginPath, ctx, []);\n    this.children.forEach(child => {\n      if (typeof child.path === 'undefined') {\n        return;\n      }\n\n      var transform = typeof child.elementTransform !== 'undefined' ? child.elementTransform() : null; // handle <use />\n\n      if (!transform) {\n        transform = Transform.fromElement(document, child);\n      }\n\n      if (transform) {\n        transform.apply(ctx);\n      }\n\n      child.path(ctx);\n\n      if (contextProto) {\n        contextProto.closePath = closePath;\n      }\n\n      if (transform) {\n        transform.unapply(ctx);\n      }\n    });\n    Reflect.apply(closePath, ctx, []);\n    ctx.clip();\n\n    if (contextProto) {\n      contextProto.beginPath = beginPath;\n      contextProto.closePath = closePath;\n    }\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\n\nclass FilterElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'filter';\n  }\n\n  apply(ctx, element) {\n    // render as temp svg\n    var {\n      document,\n      children\n    } = this;\n    var boundingBox = element.getBoundingBox(ctx);\n\n    if (!boundingBox) {\n      return;\n    }\n\n    var px = 0;\n    var py = 0;\n    children.forEach(child => {\n      var efd = child.extraFilterDistance || 0;\n      px = Math.max(px, efd);\n      py = Math.max(py, efd);\n    });\n    var width = Math.floor(boundingBox.width);\n    var height = Math.floor(boundingBox.height);\n    var tmpCanvasWidth = width + 2 * px;\n    var tmpCanvasHeight = height + 2 * py;\n\n    if (tmpCanvasWidth < 1 || tmpCanvasHeight < 1) {\n      return;\n    }\n\n    var x = Math.floor(boundingBox.x);\n    var y = Math.floor(boundingBox.y);\n    var ignoredStyles = this.removeStyles(element, FilterElement.ignoreStyles);\n    var tmpCanvas = document.createCanvas(tmpCanvasWidth, tmpCanvasHeight);\n    var tmpCtx = tmpCanvas.getContext('2d');\n    document.screen.setDefaults(tmpCtx);\n    tmpCtx.translate(-x + px, -y + py);\n    element.render(tmpCtx); // apply filters\n\n    children.forEach(child => {\n      if (typeof child.apply === 'function') {\n        child.apply(tmpCtx, 0, 0, tmpCanvasWidth, tmpCanvasHeight);\n      }\n    }); // render on me\n\n    ctx.drawImage(tmpCanvas, 0, 0, tmpCanvasWidth, tmpCanvasHeight, x - px, y - py, tmpCanvasWidth, tmpCanvasHeight);\n    this.restoreStyles(element, ignoredStyles);\n  }\n\n  render(_) {// NO RENDER\n  }\n\n}\nFilterElement.ignoreStyles = ['filter', 'transform', 'clip-path'];\n\nclass FeDropShadowElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feDropShadow';\n    this.addStylesFromStyleDefinition();\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeMorphologyElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feMorphology';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeCompositeElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'feComposite';\n  }\n\n  apply(_, _x, _y, _width, _height) {// TODO: implement\n  }\n\n}\n\nclass FeGaussianBlurElement extends Element {\n  constructor(document, node, captureTextNodes) {\n    super(document, node, captureTextNodes);\n    this.type = 'feGaussianBlur';\n    this.blurRadius = Math.floor(this.getAttribute('stdDeviation').getNumber());\n    this.extraFilterDistance = this.blurRadius;\n  }\n\n  apply(ctx, x, y, width, height) {\n    var {\n      document,\n      blurRadius\n    } = this;\n    var body = document.window ? document.window.document.body : null;\n    var canvas = ctx.canvas; // StackBlur requires canvas be on document\n\n    canvas.id = document.getUniqueId();\n\n    if (body) {\n      canvas.style.display = 'none';\n      body.appendChild(canvas);\n    }\n\n    canvasRGBA(canvas, x, y, width, height, blurRadius);\n\n    if (body) {\n      body.removeChild(canvas);\n    }\n  }\n\n}\n\nclass TitleElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'title';\n  }\n\n}\n\nclass DescElement extends Element {\n  constructor() {\n    super(...arguments);\n    this.type = 'desc';\n  }\n\n}\n\nvar elements = {\n  'svg': SVGElement,\n  'rect': RectElement,\n  'circle': CircleElement,\n  'ellipse': EllipseElement,\n  'line': LineElement,\n  'polyline': PolylineElement,\n  'polygon': PolygonElement,\n  'path': PathElement,\n  'pattern': PatternElement,\n  'marker': MarkerElement,\n  'defs': DefsElement,\n  'linearGradient': LinearGradientElement,\n  'radialGradient': RadialGradientElement,\n  'stop': StopElement,\n  'animate': AnimateElement,\n  'animateColor': AnimateColorElement,\n  'animateTransform': AnimateTransformElement,\n  'font': FontElement,\n  'font-face': FontFaceElement,\n  'missing-glyph': MissingGlyphElement,\n  'glyph': GlyphElement,\n  'text': TextElement,\n  'tspan': TSpanElement,\n  'tref': TRefElement,\n  'a': AElement,\n  'textPath': TextPathElement,\n  'image': ImageElement,\n  'g': GElement,\n  'symbol': SymbolElement,\n  'style': StyleElement,\n  'use': UseElement,\n  'mask': MaskElement,\n  'clipPath': ClipPathElement,\n  'filter': FilterElement,\n  'feDropShadow': FeDropShadowElement,\n  'feMorphology': FeMorphologyElement,\n  'feComposite': FeCompositeElement,\n  'feColorMatrix': FeColorMatrixElement,\n  'feGaussianBlur': FeGaussianBlurElement,\n  'title': TitleElement,\n  'desc': DescElement\n};\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys$1(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction createCanvas(width, height) {\n  var canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  return canvas;\n}\n\nfunction createImage(_x) {\n  return _createImage.apply(this, arguments);\n}\n\nfunction _createImage() {\n  _createImage = _asyncToGenerator(function* (src) {\n    var anonymousCrossOrigin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var image = document.createElement('img');\n\n    if (anonymousCrossOrigin) {\n      image.crossOrigin = 'Anonymous';\n    }\n\n    return new Promise((resolve, reject) => {\n      image.onload = () => {\n        resolve(image);\n      };\n\n      image.onerror = (_event, _source, _lineno, _colno, error) => {\n        reject(error);\n      };\n\n      image.src = src;\n    });\n  });\n  return _createImage.apply(this, arguments);\n}\n\nclass Document {\n  constructor(canvg) {\n    var {\n      rootEmSize = 12,\n      emSize = 12,\n      createCanvas = Document.createCanvas,\n      createImage = Document.createImage,\n      anonymousCrossOrigin\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.canvg = canvg;\n    this.definitions = {};\n    this.styles = {};\n    this.stylesSpecificity = {};\n    this.images = [];\n    this.fonts = [];\n    this.emSizeStack = [];\n    this.uniqueId = 0;\n    this.screen = canvg.screen;\n    this.rootEmSize = rootEmSize;\n    this.emSize = emSize;\n    this.createCanvas = createCanvas;\n    this.createImage = this.bindCreateImage(createImage, anonymousCrossOrigin);\n    this.screen.wait(this.isImagesLoaded.bind(this));\n    this.screen.wait(this.isFontsLoaded.bind(this));\n  }\n\n  bindCreateImage(createImage, anonymousCrossOrigin) {\n    if (typeof anonymousCrossOrigin === 'boolean') {\n      return (source, forceAnonymousCrossOrigin) => createImage(source, typeof forceAnonymousCrossOrigin === 'boolean' ? forceAnonymousCrossOrigin : anonymousCrossOrigin);\n    }\n\n    return createImage;\n  }\n\n  get window() {\n    return this.screen.window;\n  }\n\n  get fetch() {\n    return this.screen.fetch;\n  }\n\n  get ctx() {\n    return this.screen.ctx;\n  }\n\n  get emSize() {\n    var {\n      emSizeStack\n    } = this;\n    return emSizeStack[emSizeStack.length - 1];\n  }\n\n  set emSize(value) {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.push(value);\n  }\n\n  popEmSize() {\n    var {\n      emSizeStack\n    } = this;\n    emSizeStack.pop();\n  }\n\n  getUniqueId() {\n    return \"canvg\".concat(++this.uniqueId);\n  }\n\n  isImagesLoaded() {\n    return this.images.every(_ => _.loaded);\n  }\n\n  isFontsLoaded() {\n    return this.fonts.every(_ => _.loaded);\n  }\n\n  createDocumentElement(document) {\n    var documentElement = this.createElement(document.documentElement);\n    documentElement.root = true;\n    documentElement.addStylesFromStyleDefinition();\n    this.documentElement = documentElement;\n    return documentElement;\n  }\n\n  createElement(node) {\n    var elementType = node.nodeName.replace(/^[^:]+:/, '');\n    var ElementType = Document.elementTypes[elementType];\n\n    if (typeof ElementType !== 'undefined') {\n      return new ElementType(this, node);\n    }\n\n    return new UnknownElement(this, node);\n  }\n\n  createTextNode(node) {\n    return new TextNode(this, node);\n  }\n\n  setViewBox(config) {\n    this.screen.setViewBox(_objectSpread$1({\n      document: this\n    }, config));\n  }\n\n}\nDocument.createCanvas = createCanvas;\nDocument.createImage = createImage;\nDocument.elementTypes = elements;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n/**\r\n * SVG renderer on canvas.\r\n */\n\nclass Canvg {\n  /**\r\n   * Main constructor.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG Document.\r\n   * @param options - Rendering options.\r\n   */\n  constructor(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.parser = new Parser(options);\n    this.screen = new Screen(ctx, options);\n    this.options = options;\n    var document = new Document(this, options);\n    var documentElement = document.createDocumentElement(svg);\n    this.document = document;\n    this.documentElement = documentElement;\n  }\n  /**\r\n   * Create Canvg instance from SVG source string or URL.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static from(ctx, svg) {\n    var _arguments = arguments;\n    return _asyncToGenerator(function* () {\n      var options = _arguments.length > 2 && _arguments[2] !== undefined ? _arguments[2] : {};\n      var parser = new Parser(options);\n      var svgDocument = yield parser.parse(svg);\n      return new Canvg(ctx, svgDocument, options);\n    })();\n  }\n  /**\r\n   * Create Canvg instance from SVG source string.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  static fromString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var parser = new Parser(options);\n    var svgDocument = parser.parseFromString(svg);\n    return new Canvg(ctx, svgDocument, options);\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string or URL.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  fork(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.from(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Create new Canvg instance with inherited options.\r\n   * @param ctx - Rendering context.\r\n   * @param svg - SVG source string.\r\n   * @param options - Rendering options.\r\n   * @returns Canvg instance.\r\n   */\n\n\n  forkString(ctx, svg) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return Canvg.fromString(ctx, svg, _objectSpread(_objectSpread({}, this.options), options));\n  }\n  /**\r\n   * Document is ready promise.\r\n   * @returns Ready promise.\r\n   */\n\n\n  ready() {\n    return this.screen.ready();\n  }\n  /**\r\n   * Document is ready value.\r\n   * @returns Is ready or not.\r\n   */\n\n\n  isReady() {\n    return this.screen.isReady();\n  }\n  /**\r\n   * Render only first frame, ignoring animations and mouse.\r\n   * @param options - Rendering options.\r\n   */\n\n\n  render() {\n    var _arguments2 = arguments,\n        _this = this;\n\n    return _asyncToGenerator(function* () {\n      var options = _arguments2.length > 0 && _arguments2[0] !== undefined ? _arguments2[0] : {};\n\n      _this.start(_objectSpread({\n        enableRedraw: true,\n        ignoreAnimation: true,\n        ignoreMouse: true\n      }, options));\n\n      yield _this.ready();\n\n      _this.stop();\n    })();\n  }\n  /**\r\n   * Start rendering.\r\n   * @param options - Render options.\r\n   */\n\n\n  start() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var {\n      documentElement,\n      screen,\n      options: baseOptions\n    } = this;\n    screen.start(documentElement, _objectSpread(_objectSpread({\n      enableRedraw: true\n    }, baseOptions), options));\n  }\n  /**\r\n   * Stop rendering.\r\n   */\n\n\n  stop() {\n    this.screen.stop();\n  }\n  /**\r\n   * Resize SVG to fit in given size.\r\n   * @param width\r\n   * @param height\r\n   * @param preserveAspectRatio\r\n   */\n\n\n  resize(width) {\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : width;\n    var preserveAspectRatio = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    this.documentElement.resize(width, height, preserveAspectRatio);\n  }\n\n}\n\nexport { AElement, AnimateColorElement, AnimateElement, AnimateTransformElement, BoundingBox, CB1, CB2, CB3, CB4, Canvg, CircleElement, ClipPathElement, DefsElement, DescElement, Document, Element, EllipseElement, FeColorMatrixElement, FeCompositeElement, FeDropShadowElement, FeGaussianBlurElement, FeMorphologyElement, FilterElement, Font, FontElement, FontFaceElement, GElement, GlyphElement, GradientElement, ImageElement, LineElement, LinearGradientElement, MarkerElement, MaskElement, Matrix, MissingGlyphElement, Mouse, PSEUDO_ZERO, Parser, PathElement, PathParser, PatternElement, Point, PolygonElement, PolylineElement, Property, QB1, QB2, QB3, RadialGradientElement, RectElement, RenderedElement, Rotate, SVGElement, SVGFontLoader, Scale, Screen, Skew, SkewX, SkewY, StopElement, StyleElement, SymbolElement, TRefElement, TSpanElement, TextElement, TextPathElement, TitleElement, Transform, Translate, UnknownElement, UseElement, ViewPort, compressSpaces, Canvg as default, getSelectorSpecificity, normalizeAttributeName, normalizeColor, parseExternalUrl, index as presets, toNumbers, trimLeft, trimRight, vectorMagnitude, vectorsAngle, vectorsRatio };\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};\n", "'use strict';\nvar isConstructor = require('../internals/is-constructor');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsConstructor(argument) is true`\nmodule.exports = function (argument) {\n  if (isConstructor(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a constructor');\n};\n", "'use strict';\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar create = require('../internals/object-create');\nvar defineProperty = require('../internals/object-define-property').f;\n\nvar UNSCOPABLES = wellKnownSymbol('unscopables');\nvar ArrayPrototype = Array.prototype;\n\n// Array.prototype[@@unscopables]\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\nif (ArrayPrototype[UNSCOPABLES] === undefined) {\n  defineProperty(ArrayPrototype, UNSCOPABLES, {\n    configurable: true,\n    value: create(null)\n  });\n}\n\n// add a key to Array.prototype[@@unscopables]\nmodule.exports = function (key) {\n  ArrayPrototype[UNSCOPABLES][key] = true;\n};\n", "'use strict';\nvar charAt = require('../internals/string-multibyte').charAt;\n\n// `AdvanceStringIndex` abstract operation\n// https://tc39.es/ecma262/#sec-advancestringindex\nmodule.exports = function (S, index, unicode) {\n  return index + (unicode ? charAt(S, index).length : 1);\n};\n", "'use strict';\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (it, Prototype) {\n  if (isPrototypeOf(Prototype, it)) return it;\n  throw new $TypeError('Incorrect invocation');\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw new $TypeError($String(argument) + ' is not an object');\n};\n", "'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = lengthOfArrayLike(O);\n    if (length === 0) return !IS_INCLUDES && -1;\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el !== el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value !== value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call -- required for testing\n    method.call(null, argument || function () { return 1; }, 1);\n  });\n};\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\nvar $TypeError = TypeError;\n\nvar REDUCE_EMPTY = 'Reduce of empty array with no initial value';\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = lengthOfArrayLike(O);\n    aCallable(callbackfn);\n    if (length === 0 && argumentsLength < 2) throw new $TypeError(REDUCE_EMPTY);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw new $TypeError(REDUCE_EMPTY);\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis([].slice);\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var called = 0;\n  var iteratorWithReturn = {\n    next: function () {\n      return { done: !!called++ };\n    },\n    'return': function () {\n      SAFE_CLOSING = true;\n    }\n  };\n  iteratorWithReturn[ITERATOR] = function () {\n    return this;\n  };\n  // eslint-disable-next-line es/no-array-from, no-throw-literal -- required for testing\n  Array.from(iteratorWithReturn, function () { throw 2; });\n} catch (error) { /* empty */ }\n\nmodule.exports = function (exec, SKIP_CLOSING) {\n  try {\n    if (!SKIP_CLOSING && !SAFE_CLOSING) return false;\n  } catch (error) { return false; } // workaround of old WebKit + `eval` bug\n  var ITERATION_SUPPORT = false;\n  try {\n    var object = {};\n    object[ITERATOR] = function () {\n      return {\n        next: function () {\n          return { done: ITERATION_SUPPORT = true };\n        }\n      };\n    };\n    exec(object);\n  } catch (error) { /* empty */ }\n  return ITERATION_SUPPORT;\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source, exceptions) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key) && !(exceptions && hasOwn(exceptions, key))) {\n      defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n    }\n  }\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar MATCH = wellKnownSymbol('match');\n\nmodule.exports = function (METHOD_NAME) {\n  var regexp = /./;\n  try {\n    '/./'[METHOD_NAME](regexp);\n  } catch (error1) {\n    try {\n      regexp[MATCH] = false;\n      return '/./'[METHOD_NAME](regexp);\n    } catch (error2) { /* empty */ }\n  } return false;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  // eslint-disable-next-line es/no-object-getprototypeof -- required for testing\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n", "'use strict';\n// `CreateIterResultObject` abstract operation\n// https://tc39.es/ecma262/#sec-createiterresultobject\nmodule.exports = function (value, done) {\n  return { value: value, done: done };\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "'use strict';\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "'use strict';\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineProperty = require('../internals/object-define-property');\n\nmodule.exports = function (target, name, descriptor) {\n  if (descriptor.get) makeBuiltIn(descriptor.get, name, { getter: true });\n  if (descriptor.set) makeBuiltIn(descriptor.set, name, { setter: true });\n  return defineProperty.f(target, name, descriptor);\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar definePropertyModule = require('../internals/object-define-property');\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nmodule.exports = function (O, key, value, options) {\n  if (!options) options = {};\n  var simple = options.enumerable;\n  var name = options.name !== undefined ? options.name : key;\n  if (isCallable(value)) makeBuiltIn(value, name, options);\n  if (options.global) {\n    if (simple) O[key] = value;\n    else defineGlobalProperty(key, value);\n  } else {\n    try {\n      if (!options.unsafe) delete O[key];\n      else if (O[key]) simple = true;\n    } catch (error) { /* empty */ }\n    if (simple) O[key] = value;\n    else definePropertyModule.f(O, key, {\n      value: value,\n      enumerable: false,\n      configurable: !options.nonConfigurable,\n      writable: !options.nonWritable\n    });\n  } return O;\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(globalThis, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    globalThis[key] = value;\n  } return value;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] !== 7;\n});\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar isObject = require('../internals/is-object');\n\nvar document = globalThis.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "'use strict';\n// iterable DOM collections\n// flag - `iterable` interface - 'entries', 'keys', 'values', 'forEach' methods\nmodule.exports = {\n  CSSRuleList: 0,\n  CSSStyleDeclaration: 0,\n  CSSValueList: 0,\n  ClientRectList: 0,\n  DOMRectList: 0,\n  DOMStringList: 0,\n  DOMTokenList: 1,\n  DataTransferItemList: 0,\n  FileList: 0,\n  HTMLAllCollection: 0,\n  HTMLCollection: 0,\n  HTMLFormElement: 0,\n  HTMLSelectElement: 0,\n  MediaList: 0,\n  MimeTypeArray: 0,\n  NamedNodeMap: 0,\n  NodeList: 1,\n  PaintRequestList: 0,\n  Plugin: 0,\n  PluginArray: 0,\n  SVGLengthList: 0,\n  SVGNumberList: 0,\n  SVGPathSegList: 0,\n  SVGPointList: 0,\n  SVGStringList: 0,\n  SVGTransformList: 0,\n  SourceBufferList: 0,\n  StyleSheetList: 0,\n  TextTrackCueList: 0,\n  TextTrackList: 0,\n  TouchList: 0\n};\n", "'use strict';\n// in old WebKit versions, `element.classList` is not an instance of global `DOMTokenList`\nvar documentCreateElement = require('../internals/document-create-element');\n\nvar classList = documentCreateElement('span').classList;\nvar DOMTokenListPrototype = classList && classList.constructor && classList.constructor.prototype;\n\nmodule.exports = DOMTokenListPrototype === Object.prototype ? undefined : DOMTokenListPrototype;\n", "'use strict';\n// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "'use strict';\nvar userAgent = require('../internals/environment-user-agent');\n\nmodule.exports = /ipad|iphone|ipod/i.test(userAgent) && typeof Pebble != 'undefined';\n", "'use strict';\nvar userAgent = require('../internals/environment-user-agent');\n\n// eslint-disable-next-line redos/no-vulnerable -- safe\nmodule.exports = /(?:ipad|iphone|ipod).*applewebkit/i.test(userAgent);\n", "'use strict';\nvar ENVIRONMENT = require('../internals/environment');\n\nmodule.exports = ENVIRONMENT === 'NODE';\n", "'use strict';\nvar userAgent = require('../internals/environment-user-agent');\n\nmodule.exports = /web0s(?!.*chrome)/i.test(userAgent);\n", "'use strict';\nvar globalThis = require('../internals/global-this');\n\nvar navigator = globalThis.navigator;\nvar userAgent = navigator && navigator.userAgent;\n\nmodule.exports = userAgent ? String(userAgent) : '';\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar userAgent = require('../internals/environment-user-agent');\n\nvar process = globalThis.process;\nvar Deno = globalThis.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n", "'use strict';\n/* global Bun, Deno -- detection */\nvar globalThis = require('../internals/global-this');\nvar userAgent = require('../internals/environment-user-agent');\nvar classof = require('../internals/classof-raw');\n\nvar userAgentStartsWith = function (string) {\n  return userAgent.slice(0, string.length) === string;\n};\n\nmodule.exports = (function () {\n  if (userAgentStartsWith('Bun/')) return 'BUN';\n  if (userAgentStartsWith('Cloudflare-Workers')) return 'CLOUDFLARE';\n  if (userAgentStartsWith('Deno/')) return 'DENO';\n  if (userAgentStartsWith('Node.js/')) return 'NODE';\n  if (globalThis.Bun && typeof Bun.version == 'string') return 'BUN';\n  if (globalThis.Deno && typeof Deno.version == 'object') return 'DENO';\n  if (classof(globalThis.process) === 'process') return 'NODE';\n  if (globalThis.window && globalThis.document) return 'BROWSER';\n  return 'REST';\n})();\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = globalThis;\n  } else if (STATIC) {\n    target = globalThis[TARGET] || defineGlobalProperty(TARGET, {});\n  } else {\n    target = globalThis[TARGET] && globalThis[TARGET].prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    defineBuiltIn(target, key, sourceProperty, options);\n  }\n};\n", "'use strict';\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "'use strict';\n// TODO: Remove from `core-js@4` since it's moved to entry points\nrequire('../modules/es.regexp.exec');\nvar call = require('../internals/function-call');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar regexpExec = require('../internals/regexp-exec');\nvar fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\nvar SPECIES = wellKnownSymbol('species');\nvar RegExpPrototype = RegExp.prototype;\n\nmodule.exports = function (KEY, exec, FORCED, SHAM) {\n  var SYMBOL = wellKnownSymbol(KEY);\n\n  var DELEGATES_TO_SYMBOL = !fails(function () {\n    // String methods call symbol-named RegExp methods\n    var O = {};\n    O[SYMBOL] = function () { return 7; };\n    return ''[KEY](O) !== 7;\n  });\n\n  var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {\n    // Symbol-named RegExp methods call .exec\n    var execCalled = false;\n    var re = /a/;\n\n    if (KEY === 'split') {\n      // We can't use real regex here since it causes deoptimization\n      // and serious performance degradation in V8\n      // https://github.com/zloirock/core-js/issues/306\n      re = {};\n      // RegExp[@@split] doesn't call the regex's exec method, but first creates\n      // a new one. We need to return the patched regex when creating the new one.\n      re.constructor = {};\n      re.constructor[SPECIES] = function () { return re; };\n      re.flags = '';\n      re[SYMBOL] = /./[SYMBOL];\n    }\n\n    re.exec = function () {\n      execCalled = true;\n      return null;\n    };\n\n    re[SYMBOL]('');\n    return !execCalled;\n  });\n\n  if (\n    !DELEGATES_TO_SYMBOL ||\n    !DELEGATES_TO_EXEC ||\n    FORCED\n  ) {\n    var nativeRegExpMethod = /./[SYMBOL];\n    var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {\n      var $exec = regexp.exec;\n      if ($exec === regexpExec || $exec === RegExpPrototype.exec) {\n        if (DELEGATES_TO_SYMBOL && !forceStringMethod) {\n          // The native String method already delegates to @@method (this\n          // polyfilled function), leasing to infinite recursion.\n          // We avoid it by directly calling the native @@method method.\n          return { done: true, value: call(nativeRegExpMethod, regexp, str, arg2) };\n        }\n        return { done: true, value: call(nativeMethod, str, regexp, arg2) };\n      }\n      return { done: false };\n    });\n\n    defineBuiltIn(String.prototype, KEY, methods[0]);\n    defineBuiltIn(RegExpPrototype, SYMBOL, methods[1]);\n  }\n\n  if (SHAM) createNonEnumerableProperty(RegExpPrototype[SYMBOL], 'sham', true);\n};\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar aCallable = require('../internals/a-callable');\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar bind = uncurryThis(uncurryThis.bind);\n\n// optional / simple context binding\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : NATIVE_BIND ? bind(fn, that) : function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-function-prototype-bind -- safe\n  var test = (function () { /* empty */ }).bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\n\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n", "'use strict';\nvar classofRaw = require('../internals/classof-raw');\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = function (fn) {\n  // Nashorn bug:\n  //   https://github.com/zloirock/core-js/issues/1128\n  //   https://github.com/zloirock/core-js/issues/1130\n  if (classofRaw(fn) === 'Function') return uncurryThis(fn);\n};\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar call = FunctionPrototype.call;\nvar uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? uncurryThisWithBind : function (fn) {\n  return function () {\n    return call.apply(fn, arguments);\n  };\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(globalThis[namespace]) : globalThis[namespace] && globalThis[namespace][method];\n};\n", "'use strict';\nvar classof = require('../internals/classof');\nvar getMethod = require('../internals/get-method');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\nvar Iterators = require('../internals/iterators');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\n\nmodule.exports = function (it) {\n  if (!isNullOrUndefined(it)) return getMethod(it, ITERATOR)\n    || getMethod(it, '@@iterator')\n    || Iterators[classof(it)];\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar aCallable = require('../internals/a-callable');\nvar anObject = require('../internals/an-object');\nvar tryToString = require('../internals/try-to-string');\nvar getIteratorMethod = require('../internals/get-iterator-method');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument, usingIterator) {\n  var iteratorMethod = arguments.length < 2 ? getIteratorMethod(argument) : usingIterator;\n  if (aCallable(iteratorMethod)) return anObject(call(iteratorMethod, argument));\n  throw new $TypeError(tryToString(argument) + ' is not iterable');\n};\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return isNullOrUndefined(func) ? undefined : aCallable(func);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar floor = Math.floor;\nvar charAt = uncurryThis(''.charAt);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar SUBSTITUTION_SYMBOLS = /\\$([$&'`]|\\d{1,2}|<[^>]*>)/g;\nvar SUBSTITUTION_SYMBOLS_NO_NAMED = /\\$([$&'`]|\\d{1,2})/g;\n\n// `GetSubstitution` abstract operation\n// https://tc39.es/ecma262/#sec-getsubstitution\nmodule.exports = function (matched, str, position, captures, namedCaptures, replacement) {\n  var tailPos = position + matched.length;\n  var m = captures.length;\n  var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;\n  if (namedCaptures !== undefined) {\n    namedCaptures = toObject(namedCaptures);\n    symbols = SUBSTITUTION_SYMBOLS;\n  }\n  return replace(replacement, symbols, function (match, ch) {\n    var capture;\n    switch (charAt(ch, 0)) {\n      case '$': return '$';\n      case '&': return matched;\n      case '`': return stringSlice(str, 0, position);\n      case \"'\": return stringSlice(str, tailPos);\n      case '<':\n        capture = namedCaptures[stringSlice(ch, 1, -1)];\n        break;\n      default: // \\d\\d?\n        var n = +ch;\n        if (n === 0) return match;\n        if (n > m) {\n          var f = floor(n / 10);\n          if (f === 0) return match;\n          if (f <= m) return captures[f - 1] === undefined ? charAt(ch, 1) : captures[f - 1] + charAt(ch, 1);\n          return match;\n        }\n        capture = captures[n - 1];\n    }\n    return capture === undefined ? '' : capture;\n  });\n};\n", "'use strict';\nvar check = function (it) {\n  return it && it.Math === Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  check(typeof this == 'object' && this) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n", "'use strict';\nmodule.exports = {};\n", "'use strict';\nmodule.exports = function (a, b) {\n  try {\n    // eslint-disable-next-line no-console -- safe\n    arguments.length === 1 ? console.error(a) : console.error(a, b);\n  } catch (error) { /* empty */ }\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('document', 'documentElement');\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a !== 7;\n});\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) === 'String' ? split(it, '') : $Object(it);\n} : $Object;\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n", "'use strict';\nvar NATIVE_WEAK_MAP = require('../internals/weak-map-basic-detection');\nvar globalThis = require('../internals/global-this');\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\nvar shared = require('../internals/shared-store');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\nvar TypeError = globalThis.TypeError;\nvar WeakMap = globalThis.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw new TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP || shared.state) {\n  var store = shared.state || (shared.state = new WeakMap());\n  /* eslint-disable no-self-assign -- prototype methods protection */\n  store.get = store.get;\n  store.has = store.has;\n  store.set = store.set;\n  /* eslint-enable no-self-assign -- prototype methods protection */\n  set = function (it, metadata) {\n    if (store.has(it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    store.set(it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return store.get(it) || {};\n  };\n  has = function (it) {\n    return store.has(it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    if (hasOwn(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return hasOwn(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return hasOwn(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayPrototype = Array.prototype;\n\n// check on default Array iterator\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);\n};\n", "'use strict';\nvar classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) === 'Array';\n};\n", "'use strict';\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\nvar documentAll = typeof document == 'object' && document.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nmodule.exports = typeof documentAll == 'undefined' && documentAll !== undefined ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof');\nvar getBuiltIn = require('../internals/get-built-in');\nvar inspectSource = require('../internals/inspect-source');\n\nvar noop = function () { /* empty */ };\nvar construct = getBuiltIn('Reflect', 'construct');\nvar constructorRegExp = /^\\s*(?:class|function)\\b/;\nvar exec = uncurryThis(constructorRegExp.exec);\nvar INCORRECT_TO_STRING = !constructorRegExp.test(noop);\n\nvar isConstructorModern = function isConstructor(argument) {\n  if (!isCallable(argument)) return false;\n  try {\n    construct(noop, [], argument);\n    return true;\n  } catch (error) {\n    return false;\n  }\n};\n\nvar isConstructorLegacy = function isConstructor(argument) {\n  if (!isCallable(argument)) return false;\n  switch (classof(argument)) {\n    case 'AsyncFunction':\n    case 'GeneratorFunction':\n    case 'AsyncGeneratorFunction': return false;\n  }\n  try {\n    // we can't check .prototype since constructors produced by .bind haven't it\n    // `Function#toString` throws on some built-it function in some legacy engines\n    // (for example, `DOMQuad` and similar in FF41-)\n    return INCORRECT_TO_STRING || !!exec(constructorRegExp, inspectSource(argument));\n  } catch (error) {\n    return true;\n  }\n};\n\nisConstructorLegacy.sham = true;\n\n// `IsConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-isconstructor\nmodule.exports = !construct || fails(function () {\n  var called;\n  return isConstructorModern(isConstructorModern.call)\n    || !isConstructorModern(Object)\n    || !isConstructorModern(function () { called = true; })\n    || called;\n}) ? isConstructorLegacy : isConstructorModern;\n", "'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value === POLYFILL ? true\n    : value === NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "'use strict';\n// we can't use just `it == null` since of `document.all` special case\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot-aec\nmodule.exports = function (it) {\n  return it === null || it === undefined;\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};\n", "'use strict';\nmodule.exports = false;\n", "'use strict';\nvar isObject = require('../internals/is-object');\nvar classof = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar MATCH = wellKnownSymbol('match');\n\n// `IsRegExp` abstract operation\n// https://tc39.es/ecma262/#sec-isregexp\nmodule.exports = function (it) {\n  var isRegExp;\n  return isObject(it) && ((isRegExp = it[MATCH]) !== undefined ? !!isRegExp : classof(it) === 'RegExp');\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};\n", "'use strict';\nvar bind = require('../internals/function-bind-context');\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar tryToString = require('../internals/try-to-string');\nvar isArrayIteratorMethod = require('../internals/is-array-iterator-method');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar getIterator = require('../internals/get-iterator');\nvar getIteratorMethod = require('../internals/get-iterator-method');\nvar iteratorClose = require('../internals/iterator-close');\n\nvar $TypeError = TypeError;\n\nvar Result = function (stopped, result) {\n  this.stopped = stopped;\n  this.result = result;\n};\n\nvar ResultPrototype = Result.prototype;\n\nmodule.exports = function (iterable, unboundFunction, options) {\n  var that = options && options.that;\n  var AS_ENTRIES = !!(options && options.AS_ENTRIES);\n  var IS_RECORD = !!(options && options.IS_RECORD);\n  var IS_ITERATOR = !!(options && options.IS_ITERATOR);\n  var INTERRUPTED = !!(options && options.INTERRUPTED);\n  var fn = bind(unboundFunction, that);\n  var iterator, iterFn, index, length, result, next, step;\n\n  var stop = function (condition) {\n    if (iterator) iteratorClose(iterator, 'normal', condition);\n    return new Result(true, condition);\n  };\n\n  var callFn = function (value) {\n    if (AS_ENTRIES) {\n      anObject(value);\n      return INTERRUPTED ? fn(value[0], value[1], stop) : fn(value[0], value[1]);\n    } return INTERRUPTED ? fn(value, stop) : fn(value);\n  };\n\n  if (IS_RECORD) {\n    iterator = iterable.iterator;\n  } else if (IS_ITERATOR) {\n    iterator = iterable;\n  } else {\n    iterFn = getIteratorMethod(iterable);\n    if (!iterFn) throw new $TypeError(tryToString(iterable) + ' is not iterable');\n    // optimisation for array iterators\n    if (isArrayIteratorMethod(iterFn)) {\n      for (index = 0, length = lengthOfArrayLike(iterable); length > index; index++) {\n        result = callFn(iterable[index]);\n        if (result && isPrototypeOf(ResultPrototype, result)) return result;\n      } return new Result(false);\n    }\n    iterator = getIterator(iterable, iterFn);\n  }\n\n  next = IS_RECORD ? iterable.next : iterator.next;\n  while (!(step = call(next, iterator)).done) {\n    try {\n      result = callFn(step.value);\n    } catch (error) {\n      iteratorClose(iterator, 'throw', error);\n    }\n    if (typeof result == 'object' && result && isPrototypeOf(ResultPrototype, result)) return result;\n  } return new Result(false);\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar getMethod = require('../internals/get-method');\n\nmodule.exports = function (iterator, kind, value) {\n  var innerResult, innerError;\n  anObject(iterator);\n  try {\n    innerResult = getMethod(iterator, 'return');\n    if (!innerResult) {\n      if (kind === 'throw') throw value;\n      return value;\n    }\n    innerResult = call(innerResult, iterator);\n  } catch (error) {\n    innerError = true;\n    innerResult = error;\n  }\n  if (kind === 'throw') throw value;\n  if (innerError) throw innerResult;\n  anObject(innerResult);\n  return value;\n};\n", "'use strict';\nvar IteratorPrototype = require('../internals/iterators-core').IteratorPrototype;\nvar create = require('../internals/object-create');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar Iterators = require('../internals/iterators');\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (IteratorConstructor, NAME, next, ENUMERABLE_NEXT) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(+!ENUMERABLE_NEXT, next) });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar IS_PURE = require('../internals/is-pure');\nvar FunctionName = require('../internals/function-name');\nvar isCallable = require('../internals/is-callable');\nvar createIteratorConstructor = require('../internals/iterator-create-constructor');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\nvar IteratorsCore = require('../internals/iterators-core');\n\nvar PROPER_FUNCTION_NAME = FunctionName.PROPER;\nvar CONFIGURABLE_FUNCTION_NAME = FunctionName.CONFIGURABLE;\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND && KIND in IterablePrototype) return IterablePrototype[KIND];\n\n    switch (KIND) {\n      case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };\n      case VALUES: return function values() { return new IteratorConstructor(this, KIND); };\n      case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };\n    }\n\n    return function () { return new IteratorConstructor(this); };\n  };\n\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR]\n    || IterablePrototype['@@iterator']\n    || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME === 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (CurrentIteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (!isCallable(CurrentIteratorPrototype[ITERATOR])) {\n          defineBuiltIn(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array.prototype.{ values, @@iterator }.name in V8 / FF\n  if (PROPER_FUNCTION_NAME && DEFAULT === VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    if (!IS_PURE && CONFIGURABLE_FUNCTION_NAME) {\n      createNonEnumerableProperty(IterablePrototype, 'name', VALUES);\n    } else {\n      INCORRECT_VALUES_NAME = true;\n      defaultIterator = function values() { return call(nativeIterator, this); };\n    }\n  }\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        defineBuiltIn(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    defineBuiltIn(IterablePrototype, ITERATOR, defaultIterator, { name: DEFAULT });\n  }\n  Iterators[NAME] = defaultIterator;\n\n  return methods;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar create = require('../internals/object-create');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\n// `%IteratorPrototype%` object\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\n/* eslint-disable es/no-array-prototype-keys -- safe */\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;\n  else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\n\nvar NEW_ITERATOR_PROTOTYPE = !isObject(IteratorPrototype) || fails(function () {\n  var test = {};\n  // FF44- legacy iterators case\n  return IteratorPrototype[ITERATOR].call(test) !== test;\n});\n\nif (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};\nelse if (IS_PURE) IteratorPrototype = create(IteratorPrototype);\n\n// `%IteratorPrototype%[@@iterator]()` method\n// https://tc39.es/ecma262/#sec-%iteratorprototype%-@@iterator\nif (!isCallable(IteratorPrototype[ITERATOR])) {\n  defineBuiltIn(IteratorPrototype, ITERATOR, function () {\n    return this;\n  });\n}\n\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};\n", "'use strict';\nmodule.exports = {};\n", "'use strict';\nvar toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar $String = String;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nvar stringSlice = uncurryThis(''.slice);\nvar replace = uncurryThis(''.replace);\nvar join = uncurryThis([].join);\n\nvar CONFIGURABLE_LENGTH = DESCRIPTORS && !fails(function () {\n  return defineProperty(function () { /* empty */ }, 'length', { value: 8 }).length !== 8;\n});\n\nvar TEMPLATE = String(String).split('String');\n\nvar makeBuiltIn = module.exports = function (value, name, options) {\n  if (stringSlice($String(name), 0, 7) === 'Symbol(') {\n    name = '[' + replace($String(name), /^Symbol\\(([^)]*)\\).*$/, '$1') + ']';\n  }\n  if (options && options.getter) name = 'get ' + name;\n  if (options && options.setter) name = 'set ' + name;\n  if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n    if (DESCRIPTORS) defineProperty(value, 'name', { value: name, configurable: true });\n    else value.name = name;\n  }\n  if (CONFIGURABLE_LENGTH && options && hasOwn(options, 'arity') && value.length !== options.arity) {\n    defineProperty(value, 'length', { value: options.arity });\n  }\n  try {\n    if (options && hasOwn(options, 'constructor') && options.constructor) {\n      if (DESCRIPTORS) defineProperty(value, 'prototype', { writable: false });\n    // in V8 ~ Chrome 53, prototypes of some methods, like `Array.prototype.values`, are non-writable\n    } else if (value.prototype) value.prototype = undefined;\n  } catch (error) { /* empty */ }\n  var state = enforceInternalState(value);\n  if (!hasOwn(state, 'source')) {\n    state.source = join(TEMPLATE, typeof name == 'string' ? name : '');\n  } return value;\n};\n\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n// eslint-disable-next-line no-extend-native -- required\nFunction.prototype.toString = makeBuiltIn(function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n}, 'toString');\n", "'use strict';\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `Math.trunc` method\n// https://tc39.es/ecma262/#sec-math.trunc\n// eslint-disable-next-line es/no-math-trunc -- safe\nmodule.exports = Math.trunc || function trunc(x) {\n  var n = +x;\n  return (n > 0 ? floor : ceil)(n);\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar safeGetBuiltIn = require('../internals/safe-get-built-in');\nvar bind = require('../internals/function-bind-context');\nvar macrotask = require('../internals/task').set;\nvar Queue = require('../internals/queue');\nvar IS_IOS = require('../internals/environment-is-ios');\nvar IS_IOS_PEBBLE = require('../internals/environment-is-ios-pebble');\nvar IS_WEBOS_WEBKIT = require('../internals/environment-is-webos-webkit');\nvar IS_NODE = require('../internals/environment-is-node');\n\nvar MutationObserver = globalThis.MutationObserver || globalThis.WebKitMutationObserver;\nvar document = globalThis.document;\nvar process = globalThis.process;\nvar Promise = globalThis.Promise;\nvar microtask = safeGetBuiltIn('queueMicrotask');\nvar notify, toggle, node, promise, then;\n\n// modern engines have queueMicrotask method\nif (!microtask) {\n  var queue = new Queue();\n\n  var flush = function () {\n    var parent, fn;\n    if (IS_NODE && (parent = process.domain)) parent.exit();\n    while (fn = queue.get()) try {\n      fn();\n    } catch (error) {\n      if (queue.head) notify();\n      throw error;\n    }\n    if (parent) parent.enter();\n  };\n\n  // browsers with MutationObserver, except iOS - https://github.com/zloirock/core-js/issues/339\n  // also except WebOS Webkit https://github.com/zloirock/core-js/issues/898\n  if (!IS_IOS && !IS_NODE && !IS_WEBOS_WEBKIT && MutationObserver && document) {\n    toggle = true;\n    node = document.createTextNode('');\n    new MutationObserver(flush).observe(node, { characterData: true });\n    notify = function () {\n      node.data = toggle = !toggle;\n    };\n  // environments with maybe non-completely correct, but existent Promise\n  } else if (!IS_IOS_PEBBLE && Promise && Promise.resolve) {\n    // Promise.resolve without an argument throws an error in LG WebOS 2\n    promise = Promise.resolve(undefined);\n    // workaround of WebKit ~ iOS Safari 10.1 bug\n    promise.constructor = Promise;\n    then = bind(promise.then, promise);\n    notify = function () {\n      then(flush);\n    };\n  // Node.js without promises\n  } else if (IS_NODE) {\n    notify = function () {\n      process.nextTick(flush);\n    };\n  // for other environments - macrotask based on:\n  // - setImmediate\n  // - MessageChannel\n  // - window.postMessage\n  // - onreadystatechange\n  // - setTimeout\n  } else {\n    // `webpack` dev server bug on IE global methods - use bind(fn, global)\n    macrotask = bind(macrotask, globalThis);\n    notify = function () {\n      macrotask(flush);\n    };\n  }\n\n  microtask = function (fn) {\n    if (!queue.head) notify();\n    queue.add(fn);\n  };\n}\n\nmodule.exports = microtask;\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\n\nvar $TypeError = TypeError;\n\nvar PromiseCapability = function (C) {\n  var resolve, reject;\n  this.promise = new C(function ($$resolve, $$reject) {\n    if (resolve !== undefined || reject !== undefined) throw new $TypeError('Bad Promise constructor');\n    resolve = $$resolve;\n    reject = $$reject;\n  });\n  this.resolve = aCallable(resolve);\n  this.reject = aCallable(reject);\n};\n\n// `NewPromiseCapability` abstract operation\n// https://tc39.es/ecma262/#sec-newpromisecapability\nmodule.exports.f = function (C) {\n  return new PromiseCapability(C);\n};\n", "'use strict';\nvar isRegExp = require('../internals/is-regexp');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (it) {\n  if (isRegExp(it)) {\n    throw new $TypeError(\"The method doesn't accept regular expressions\");\n  } return it;\n};\n", "'use strict';\n/* global ActiveXObject -- old IE, WSH */\nvar anObject = require('../internals/an-object');\nvar definePropertiesModule = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\n\nvar GT = '>';\nvar LT = '<';\nvar PROTOTYPE = 'prototype';\nvar SCRIPT = 'script';\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar EmptyConstructor = function () { /* empty */ };\n\nvar scriptTag = function (content) {\n  return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n};\n\n// Create object with fake `null` prototype: use ActiveX Object with cleared prototype\nvar NullProtoObjectViaActiveX = function (activeXDocument) {\n  activeXDocument.write(scriptTag(''));\n  activeXDocument.close();\n  var temp = activeXDocument.parentWindow.Object;\n  // eslint-disable-next-line no-useless-assignment -- avoid memory leak\n  activeXDocument = null;\n  return temp;\n};\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar NullProtoObjectViaIFrame = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var JS = 'java' + SCRIPT + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  // https://github.com/zloirock/core-js/issues/475\n  iframe.src = String(JS);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(scriptTag('document.F=Object'));\n  iframeDocument.close();\n  return iframeDocument.F;\n};\n\n// Check for document.domain and active x support\n// No need to use active x approach when document.domain is not set\n// see https://github.com/es-shims/es5-shim/issues/150\n// variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n// avoid IE GC bug\nvar activeXDocument;\nvar NullProtoObject = function () {\n  try {\n    activeXDocument = new ActiveXObject('htmlfile');\n  } catch (error) { /* ignore */ }\n  NullProtoObject = typeof document != 'undefined'\n    ? document.domain && activeXDocument\n      ? NullProtoObjectViaActiveX(activeXDocument) // old IE\n      : NullProtoObjectViaIFrame()\n    : NullProtoObjectViaActiveX(activeXDocument); // WSH\n  var length = enumBugKeys.length;\n  while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n  return NullProtoObject();\n};\n\nhiddenKeys[IE_PROTO] = true;\n\n// `Object.create` method\n// https://tc39.es/ecma262/#sec-object.create\n// eslint-disable-next-line es/no-object-create -- safe\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    EmptyConstructor[PROTOTYPE] = anObject(O);\n    result = new EmptyConstructor();\n    EmptyConstructor[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = NullProtoObject();\n  return Properties === undefined ? result : definePropertiesModule.f(result, Properties);\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar objectKeys = require('../internals/object-keys');\n\n// `Object.defineProperties` method\n// https://tc39.es/ecma262/#sec-object.defineproperties\n// eslint-disable-next-line es/no-object-defineproperties -- safe\nexports.f = DESCRIPTORS && !V8_PROTOTYPE_DEFINE_BUG ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var props = toIndexedObject(Properties);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], props[key]);\n  return O;\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar ENUMERABLE = 'enumerable';\nvar CONFIGURABLE = 'configurable';\nvar WRITABLE = 'writable';\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (typeof O === 'function' && P === 'prototype' && 'value' in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {\n    var current = $getOwnPropertyDescriptor(O, P);\n    if (current && current[WRITABLE]) {\n      O[P] = Attributes.value;\n      Attributes = {\n        configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],\n        enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],\n        writable: false\n      };\n    }\n  } return $defineProperty(O, P, Attributes);\n} : $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw new $TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n", "'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "'use strict';\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n", "'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar isCallable = require('../internals/is-callable');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar $Object = Object;\nvar ObjectPrototype = $Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.getprototypeof\n// eslint-disable-next-line es/no-object-getprototypeof -- safe\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function (O) {\n  var object = toObject(O);\n  if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];\n  var constructor = object.constructor;\n  if (isCallable(constructor) && object instanceof constructor) {\n    return constructor.prototype;\n  } return object instanceof $Object ? ObjectPrototype : null;\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar hasOwn = require('../internals/has-own-property');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar push = uncurryThis([].push);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !hasOwn(hiddenKeys, key) && hasOwn(O, key) && push(result, key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (hasOwn(O, key = names[i++])) {\n    ~indexOf(result, key) || push(result, key);\n  }\n  return result;\n};\n", "'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// `Object.keys` method\n// https://tc39.es/ecma262/#sec-object.keys\n// eslint-disable-next-line es/no-object-keys -- safe\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n", "'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n", "'use strict';\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar isObject = require('../internals/is-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    requireObjectCoercible(O);\n    aPossiblePrototype(proto);\n    if (!isObject(O)) return O;\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw new $TypeError(\"Can't convert object to primitive value\");\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar concat = uncurryThis([].concat);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? concat(keys, getOwnPropertySymbols(it)) : keys;\n};\n", "'use strict';\nmodule.exports = function (exec) {\n  try {\n    return { error: false, value: exec() };\n  } catch (error) {\n    return { error: true, value: error };\n  }\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar NativePromiseConstructor = require('../internals/promise-native-constructor');\nvar isCallable = require('../internals/is-callable');\nvar isForced = require('../internals/is-forced');\nvar inspectSource = require('../internals/inspect-source');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar ENVIRONMENT = require('../internals/environment');\nvar IS_PURE = require('../internals/is-pure');\nvar V8_VERSION = require('../internals/environment-v8-version');\n\nvar NativePromisePrototype = NativePromiseConstructor && NativePromiseConstructor.prototype;\nvar SPECIES = wellKnownSymbol('species');\nvar SUBCLASSING = false;\nvar NATIVE_PROMISE_REJECTION_EVENT = isCallable(globalThis.PromiseRejectionEvent);\n\nvar FORCED_PROMISE_CONSTRUCTOR = isForced('Promise', function () {\n  var PROMISE_CONSTRUCTOR_SOURCE = inspectSource(NativePromiseConstructor);\n  var GLOBAL_CORE_JS_PROMISE = PROMISE_CONSTRUCTOR_SOURCE !== String(NativePromiseConstructor);\n  // V8 6.6 (Node 10 and Chrome 66) have a bug with resolving custom thenables\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=830565\n  // We can't detect it synchronously, so just check versions\n  if (!GLOBAL_CORE_JS_PROMISE && V8_VERSION === 66) return true;\n  // We need Promise#{ catch, finally } in the pure version for preventing prototype pollution\n  if (IS_PURE && !(NativePromisePrototype['catch'] && NativePromisePrototype['finally'])) return true;\n  // We can't use @@species feature detection in V8 since it causes\n  // deoptimization and performance degradation\n  // https://github.com/zloirock/core-js/issues/679\n  if (!V8_VERSION || V8_VERSION < 51 || !/native code/.test(PROMISE_CONSTRUCTOR_SOURCE)) {\n    // Detect correctness of subclassing with @@species support\n    var promise = new NativePromiseConstructor(function (resolve) { resolve(1); });\n    var FakePromise = function (exec) {\n      exec(function () { /* empty */ }, function () { /* empty */ });\n    };\n    var constructor = promise.constructor = {};\n    constructor[SPECIES] = FakePromise;\n    SUBCLASSING = promise.then(function () { /* empty */ }) instanceof FakePromise;\n    if (!SUBCLASSING) return true;\n  // Unhandled rejections tracking support, NodeJS Promise without it fails @@species test\n  } return !GLOBAL_CORE_JS_PROMISE && (ENVIRONMENT === 'BROWSER' || ENVIRONMENT === 'DENO') && !NATIVE_PROMISE_REJECTION_EVENT;\n});\n\nmodule.exports = {\n  CONSTRUCTOR: FORCED_PROMISE_CONSTRUCTOR,\n  REJECTION_EVENT: NATIVE_PROMISE_REJECTION_EVENT,\n  SUBCLASSING: SUBCLASSING\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\n\nmodule.exports = globalThis.Promise;\n", "'use strict';\nvar anObject = require('../internals/an-object');\nvar isObject = require('../internals/is-object');\nvar newPromiseCapability = require('../internals/new-promise-capability');\n\nmodule.exports = function (C, x) {\n  anObject(C);\n  if (isObject(x) && x.constructor === C) return x;\n  var promiseCapability = newPromiseCapability.f(C);\n  var resolve = promiseCapability.resolve;\n  resolve(x);\n  return promiseCapability.promise;\n};\n", "'use strict';\nvar NativePromiseConstructor = require('../internals/promise-native-constructor');\nvar checkCorrectnessOfIteration = require('../internals/check-correctness-of-iteration');\nvar FORCED_PROMISE_CONSTRUCTOR = require('../internals/promise-constructor-detection').CONSTRUCTOR;\n\nmodule.exports = FORCED_PROMISE_CONSTRUCTOR || !checkCorrectnessOfIteration(function (iterable) {\n  NativePromiseConstructor.all(iterable).then(undefined, function () { /* empty */ });\n});\n", "'use strict';\nvar Queue = function () {\n  this.head = null;\n  this.tail = null;\n};\n\nQueue.prototype = {\n  add: function (item) {\n    var entry = { item: item, next: null };\n    var tail = this.tail;\n    if (tail) tail.next = entry;\n    else this.head = entry;\n    this.tail = entry;\n  },\n  get: function () {\n    var entry = this.head;\n    if (entry) {\n      var next = this.head = entry.next;\n      if (next === null) this.tail = null;\n      return entry.item;\n    }\n  }\n};\n\nmodule.exports = Queue;\n", "'use strict';\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof-raw');\nvar regexpExec = require('../internals/regexp-exec');\n\nvar $TypeError = TypeError;\n\n// `RegExpExec` abstract operation\n// https://tc39.es/ecma262/#sec-regexpexec\nmodule.exports = function (R, S) {\n  var exec = R.exec;\n  if (isCallable(exec)) {\n    var result = call(exec, R, S);\n    if (result !== null) anObject(result);\n    return result;\n  }\n  if (classof(R) === 'RegExp') return call(regexpExec, R, S);\n  throw new $TypeError('RegExp#exec called on incompatible receiver');\n};\n", "'use strict';\n/* eslint-disable regexp/no-empty-capturing-group, regexp/no-empty-group, regexp/no-lazy-ends -- testing */\n/* eslint-disable regexp/no-useless-quantifier -- testing */\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar regexpFlags = require('../internals/regexp-flags');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar shared = require('../internals/shared');\nvar create = require('../internals/object-create');\nvar getInternalState = require('../internals/internal-state').get;\nvar UNSUPPORTED_DOT_ALL = require('../internals/regexp-unsupported-dot-all');\nvar UNSUPPORTED_NCG = require('../internals/regexp-unsupported-ncg');\n\nvar nativeReplace = shared('native-string-replace', String.prototype.replace);\nvar nativeExec = RegExp.prototype.exec;\nvar patchedExec = nativeExec;\nvar charAt = uncurryThis(''.charAt);\nvar indexOf = uncurryThis(''.indexOf);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n\nvar UPDATES_LAST_INDEX_WRONG = (function () {\n  var re1 = /a/;\n  var re2 = /b*/g;\n  call(nativeExec, re1, 'a');\n  call(nativeExec, re2, 'a');\n  return re1.lastIndex !== 0 || re2.lastIndex !== 0;\n})();\n\nvar UNSUPPORTED_Y = stickyHelpers.BROKEN_CARET;\n\n// nonparticipating capturing group, copied from es5-shim's String#split patch.\nvar NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;\n\nvar PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED || UNSUPPORTED_Y || UNSUPPORTED_DOT_ALL || UNSUPPORTED_NCG;\n\nif (PATCH) {\n  patchedExec = function exec(string) {\n    var re = this;\n    var state = getInternalState(re);\n    var str = toString(string);\n    var raw = state.raw;\n    var result, reCopy, lastIndex, match, i, object, group;\n\n    if (raw) {\n      raw.lastIndex = re.lastIndex;\n      result = call(patchedExec, raw, str);\n      re.lastIndex = raw.lastIndex;\n      return result;\n    }\n\n    var groups = state.groups;\n    var sticky = UNSUPPORTED_Y && re.sticky;\n    var flags = call(regexpFlags, re);\n    var source = re.source;\n    var charsAdded = 0;\n    var strCopy = str;\n\n    if (sticky) {\n      flags = replace(flags, 'y', '');\n      if (indexOf(flags, 'g') === -1) {\n        flags += 'g';\n      }\n\n      strCopy = stringSlice(str, re.lastIndex);\n      // Support anchored sticky behavior.\n      if (re.lastIndex > 0 && (!re.multiline || re.multiline && charAt(str, re.lastIndex - 1) !== '\\n')) {\n        source = '(?: ' + source + ')';\n        strCopy = ' ' + strCopy;\n        charsAdded++;\n      }\n      // ^(? + rx + ) is needed, in combination with some str slicing, to\n      // simulate the 'y' flag.\n      reCopy = new RegExp('^(?:' + source + ')', flags);\n    }\n\n    if (NPCG_INCLUDED) {\n      reCopy = new RegExp('^' + source + '$(?!\\\\s)', flags);\n    }\n    if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;\n\n    match = call(nativeExec, sticky ? reCopy : re, strCopy);\n\n    if (sticky) {\n      if (match) {\n        match.input = stringSlice(match.input, charsAdded);\n        match[0] = stringSlice(match[0], charsAdded);\n        match.index = re.lastIndex;\n        re.lastIndex += match[0].length;\n      } else re.lastIndex = 0;\n    } else if (UPDATES_LAST_INDEX_WRONG && match) {\n      re.lastIndex = re.global ? match.index + match[0].length : lastIndex;\n    }\n    if (NPCG_INCLUDED && match && match.length > 1) {\n      // Fix browsers whose `exec` methods don't consistently return `undefined`\n      // for NPCG, like IE8. NOTE: This doesn't work for /(.?)?/\n      call(nativeReplace, match[0], reCopy, function () {\n        for (i = 1; i < arguments.length - 2; i++) {\n          if (arguments[i] === undefined) match[i] = undefined;\n        }\n      });\n    }\n\n    if (match && groups) {\n      match.groups = object = create(null);\n      for (i = 0; i < groups.length; i++) {\n        group = groups[i];\n        object[group[0]] = match[group[1]];\n      }\n    }\n\n    return match;\n  };\n}\n\nmodule.exports = patchedExec;\n", "'use strict';\nvar anObject = require('../internals/an-object');\n\n// `RegExp.prototype.flags` getter implementation\n// https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\nmodule.exports = function () {\n  var that = anObject(this);\n  var result = '';\n  if (that.hasIndices) result += 'd';\n  if (that.global) result += 'g';\n  if (that.ignoreCase) result += 'i';\n  if (that.multiline) result += 'm';\n  if (that.dotAll) result += 's';\n  if (that.unicode) result += 'u';\n  if (that.unicodeSets) result += 'v';\n  if (that.sticky) result += 'y';\n  return result;\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar hasOwn = require('../internals/has-own-property');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar regExpFlags = require('../internals/regexp-flags');\n\nvar RegExpPrototype = RegExp.prototype;\n\nmodule.exports = function (R) {\n  var flags = R.flags;\n  return flags === undefined && !('flags' in RegExpPrototype) && !hasOwn(R, 'flags') && isPrototypeOf(RegExpPrototype, R)\n    ? call(regExpFlags, R) : flags;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\n\nvar UNSUPPORTED_Y = fails(function () {\n  var re = $RegExp('a', 'y');\n  re.lastIndex = 2;\n  return re.exec('abcd') !== null;\n});\n\n// UC Browser bug\n// https://github.com/zloirock/core-js/issues/1008\nvar MISSED_STICKY = UNSUPPORTED_Y || fails(function () {\n  return !$RegExp('a', 'y').sticky;\n});\n\nvar BROKEN_CARET = UNSUPPORTED_Y || fails(function () {\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=773687\n  var re = $RegExp('^r', 'gy');\n  re.lastIndex = 2;\n  return re.exec('str') !== null;\n});\n\nmodule.exports = {\n  BROKEN_CARET: BROKEN_CARET,\n  MISSED_STICKY: MISSED_STICKY,\n  UNSUPPORTED_Y: UNSUPPORTED_Y\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 's') -> /./s and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('.', 's');\n  return !(re.dotAll && re.test('\\n') && re.flags === 's');\n});\n", "'use strict';\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\n// babel-minify and Closure Compiler transpiles RegExp('(?<a>b)', 'g') -> /(?<a>b)/g and it causes SyntaxError\nvar $RegExp = globalThis.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('(?<a>b)', 'g');\n  return re.exec('b').groups.a !== 'b' ||\n    'b'.replace(re, '$<a>c') !== 'bc';\n});\n", "'use strict';\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\nvar $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (isNullOrUndefined(it)) throw new $TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar DESCRIPTORS = require('../internals/descriptors');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Avoid NodeJS experimental warning\nmodule.exports = function (name) {\n  if (!DESCRIPTORS) return globalThis[name];\n  var descriptor = getOwnPropertyDescriptor(globalThis, name);\n  return descriptor && descriptor.value;\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar defineBuiltInAccessor = require('../internals/define-built-in-accessor');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar DESCRIPTORS = require('../internals/descriptors');\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (CONSTRUCTOR_NAME) {\n  var Constructor = getBuiltIn(CONSTRUCTOR_NAME);\n\n  if (DESCRIPTORS && Constructor && !Constructor[SPECIES]) {\n    defineBuiltInAccessor(Constructor, SPECIES, {\n      configurable: true,\n      get: function () { return this; }\n    });\n  }\n};\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (target, TAG, STATIC) {\n  if (target && !STATIC) target = target.prototype;\n  if (target && !hasOwn(target, TO_STRING_TAG)) {\n    defineProperty(target, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n", "'use strict';\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "'use strict';\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global-this');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n\n(store.versions || (store.versions = [])).push({\n  version: '3.39.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2024 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n", "'use strict';\nvar store = require('../internals/shared-store');\n\nmodule.exports = function (key, value) {\n  return store[key] || (store[key] = value || {});\n};\n", "'use strict';\nvar anObject = require('../internals/an-object');\nvar aConstructor = require('../internals/a-constructor');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar SPECIES = wellKnownSymbol('species');\n\n// `SpeciesConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-speciesconstructor\nmodule.exports = function (O, defaultConstructor) {\n  var C = anObject(O).constructor;\n  var S;\n  return C === undefined || isNullOrUndefined(S = anObject(C)[SPECIES]) ? defaultConstructor : aConstructor(S);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar charAt = uncurryThis(''.charAt);\nvar charCodeAt = uncurryThis(''.charCodeAt);\nvar stringSlice = uncurryThis(''.slice);\n\nvar createMethod = function (CONVERT_TO_STRING) {\n  return function ($this, pos) {\n    var S = toString(requireObjectCoercible($this));\n    var position = toIntegerOrInfinity(pos);\n    var size = S.length;\n    var first, second;\n    if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;\n    first = charCodeAt(S, position);\n    return first < 0xD800 || first > 0xDBFF || position + 1 === size\n      || (second = charCodeAt(S, position + 1)) < 0xDC00 || second > 0xDFFF\n        ? CONVERT_TO_STRING\n          ? charAt(S, position)\n          : first\n        : CONVERT_TO_STRING\n          ? stringSlice(S, position, position + 2)\n          : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.codePointAt` method\n  // https://tc39.es/ecma262/#sec-string.prototype.codepointat\n  codeAt: createMethod(false),\n  // `String.prototype.at` method\n  // https://github.com/mathiasbynens/String.prototype.at\n  charAt: createMethod(true)\n};\n", "'use strict';\nvar PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar fails = require('../internals/fails');\nvar whitespaces = require('../internals/whitespaces');\n\nvar non = '\\u200B\\u0085\\u180E';\n\n// check that a method works with the correct list\n// of whitespaces and has a correct name\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    return !!whitespaces[METHOD_NAME]()\n      || non[METHOD_NAME]() !== non\n      || (PROPER_FUNCTION_NAME && whitespaces[METHOD_NAME].name !== METHOD_NAME);\n  });\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\nvar whitespaces = require('../internals/whitespaces');\n\nvar replace = uncurryThis(''.replace);\nvar ltrim = RegExp('^[' + whitespaces + ']+');\nvar rtrim = RegExp('(^|[^' + whitespaces + '])[' + whitespaces + ']+$');\n\n// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\nvar createMethod = function (TYPE) {\n  return function ($this) {\n    var string = toString(requireObjectCoercible($this));\n    if (TYPE & 1) string = replace(string, ltrim, '');\n    if (TYPE & 2) string = replace(string, rtrim, '$1');\n    return string;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.{ trimLeft, trimStart }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n  start: createMethod(1),\n  // `String.prototype.{ trimRight, trimEnd }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimend\n  end: createMethod(2),\n  // `String.prototype.trim` method\n  // https://tc39.es/ecma262/#sec-string.prototype.trim\n  trim: createMethod(3)\n};\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/environment-v8-version');\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\nvar $String = globalThis.String;\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol('symbol detection');\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  // nb: Do not call `String` directly to avoid this being optimized out to `symbol+''` which will,\n  // of course, fail.\n  return !$String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar apply = require('../internals/function-apply');\nvar bind = require('../internals/function-bind-context');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar fails = require('../internals/fails');\nvar html = require('../internals/html');\nvar arraySlice = require('../internals/array-slice');\nvar createElement = require('../internals/document-create-element');\nvar validateArgumentsLength = require('../internals/validate-arguments-length');\nvar IS_IOS = require('../internals/environment-is-ios');\nvar IS_NODE = require('../internals/environment-is-node');\n\nvar set = globalThis.setImmediate;\nvar clear = globalThis.clearImmediate;\nvar process = globalThis.process;\nvar Dispatch = globalThis.Dispatch;\nvar Function = globalThis.Function;\nvar MessageChannel = globalThis.MessageChannel;\nvar String = globalThis.String;\nvar counter = 0;\nvar queue = {};\nvar ONREADYSTATECHANGE = 'onreadystatechange';\nvar $location, defer, channel, port;\n\nfails(function () {\n  // Deno throws a ReferenceError on `location` access without `--location` flag\n  $location = globalThis.location;\n});\n\nvar run = function (id) {\n  if (hasOwn(queue, id)) {\n    var fn = queue[id];\n    delete queue[id];\n    fn();\n  }\n};\n\nvar runner = function (id) {\n  return function () {\n    run(id);\n  };\n};\n\nvar eventListener = function (event) {\n  run(event.data);\n};\n\nvar globalPostMessageDefer = function (id) {\n  // old engines have not location.origin\n  globalThis.postMessage(String(id), $location.protocol + '//' + $location.host);\n};\n\n// Node.js 0.9+ & IE10+ has setImmediate, otherwise:\nif (!set || !clear) {\n  set = function setImmediate(handler) {\n    validateArgumentsLength(arguments.length, 1);\n    var fn = isCallable(handler) ? handler : Function(handler);\n    var args = arraySlice(arguments, 1);\n    queue[++counter] = function () {\n      apply(fn, undefined, args);\n    };\n    defer(counter);\n    return counter;\n  };\n  clear = function clearImmediate(id) {\n    delete queue[id];\n  };\n  // Node.js 0.8-\n  if (IS_NODE) {\n    defer = function (id) {\n      process.nextTick(runner(id));\n    };\n  // Sphere (JS game engine) Dispatch API\n  } else if (Dispatch && Dispatch.now) {\n    defer = function (id) {\n      Dispatch.now(runner(id));\n    };\n  // Browsers with MessageChannel, includes WebWorkers\n  // except iOS - https://github.com/zloirock/core-js/issues/624\n  } else if (MessageChannel && !IS_IOS) {\n    channel = new MessageChannel();\n    port = channel.port2;\n    channel.port1.onmessage = eventListener;\n    defer = bind(port.postMessage, port);\n  // Browsers with postMessage, skip WebWorkers\n  // IE8 has postMessage, but it's sync & typeof its postMessage is 'object'\n  } else if (\n    globalThis.addEventListener &&\n    isCallable(globalThis.postMessage) &&\n    !globalThis.importScripts &&\n    $location && $location.protocol !== 'file:' &&\n    !fails(globalPostMessageDefer)\n  ) {\n    defer = globalPostMessageDefer;\n    globalThis.addEventListener('message', eventListener, false);\n  // IE8-\n  } else if (ONREADYSTATECHANGE in createElement('script')) {\n    defer = function (id) {\n      html.appendChild(createElement('script'))[ONREADYSTATECHANGE] = function () {\n        html.removeChild(this);\n        run(id);\n      };\n    };\n  // Rest old browsers\n  } else {\n    defer = function (id) {\n      setTimeout(runner(id), 0);\n    };\n  }\n}\n\nmodule.exports = {\n  set: set,\n  clear: clear\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "'use strict';\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "'use strict';\nvar trunc = require('../internals/math-trunc');\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- NaN check\n  return number !== number || number === 0 ? 0 : trunc(number);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  var len = toIntegerOrInfinity(argument);\n  return len > 0 ? min(len, 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "'use strict';\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw new $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n", "'use strict';\nvar toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "'use strict';\nvar classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n", "'use strict';\nvar $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\n\nmodule.exports = NATIVE_SYMBOL &&\n  !Symbol.sham &&\n  typeof Symbol.iterator == 'symbol';\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () { /* empty */ }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype !== 42;\n});\n", "'use strict';\nvar $TypeError = TypeError;\n\nmodule.exports = function (passed, required) {\n  if (passed < required) throw new $TypeError('Not enough arguments');\n  return passed;\n};\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\n\nvar WeakMap = globalThis.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Symbol = globalThis.Symbol;\nvar WellKnownSymbolsStore = shared('wks');\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol['for'] || Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name)) {\n    WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol, name)\n      ? Symbol[name]\n      : createWellKnownSymbol('Symbol.' + name);\n  } return WellKnownSymbolsStore[name];\n};\n", "'use strict';\n// a string of all valid unicode whitespaces\nmodule.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' +\n  '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n", "'use strict';\n/* eslint-disable es/no-array-prototype-indexof -- required for testing */\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar $indexOf = require('../internals/array-includes').indexOf;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\n\nvar nativeIndexOf = uncurryThis([].indexOf);\n\nvar NEGATIVE_ZERO = !!nativeIndexOf && 1 / nativeIndexOf([1], 1, -0) < 0;\nvar FORCED = NEGATIVE_ZERO || !arrayMethodIsStrict('indexOf');\n\n// `Array.prototype.indexOf` method\n// https://tc39.es/ecma262/#sec-array.prototype.indexof\n$({ target: 'Array', proto: true, forced: FORCED }, {\n  indexOf: function indexOf(searchElement /* , fromIndex = 0 */) {\n    var fromIndex = arguments.length > 1 ? arguments[1] : undefined;\n    return NEGATIVE_ZERO\n      // convert -0 to +0\n      ? nativeIndexOf(this, searchElement, fromIndex) || 0\n      : $indexOf(this, searchElement, fromIndex);\n  }\n});\n", "'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar addToUnscopables = require('../internals/add-to-unscopables');\nvar Iterators = require('../internals/iterators');\nvar InternalStateModule = require('../internals/internal-state');\nvar defineProperty = require('../internals/object-define-property').f;\nvar defineIterator = require('../internals/iterator-define');\nvar createIterResultObject = require('../internals/create-iter-result-object');\nvar IS_PURE = require('../internals/is-pure');\nvar DESCRIPTORS = require('../internals/descriptors');\n\nvar ARRAY_ITERATOR = 'Array Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);\n\n// `Array.prototype.entries` method\n// https://tc39.es/ecma262/#sec-array.prototype.entries\n// `Array.prototype.keys` method\n// https://tc39.es/ecma262/#sec-array.prototype.keys\n// `Array.prototype.values` method\n// https://tc39.es/ecma262/#sec-array.prototype.values\n// `Array.prototype[@@iterator]` method\n// https://tc39.es/ecma262/#sec-array.prototype-@@iterator\n// `CreateArrayIterator` internal method\n// https://tc39.es/ecma262/#sec-createarrayiterator\nmodule.exports = defineIterator(Array, 'Array', function (iterated, kind) {\n  setInternalState(this, {\n    type: ARRAY_ITERATOR,\n    target: toIndexedObject(iterated), // target\n    index: 0,                          // next index\n    kind: kind                         // kind\n  });\n// `%ArrayIteratorPrototype%.next` method\n// https://tc39.es/ecma262/#sec-%arrayiteratorprototype%.next\n}, function () {\n  var state = getInternalState(this);\n  var target = state.target;\n  var index = state.index++;\n  if (!target || index >= target.length) {\n    state.target = null;\n    return createIterResultObject(undefined, true);\n  }\n  switch (state.kind) {\n    case 'keys': return createIterResultObject(index, false);\n    case 'values': return createIterResultObject(target[index], false);\n  } return createIterResultObject([index, target[index]], false);\n}, 'values');\n\n// argumentsList[@@iterator] is %ArrayProto_values%\n// https://tc39.es/ecma262/#sec-createunmappedargumentsobject\n// https://tc39.es/ecma262/#sec-createmappedargumentsobject\nvar values = Iterators.Arguments = Iterators.Array;\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables('keys');\naddToUnscopables('values');\naddToUnscopables('entries');\n\n// V8 ~ Chrome 45- bug\nif (!IS_PURE && DESCRIPTORS && values.name !== 'values') try {\n  defineProperty(values, 'name', { value: 'values' });\n} catch (error) { /* empty */ }\n", "'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/environment-v8-version');\nvar IS_NODE = require('../internals/environment-is-node');\n\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\nvar FORCED = CHROME_BUG || !arrayMethodIsStrict('reduce');\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: FORCED }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    var length = arguments.length;\n    return $reduce(this, callbackfn, length, length > 1 ? arguments[1] : undefined);\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isArray = require('../internals/is-array');\n\nvar nativeReverse = uncurryThis([].reverse);\nvar test = [1, 2];\n\n// `Array.prototype.reverse` method\n// https://tc39.es/ecma262/#sec-array.prototype.reverse\n// fix for Safari 12.0 bug\n// https://bugs.webkit.org/show_bug.cgi?id=188794\n$({ target: 'Array', proto: true, forced: String(test) === String(test.reverse()) }, {\n  reverse: function reverse() {\n    // eslint-disable-next-line no-self-assign -- dirty hack\n    if (isArray(this)) this.length = this.length;\n    return nativeReverse(this);\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar aCallable = require('../internals/a-callable');\nvar newPromiseCapabilityModule = require('../internals/new-promise-capability');\nvar perform = require('../internals/perform');\nvar iterate = require('../internals/iterate');\nvar PROMISE_STATICS_INCORRECT_ITERATION = require('../internals/promise-statics-incorrect-iteration');\n\n// `Promise.all` method\n// https://tc39.es/ecma262/#sec-promise.all\n$({ target: 'Promise', stat: true, forced: PROMISE_STATICS_INCORRECT_ITERATION }, {\n  all: function all(iterable) {\n    var C = this;\n    var capability = newPromiseCapabilityModule.f(C);\n    var resolve = capability.resolve;\n    var reject = capability.reject;\n    var result = perform(function () {\n      var $promiseResolve = aCallable(C.resolve);\n      var values = [];\n      var counter = 0;\n      var remaining = 1;\n      iterate(iterable, function (promise) {\n        var index = counter++;\n        var alreadyCalled = false;\n        remaining++;\n        call($promiseResolve, C, promise).then(function (value) {\n          if (alreadyCalled) return;\n          alreadyCalled = true;\n          values[index] = value;\n          --remaining || resolve(values);\n        }, reject);\n      });\n      --remaining || resolve(values);\n    });\n    if (result.error) reject(result.value);\n    return capability.promise;\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar IS_PURE = require('../internals/is-pure');\nvar FORCED_PROMISE_CONSTRUCTOR = require('../internals/promise-constructor-detection').CONSTRUCTOR;\nvar NativePromiseConstructor = require('../internals/promise-native-constructor');\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar defineBuiltIn = require('../internals/define-built-in');\n\nvar NativePromisePrototype = NativePromiseConstructor && NativePromiseConstructor.prototype;\n\n// `Promise.prototype.catch` method\n// https://tc39.es/ecma262/#sec-promise.prototype.catch\n$({ target: 'Promise', proto: true, forced: FORCED_PROMISE_CONSTRUCTOR, real: true }, {\n  'catch': function (onRejected) {\n    return this.then(undefined, onRejected);\n  }\n});\n\n// makes sure that native promise-based APIs `Promise#catch` properly works with patched `Promise#then`\nif (!IS_PURE && isCallable(NativePromiseConstructor)) {\n  var method = getBuiltIn('Promise').prototype['catch'];\n  if (NativePromisePrototype['catch'] !== method) {\n    defineBuiltIn(NativePromisePrototype, 'catch', method, { unsafe: true });\n  }\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar IS_PURE = require('../internals/is-pure');\nvar IS_NODE = require('../internals/environment-is-node');\nvar globalThis = require('../internals/global-this');\nvar call = require('../internals/function-call');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar setSpecies = require('../internals/set-species');\nvar aCallable = require('../internals/a-callable');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar anInstance = require('../internals/an-instance');\nvar speciesConstructor = require('../internals/species-constructor');\nvar task = require('../internals/task').set;\nvar microtask = require('../internals/microtask');\nvar hostReportErrors = require('../internals/host-report-errors');\nvar perform = require('../internals/perform');\nvar Queue = require('../internals/queue');\nvar InternalStateModule = require('../internals/internal-state');\nvar NativePromiseConstructor = require('../internals/promise-native-constructor');\nvar PromiseConstructorDetection = require('../internals/promise-constructor-detection');\nvar newPromiseCapabilityModule = require('../internals/new-promise-capability');\n\nvar PROMISE = 'Promise';\nvar FORCED_PROMISE_CONSTRUCTOR = PromiseConstructorDetection.CONSTRUCTOR;\nvar NATIVE_PROMISE_REJECTION_EVENT = PromiseConstructorDetection.REJECTION_EVENT;\nvar NATIVE_PROMISE_SUBCLASSING = PromiseConstructorDetection.SUBCLASSING;\nvar getInternalPromiseState = InternalStateModule.getterFor(PROMISE);\nvar setInternalState = InternalStateModule.set;\nvar NativePromisePrototype = NativePromiseConstructor && NativePromiseConstructor.prototype;\nvar PromiseConstructor = NativePromiseConstructor;\nvar PromisePrototype = NativePromisePrototype;\nvar TypeError = globalThis.TypeError;\nvar document = globalThis.document;\nvar process = globalThis.process;\nvar newPromiseCapability = newPromiseCapabilityModule.f;\nvar newGenericPromiseCapability = newPromiseCapability;\n\nvar DISPATCH_EVENT = !!(document && document.createEvent && globalThis.dispatchEvent);\nvar UNHANDLED_REJECTION = 'unhandledrejection';\nvar REJECTION_HANDLED = 'rejectionhandled';\nvar PENDING = 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\nvar HANDLED = 1;\nvar UNHANDLED = 2;\n\nvar Internal, OwnPromiseCapability, PromiseWrapper, nativeThen;\n\n// helpers\nvar isThenable = function (it) {\n  var then;\n  return isObject(it) && isCallable(then = it.then) ? then : false;\n};\n\nvar callReaction = function (reaction, state) {\n  var value = state.value;\n  var ok = state.state === FULFILLED;\n  var handler = ok ? reaction.ok : reaction.fail;\n  var resolve = reaction.resolve;\n  var reject = reaction.reject;\n  var domain = reaction.domain;\n  var result, then, exited;\n  try {\n    if (handler) {\n      if (!ok) {\n        if (state.rejection === UNHANDLED) onHandleUnhandled(state);\n        state.rejection = HANDLED;\n      }\n      if (handler === true) result = value;\n      else {\n        if (domain) domain.enter();\n        result = handler(value); // can throw\n        if (domain) {\n          domain.exit();\n          exited = true;\n        }\n      }\n      if (result === reaction.promise) {\n        reject(new TypeError('Promise-chain cycle'));\n      } else if (then = isThenable(result)) {\n        call(then, result, resolve, reject);\n      } else resolve(result);\n    } else reject(value);\n  } catch (error) {\n    if (domain && !exited) domain.exit();\n    reject(error);\n  }\n};\n\nvar notify = function (state, isReject) {\n  if (state.notified) return;\n  state.notified = true;\n  microtask(function () {\n    var reactions = state.reactions;\n    var reaction;\n    while (reaction = reactions.get()) {\n      callReaction(reaction, state);\n    }\n    state.notified = false;\n    if (isReject && !state.rejection) onUnhandled(state);\n  });\n};\n\nvar dispatchEvent = function (name, promise, reason) {\n  var event, handler;\n  if (DISPATCH_EVENT) {\n    event = document.createEvent('Event');\n    event.promise = promise;\n    event.reason = reason;\n    event.initEvent(name, false, true);\n    globalThis.dispatchEvent(event);\n  } else event = { promise: promise, reason: reason };\n  if (!NATIVE_PROMISE_REJECTION_EVENT && (handler = globalThis['on' + name])) handler(event);\n  else if (name === UNHANDLED_REJECTION) hostReportErrors('Unhandled promise rejection', reason);\n};\n\nvar onUnhandled = function (state) {\n  call(task, globalThis, function () {\n    var promise = state.facade;\n    var value = state.value;\n    var IS_UNHANDLED = isUnhandled(state);\n    var result;\n    if (IS_UNHANDLED) {\n      result = perform(function () {\n        if (IS_NODE) {\n          process.emit('unhandledRejection', value, promise);\n        } else dispatchEvent(UNHANDLED_REJECTION, promise, value);\n      });\n      // Browsers should not trigger `rejectionHandled` event if it was handled here, NodeJS - should\n      state.rejection = IS_NODE || isUnhandled(state) ? UNHANDLED : HANDLED;\n      if (result.error) throw result.value;\n    }\n  });\n};\n\nvar isUnhandled = function (state) {\n  return state.rejection !== HANDLED && !state.parent;\n};\n\nvar onHandleUnhandled = function (state) {\n  call(task, globalThis, function () {\n    var promise = state.facade;\n    if (IS_NODE) {\n      process.emit('rejectionHandled', promise);\n    } else dispatchEvent(REJECTION_HANDLED, promise, state.value);\n  });\n};\n\nvar bind = function (fn, state, unwrap) {\n  return function (value) {\n    fn(state, value, unwrap);\n  };\n};\n\nvar internalReject = function (state, value, unwrap) {\n  if (state.done) return;\n  state.done = true;\n  if (unwrap) state = unwrap;\n  state.value = value;\n  state.state = REJECTED;\n  notify(state, true);\n};\n\nvar internalResolve = function (state, value, unwrap) {\n  if (state.done) return;\n  state.done = true;\n  if (unwrap) state = unwrap;\n  try {\n    if (state.facade === value) throw new TypeError(\"Promise can't be resolved itself\");\n    var then = isThenable(value);\n    if (then) {\n      microtask(function () {\n        var wrapper = { done: false };\n        try {\n          call(then, value,\n            bind(internalResolve, wrapper, state),\n            bind(internalReject, wrapper, state)\n          );\n        } catch (error) {\n          internalReject(wrapper, error, state);\n        }\n      });\n    } else {\n      state.value = value;\n      state.state = FULFILLED;\n      notify(state, false);\n    }\n  } catch (error) {\n    internalReject({ done: false }, error, state);\n  }\n};\n\n// constructor polyfill\nif (FORCED_PROMISE_CONSTRUCTOR) {\n  // 25.4.3.1 Promise(executor)\n  PromiseConstructor = function Promise(executor) {\n    anInstance(this, PromisePrototype);\n    aCallable(executor);\n    call(Internal, this);\n    var state = getInternalPromiseState(this);\n    try {\n      executor(bind(internalResolve, state), bind(internalReject, state));\n    } catch (error) {\n      internalReject(state, error);\n    }\n  };\n\n  PromisePrototype = PromiseConstructor.prototype;\n\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  Internal = function Promise(executor) {\n    setInternalState(this, {\n      type: PROMISE,\n      done: false,\n      notified: false,\n      parent: false,\n      reactions: new Queue(),\n      rejection: false,\n      state: PENDING,\n      value: null\n    });\n  };\n\n  // `Promise.prototype.then` method\n  // https://tc39.es/ecma262/#sec-promise.prototype.then\n  Internal.prototype = defineBuiltIn(PromisePrototype, 'then', function then(onFulfilled, onRejected) {\n    var state = getInternalPromiseState(this);\n    var reaction = newPromiseCapability(speciesConstructor(this, PromiseConstructor));\n    state.parent = true;\n    reaction.ok = isCallable(onFulfilled) ? onFulfilled : true;\n    reaction.fail = isCallable(onRejected) && onRejected;\n    reaction.domain = IS_NODE ? process.domain : undefined;\n    if (state.state === PENDING) state.reactions.add(reaction);\n    else microtask(function () {\n      callReaction(reaction, state);\n    });\n    return reaction.promise;\n  });\n\n  OwnPromiseCapability = function () {\n    var promise = new Internal();\n    var state = getInternalPromiseState(promise);\n    this.promise = promise;\n    this.resolve = bind(internalResolve, state);\n    this.reject = bind(internalReject, state);\n  };\n\n  newPromiseCapabilityModule.f = newPromiseCapability = function (C) {\n    return C === PromiseConstructor || C === PromiseWrapper\n      ? new OwnPromiseCapability(C)\n      : newGenericPromiseCapability(C);\n  };\n\n  if (!IS_PURE && isCallable(NativePromiseConstructor) && NativePromisePrototype !== Object.prototype) {\n    nativeThen = NativePromisePrototype.then;\n\n    if (!NATIVE_PROMISE_SUBCLASSING) {\n      // make `Promise#then` return a polyfilled `Promise` for native promise-based APIs\n      defineBuiltIn(NativePromisePrototype, 'then', function then(onFulfilled, onRejected) {\n        var that = this;\n        return new PromiseConstructor(function (resolve, reject) {\n          call(nativeThen, that, resolve, reject);\n        }).then(onFulfilled, onRejected);\n      // https://github.com/zloirock/core-js/issues/640\n      }, { unsafe: true });\n    }\n\n    // make `.constructor === Promise` work for native promise-based APIs\n    try {\n      delete NativePromisePrototype.constructor;\n    } catch (error) { /* empty */ }\n\n    // make `instanceof Promise` work for native promise-based APIs\n    if (setPrototypeOf) {\n      setPrototypeOf(NativePromisePrototype, PromisePrototype);\n    }\n  }\n}\n\n// `Promise` constructor\n// https://tc39.es/ecma262/#sec-promise-executor\n$({ global: true, constructor: true, wrap: true, forced: FORCED_PROMISE_CONSTRUCTOR }, {\n  Promise: PromiseConstructor\n});\n\nsetToStringTag(PromiseConstructor, PROMISE, false, true);\nsetSpecies(PROMISE);\n", "'use strict';\n// TODO: Remove this module from `core-js@4` since it's split to modules listed below\nrequire('../modules/es.promise.constructor');\nrequire('../modules/es.promise.all');\nrequire('../modules/es.promise.catch');\nrequire('../modules/es.promise.race');\nrequire('../modules/es.promise.reject');\nrequire('../modules/es.promise.resolve');\n", "'use strict';\nvar $ = require('../internals/export');\nvar call = require('../internals/function-call');\nvar aCallable = require('../internals/a-callable');\nvar newPromiseCapabilityModule = require('../internals/new-promise-capability');\nvar perform = require('../internals/perform');\nvar iterate = require('../internals/iterate');\nvar PROMISE_STATICS_INCORRECT_ITERATION = require('../internals/promise-statics-incorrect-iteration');\n\n// `Promise.race` method\n// https://tc39.es/ecma262/#sec-promise.race\n$({ target: 'Promise', stat: true, forced: PROMISE_STATICS_INCORRECT_ITERATION }, {\n  race: function race(iterable) {\n    var C = this;\n    var capability = newPromiseCapabilityModule.f(C);\n    var reject = capability.reject;\n    var result = perform(function () {\n      var $promiseResolve = aCallable(C.resolve);\n      iterate(iterable, function (promise) {\n        call($promiseResolve, C, promise).then(capability.resolve, reject);\n      });\n    });\n    if (result.error) reject(result.value);\n    return capability.promise;\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar newPromiseCapabilityModule = require('../internals/new-promise-capability');\nvar FORCED_PROMISE_CONSTRUCTOR = require('../internals/promise-constructor-detection').CONSTRUCTOR;\n\n// `Promise.reject` method\n// https://tc39.es/ecma262/#sec-promise.reject\n$({ target: 'Promise', stat: true, forced: FORCED_PROMISE_CONSTRUCTOR }, {\n  reject: function reject(r) {\n    var capability = newPromiseCapabilityModule.f(this);\n    var capabilityReject = capability.reject;\n    capabilityReject(r);\n    return capability.promise;\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar getBuiltIn = require('../internals/get-built-in');\nvar IS_PURE = require('../internals/is-pure');\nvar NativePromiseConstructor = require('../internals/promise-native-constructor');\nvar FORCED_PROMISE_CONSTRUCTOR = require('../internals/promise-constructor-detection').CONSTRUCTOR;\nvar promiseResolve = require('../internals/promise-resolve');\n\nvar PromiseConstructorWrapper = getBuiltIn('Promise');\nvar CHECK_WRAPPER = IS_PURE && !FORCED_PROMISE_CONSTRUCTOR;\n\n// `Promise.resolve` method\n// https://tc39.es/ecma262/#sec-promise.resolve\n$({ target: 'Promise', stat: true, forced: IS_PURE || FORCED_PROMISE_CONSTRUCTOR }, {\n  resolve: function resolve(x) {\n    return promiseResolve(CHECK_WRAPPER && this === PromiseConstructorWrapper ? NativePromiseConstructor : this, x);\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar exec = require('../internals/regexp-exec');\n\n// `RegExp.prototype.exec` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.exec\n$({ target: 'RegExp', proto: true, forced: /./.exec !== exec }, {\n  exec: exec\n});\n", "'use strict';\nvar PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar defineBuiltIn = require('../internals/define-built-in');\nvar anObject = require('../internals/an-object');\nvar $toString = require('../internals/to-string');\nvar fails = require('../internals/fails');\nvar getRegExpFlags = require('../internals/regexp-get-flags');\n\nvar TO_STRING = 'toString';\nvar RegExpPrototype = RegExp.prototype;\nvar nativeToString = RegExpPrototype[TO_STRING];\n\nvar NOT_GENERIC = fails(function () { return nativeToString.call({ source: 'a', flags: 'b' }) !== '/a/b'; });\n// FF44- RegExp#toString has a wrong name\nvar INCORRECT_NAME = PROPER_FUNCTION_NAME && nativeToString.name !== TO_STRING;\n\n// `RegExp.prototype.toString` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.tostring\nif (NOT_GENERIC || INCORRECT_NAME) {\n  defineBuiltIn(RegExpPrototype, TO_STRING, function toString() {\n    var R = anObject(this);\n    var pattern = $toString(R.source);\n    var flags = $toString(getRegExpFlags(R));\n    return '/' + pattern + '/' + flags;\n  }, { unsafe: true });\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar notARegExp = require('../internals/not-a-regexp');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar correctIsRegExpLogic = require('../internals/correct-is-regexp-logic');\nvar IS_PURE = require('../internals/is-pure');\n\nvar slice = uncurryThis(''.slice);\nvar min = Math.min;\n\nvar CORRECT_IS_REGEXP_LOGIC = correctIsRegExpLogic('endsWith');\n// https://github.com/zloirock/core-js/pull/702\nvar MDN_POLYFILL_BUG = !IS_PURE && !CORRECT_IS_REGEXP_LOGIC && !!function () {\n  var descriptor = getOwnPropertyDescriptor(String.prototype, 'endsWith');\n  return descriptor && !descriptor.writable;\n}();\n\n// `String.prototype.endsWith` method\n// https://tc39.es/ecma262/#sec-string.prototype.endswith\n$({ target: 'String', proto: true, forced: !MDN_POLYFILL_BUG && !CORRECT_IS_REGEXP_LOGIC }, {\n  endsWith: function endsWith(searchString /* , endPosition = @length */) {\n    var that = toString(requireObjectCoercible(this));\n    notARegExp(searchString);\n    var endPosition = arguments.length > 1 ? arguments[1] : undefined;\n    var len = that.length;\n    var end = endPosition === undefined ? len : min(toLength(endPosition), len);\n    var search = toString(searchString);\n    return slice(that, end - search.length, end) === search;\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar notARegExp = require('../internals/not-a-regexp');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\nvar correctIsRegExpLogic = require('../internals/correct-is-regexp-logic');\n\nvar stringIndexOf = uncurryThis(''.indexOf);\n\n// `String.prototype.includes` method\n// https://tc39.es/ecma262/#sec-string.prototype.includes\n$({ target: 'String', proto: true, forced: !correctIsRegExpLogic('includes') }, {\n  includes: function includes(searchString /* , position = 0 */) {\n    return !!~stringIndexOf(\n      toString(requireObjectCoercible(this)),\n      toString(notARegExp(searchString)),\n      arguments.length > 1 ? arguments[1] : undefined\n    );\n  }\n});\n", "'use strict';\nvar call = require('../internals/function-call');\nvar fixRegExpWellKnownSymbolLogic = require('../internals/fix-regexp-well-known-symbol-logic');\nvar anObject = require('../internals/an-object');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar getMethod = require('../internals/get-method');\nvar advanceStringIndex = require('../internals/advance-string-index');\nvar regExpExec = require('../internals/regexp-exec-abstract');\n\n// @@match logic\nfixRegExpWellKnownSymbolLogic('match', function (MATCH, nativeMatch, maybeCallNative) {\n  return [\n    // `String.prototype.match` method\n    // https://tc39.es/ecma262/#sec-string.prototype.match\n    function match(regexp) {\n      var O = requireObjectCoercible(this);\n      var matcher = isNullOrUndefined(regexp) ? undefined : getMethod(regexp, MATCH);\n      return matcher ? call(matcher, regexp, O) : new RegExp(regexp)[MATCH](toString(O));\n    },\n    // `RegExp.prototype[@@match]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@match\n    function (string) {\n      var rx = anObject(this);\n      var S = toString(string);\n      var res = maybeCallNative(nativeMatch, rx, S);\n\n      if (res.done) return res.value;\n\n      if (!rx.global) return regExpExec(rx, S);\n\n      var fullUnicode = rx.unicode;\n      rx.lastIndex = 0;\n      var A = [];\n      var n = 0;\n      var result;\n      while ((result = regExpExec(rx, S)) !== null) {\n        var matchStr = toString(result[0]);\n        A[n] = matchStr;\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n        n++;\n      }\n      return n === 0 ? null : A;\n    }\n  ];\n});\n", "'use strict';\nvar apply = require('../internals/function-apply');\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fixRegExpWellKnownSymbolLogic = require('../internals/fix-regexp-well-known-symbol-logic');\nvar fails = require('../internals/fails');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar advanceStringIndex = require('../internals/advance-string-index');\nvar getMethod = require('../internals/get-method');\nvar getSubstitution = require('../internals/get-substitution');\nvar regExpExec = require('../internals/regexp-exec-abstract');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar REPLACE = wellKnownSymbol('replace');\nvar max = Math.max;\nvar min = Math.min;\nvar concat = uncurryThis([].concat);\nvar push = uncurryThis([].push);\nvar stringIndexOf = uncurryThis(''.indexOf);\nvar stringSlice = uncurryThis(''.slice);\n\nvar maybeToString = function (it) {\n  return it === undefined ? it : String(it);\n};\n\n// IE <= 11 replaces $0 with the whole match, as if it was $&\n// https://stackoverflow.com/questions/6024666/getting-ie-to-replace-a-regex-with-the-literal-string-0\nvar REPLACE_KEEPS_$0 = (function () {\n  // eslint-disable-next-line regexp/prefer-escape-replacement-dollar-char -- required for testing\n  return 'a'.replace(/./, '$0') === '$0';\n})();\n\n// Safari <= 13.0.3(?) substitutes nth capture where n>m with an empty string\nvar REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE = (function () {\n  if (/./[REPLACE]) {\n    return /./[REPLACE]('a', '$0') === '';\n  }\n  return false;\n})();\n\nvar REPLACE_SUPPORTS_NAMED_GROUPS = !fails(function () {\n  var re = /./;\n  re.exec = function () {\n    var result = [];\n    result.groups = { a: '7' };\n    return result;\n  };\n  // eslint-disable-next-line regexp/no-useless-dollar-replacements -- false positive\n  return ''.replace(re, '$<a>') !== '7';\n});\n\n// @@replace logic\nfixRegExpWellKnownSymbolLogic('replace', function (_, nativeReplace, maybeCallNative) {\n  var UNSAFE_SUBSTITUTE = REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE ? '$' : '$0';\n\n  return [\n    // `String.prototype.replace` method\n    // https://tc39.es/ecma262/#sec-string.prototype.replace\n    function replace(searchValue, replaceValue) {\n      var O = requireObjectCoercible(this);\n      var replacer = isNullOrUndefined(searchValue) ? undefined : getMethod(searchValue, REPLACE);\n      return replacer\n        ? call(replacer, searchValue, O, replaceValue)\n        : call(nativeReplace, toString(O), searchValue, replaceValue);\n    },\n    // `RegExp.prototype[@@replace]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@replace\n    function (string, replaceValue) {\n      var rx = anObject(this);\n      var S = toString(string);\n\n      if (\n        typeof replaceValue == 'string' &&\n        stringIndexOf(replaceValue, UNSAFE_SUBSTITUTE) === -1 &&\n        stringIndexOf(replaceValue, '$<') === -1\n      ) {\n        var res = maybeCallNative(nativeReplace, rx, S, replaceValue);\n        if (res.done) return res.value;\n      }\n\n      var functionalReplace = isCallable(replaceValue);\n      if (!functionalReplace) replaceValue = toString(replaceValue);\n\n      var global = rx.global;\n      var fullUnicode;\n      if (global) {\n        fullUnicode = rx.unicode;\n        rx.lastIndex = 0;\n      }\n\n      var results = [];\n      var result;\n      while (true) {\n        result = regExpExec(rx, S);\n        if (result === null) break;\n\n        push(results, result);\n        if (!global) break;\n\n        var matchStr = toString(result[0]);\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n      }\n\n      var accumulatedResult = '';\n      var nextSourcePosition = 0;\n      for (var i = 0; i < results.length; i++) {\n        result = results[i];\n\n        var matched = toString(result[0]);\n        var position = max(min(toIntegerOrInfinity(result.index), S.length), 0);\n        var captures = [];\n        var replacement;\n        // NOTE: This is equivalent to\n        //   captures = result.slice(1).map(maybeToString)\n        // but for some reason `nativeSlice.call(result, 1, result.length)` (called in\n        // the slice polyfill when slicing native arrays) \"doesn't work\" in safari 9 and\n        // causes a crash (https://pastebin.com/N21QzeQA) when trying to debug it.\n        for (var j = 1; j < result.length; j++) push(captures, maybeToString(result[j]));\n        var namedCaptures = result.groups;\n        if (functionalReplace) {\n          var replacerArgs = concat([matched], captures, position, S);\n          if (namedCaptures !== undefined) push(replacerArgs, namedCaptures);\n          replacement = toString(apply(replaceValue, undefined, replacerArgs));\n        } else {\n          replacement = getSubstitution(matched, S, position, captures, namedCaptures, replaceValue);\n        }\n        if (position >= nextSourcePosition) {\n          accumulatedResult += stringSlice(S, nextSourcePosition, position) + replacement;\n          nextSourcePosition = position + matched.length;\n        }\n      }\n\n      return accumulatedResult + stringSlice(S, nextSourcePosition);\n    }\n  ];\n}, !REPLACE_SUPPORTS_NAMED_GROUPS || !REPLACE_KEEPS_$0 || REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE);\n", "'use strict';\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fixRegExpWellKnownSymbolLogic = require('../internals/fix-regexp-well-known-symbol-logic');\nvar anObject = require('../internals/an-object');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar speciesConstructor = require('../internals/species-constructor');\nvar advanceStringIndex = require('../internals/advance-string-index');\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar getMethod = require('../internals/get-method');\nvar regExpExec = require('../internals/regexp-exec-abstract');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar fails = require('../internals/fails');\n\nvar UNSUPPORTED_Y = stickyHelpers.UNSUPPORTED_Y;\nvar MAX_UINT32 = 0xFFFFFFFF;\nvar min = Math.min;\nvar push = uncurryThis([].push);\nvar stringSlice = uncurryThis(''.slice);\n\n// Chrome 51 has a buggy \"split\" implementation when RegExp#exec !== nativeExec\n// Weex JS has frozen built-in prototypes, so use try / catch wrapper\nvar SPLIT_WORKS_WITH_OVERWRITTEN_EXEC = !fails(function () {\n  // eslint-disable-next-line regexp/no-empty-group -- required for testing\n  var re = /(?:)/;\n  var originalExec = re.exec;\n  re.exec = function () { return originalExec.apply(this, arguments); };\n  var result = 'ab'.split(re);\n  return result.length !== 2 || result[0] !== 'a' || result[1] !== 'b';\n});\n\nvar BUGGY = 'abbc'.split(/(b)*/)[1] === 'c' ||\n  // eslint-disable-next-line regexp/no-empty-group -- required for testing\n  'test'.split(/(?:)/, -1).length !== 4 ||\n  'ab'.split(/(?:ab)*/).length !== 2 ||\n  '.'.split(/(.?)(.?)/).length !== 4 ||\n  // eslint-disable-next-line regexp/no-empty-capturing-group, regexp/no-empty-group -- required for testing\n  '.'.split(/()()/).length > 1 ||\n  ''.split(/.?/).length;\n\n// @@split logic\nfixRegExpWellKnownSymbolLogic('split', function (SPLIT, nativeSplit, maybeCallNative) {\n  var internalSplit = '0'.split(undefined, 0).length ? function (separator, limit) {\n    return separator === undefined && limit === 0 ? [] : call(nativeSplit, this, separator, limit);\n  } : nativeSplit;\n\n  return [\n    // `String.prototype.split` method\n    // https://tc39.es/ecma262/#sec-string.prototype.split\n    function split(separator, limit) {\n      var O = requireObjectCoercible(this);\n      var splitter = isNullOrUndefined(separator) ? undefined : getMethod(separator, SPLIT);\n      return splitter\n        ? call(splitter, separator, O, limit)\n        : call(internalSplit, toString(O), separator, limit);\n    },\n    // `RegExp.prototype[@@split]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@split\n    //\n    // NOTE: This cannot be properly polyfilled in engines that don't support\n    // the 'y' flag.\n    function (string, limit) {\n      var rx = anObject(this);\n      var S = toString(string);\n\n      if (!BUGGY) {\n        var res = maybeCallNative(internalSplit, rx, S, limit, internalSplit !== nativeSplit);\n        if (res.done) return res.value;\n      }\n\n      var C = speciesConstructor(rx, RegExp);\n      var unicodeMatching = rx.unicode;\n      var flags = (rx.ignoreCase ? 'i' : '') +\n                  (rx.multiline ? 'm' : '') +\n                  (rx.unicode ? 'u' : '') +\n                  (UNSUPPORTED_Y ? 'g' : 'y');\n      // ^(? + rx + ) is needed, in combination with some S slicing, to\n      // simulate the 'y' flag.\n      var splitter = new C(UNSUPPORTED_Y ? '^(?:' + rx.source + ')' : rx, flags);\n      var lim = limit === undefined ? MAX_UINT32 : limit >>> 0;\n      if (lim === 0) return [];\n      if (S.length === 0) return regExpExec(splitter, S) === null ? [S] : [];\n      var p = 0;\n      var q = 0;\n      var A = [];\n      while (q < S.length) {\n        splitter.lastIndex = UNSUPPORTED_Y ? 0 : q;\n        var z = regExpExec(splitter, UNSUPPORTED_Y ? stringSlice(S, q) : S);\n        var e;\n        if (\n          z === null ||\n          (e = min(toLength(splitter.lastIndex + (UNSUPPORTED_Y ? q : 0)), S.length)) === p\n        ) {\n          q = advanceStringIndex(S, q, unicodeMatching);\n        } else {\n          push(A, stringSlice(S, p, q));\n          if (A.length === lim) return A;\n          for (var i = 1; i <= z.length - 1; i++) {\n            push(A, z[i]);\n            if (A.length === lim) return A;\n          }\n          q = p = e;\n        }\n      }\n      push(A, stringSlice(S, p));\n      return A;\n    }\n  ];\n}, BUGGY || !SPLIT_WORKS_WITH_OVERWRITTEN_EXEC, UNSUPPORTED_Y);\n", "'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this-clause');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar notARegExp = require('../internals/not-a-regexp');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar correctIsRegExpLogic = require('../internals/correct-is-regexp-logic');\nvar IS_PURE = require('../internals/is-pure');\n\nvar stringSlice = uncurryThis(''.slice);\nvar min = Math.min;\n\nvar CORRECT_IS_REGEXP_LOGIC = correctIsRegExpLogic('startsWith');\n// https://github.com/zloirock/core-js/pull/702\nvar MDN_POLYFILL_BUG = !IS_PURE && !CORRECT_IS_REGEXP_LOGIC && !!function () {\n  var descriptor = getOwnPropertyDescriptor(String.prototype, 'startsWith');\n  return descriptor && !descriptor.writable;\n}();\n\n// `String.prototype.startsWith` method\n// https://tc39.es/ecma262/#sec-string.prototype.startswith\n$({ target: 'String', proto: true, forced: !MDN_POLYFILL_BUG && !CORRECT_IS_REGEXP_LOGIC }, {\n  startsWith: function startsWith(searchString /* , position = 0 */) {\n    var that = toString(requireObjectCoercible(this));\n    notARegExp(searchString);\n    var index = toLength(min(arguments.length > 1 ? arguments[1] : undefined, that.length));\n    var search = toString(searchString);\n    return stringSlice(that, index, index + search.length) === search;\n  }\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar $trim = require('../internals/string-trim').trim;\nvar forcedStringTrimMethod = require('../internals/string-trim-forced');\n\n// `String.prototype.trim` method\n// https://tc39.es/ecma262/#sec-string.prototype.trim\n$({ target: 'String', proto: true, forced: forcedStringTrimMethod('trim') }, {\n  trim: function trim() {\n    return $trim(this);\n  }\n});\n", "'use strict';\nvar globalThis = require('../internals/global-this');\nvar DOMIterables = require('../internals/dom-iterables');\nvar DOMTokenListPrototype = require('../internals/dom-token-list-prototype');\nvar ArrayIteratorMethods = require('../modules/es.array.iterator');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayValues = ArrayIteratorMethods.values;\n\nvar handlePrototype = function (CollectionPrototype, COLLECTION_NAME) {\n  if (CollectionPrototype) {\n    // some Chrome versions have non-configurable methods on DOMTokenList\n    if (CollectionPrototype[ITERATOR] !== ArrayValues) try {\n      createNonEnumerableProperty(CollectionPrototype, ITERATOR, ArrayValues);\n    } catch (error) {\n      CollectionPrototype[ITERATOR] = ArrayValues;\n    }\n    setToStringTag(CollectionPrototype, COLLECTION_NAME, true);\n    if (DOMIterables[COLLECTION_NAME]) for (var METHOD_NAME in ArrayIteratorMethods) {\n      // some Chrome versions have non-configurable methods on DOMTokenList\n      if (CollectionPrototype[METHOD_NAME] !== ArrayIteratorMethods[METHOD_NAME]) try {\n        createNonEnumerableProperty(CollectionPrototype, METHOD_NAME, ArrayIteratorMethods[METHOD_NAME]);\n      } catch (error) {\n        CollectionPrototype[METHOD_NAME] = ArrayIteratorMethods[METHOD_NAME];\n      }\n    }\n  }\n};\n\nfor (var COLLECTION_NAME in DOMIterables) {\n  handlePrototype(globalThis[COLLECTION_NAME] && globalThis[COLLECTION_NAME].prototype, COLLECTION_NAME);\n}\n\nhandlePrototype(DOMTokenListPrototype, 'DOMTokenList');\n"], "sourceRoot": ""}