<?php
/**
 * Export Services Data Script
 * 
 * This script exports all service-related data to text files for comparison
 * Run this BEFORE importing production data
 */

require_once '../../../wp-config.php';
require_once '../../../wp-load.php';

global $wpdb;

echo "=== KIVICARE SERVICES DATA EXPORT ===" . PHP_EOL;
echo "Timestamp: " . current_time('Y-m-d H:i:s') . PHP_EOL;
echo "========================================" . PHP_EOL . PHP_EOL;

// Create exports directory if it doesn't exist
$export_dir = 'data_exports';
if (!file_exists($export_dir)) {
    mkdir($export_dir, 0755, true);
}

$timestamp = date('Y-m-d_H-i-s');

// 1. Export current services table
echo "1. Exporting current services table..." . PHP_EOL;
$services_file = $export_dir . '/services_' . $timestamp . '.txt';
$services = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}kc_services ORDER BY id");

$content = "=== SERVICES TABLE EXPORT ===" . PHP_EOL;
$content .= "Total Records: " . count($services) . PHP_EOL;
$content .= "Exported: " . current_time('Y-m-d H:i:s') . PHP_EOL;
$content .= "================================" . PHP_EOL . PHP_EOL;

foreach ($services as $service) {
    $content .= "ID: {$service->id}" . PHP_EOL;
    $content .= "Name: {$service->name}" . PHP_EOL;
    $content .= "Type: {$service->type}" . PHP_EOL;
    $content .= "Category ID: {$service->category_id}" . PHP_EOL;
    $content .= "Price: {$service->price}" . PHP_EOL;
    $content .= "Status: {$service->status}" . PHP_EOL;
    $content .= "Doctor ID: " . ($service->doctor_id ?? '0') . PHP_EOL;
    $content .= "Clinic ID: " . ($service->clinic_id ?? '0') . PHP_EOL;
    $content .= "Charges: " . ($service->charges ?? 'NULL') . PHP_EOL;
    $content .= "Extra: " . ($service->extra ?? 'NULL') . PHP_EOL;
    $content .= "Telemed Service: " . ($service->telemed_service ?? 'NULL') . PHP_EOL;
    $content .= "Duration: " . ($service->duration ?? 'NULL') . PHP_EOL;
    $content .= "Service Name Alias: " . ($service->service_name_alias ?? 'NULL') . PHP_EOL;
    $content .= "Multiple: " . ($service->multiple ?? 'NULL') . PHP_EOL;
    $content .= "Image: " . ($service->image ?? 'NULL') . PHP_EOL;
    $content .= "Description: " . ($service->description ?? 'NULL') . PHP_EOL;
    $content .= "Created At: {$service->created_at}" . PHP_EOL;
    $content .= "Updated At: " . ($service->updated_at ?? 'NULL') . PHP_EOL;
    $content .= "---" . PHP_EOL;
}

file_put_contents($services_file, $content);
echo "   Exported to: {$services_file}" . PHP_EOL;

// 2. Export old mapping table if it exists
echo "2. Checking for old mapping table..." . PHP_EOL;
$old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';
$current_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';

$mapping_table_to_export = null;
if ($wpdb->get_var("SHOW TABLES LIKE '{$old_mapping_table}'")) {
    $mapping_table_to_export = $old_mapping_table;
    echo "   Found old mapping table: {$old_mapping_table}" . PHP_EOL;
} elseif ($wpdb->get_var("SHOW TABLES LIKE '{$current_mapping_table}'")) {
    $mapping_table_to_export = $current_mapping_table;
    echo "   Found current mapping table: {$current_mapping_table}" . PHP_EOL;
} else {
    echo "   No mapping table found." . PHP_EOL;
}

if ($mapping_table_to_export) {
    $mappings_file = $export_dir . '/mappings_' . $timestamp . '.txt';
    $mappings = $wpdb->get_results("SELECT * FROM {$mapping_table_to_export} ORDER BY id");
    
    $content = "=== SERVICE DOCTOR MAPPINGS EXPORT ===" . PHP_EOL;
    $content .= "Table: {$mapping_table_to_export}" . PHP_EOL;
    $content .= "Total Records: " . count($mappings) . PHP_EOL;
    $content .= "Exported: " . current_time('Y-m-d H:i:s') . PHP_EOL;
    $content .= "======================================" . PHP_EOL . PHP_EOL;
    
    foreach ($mappings as $mapping) {
        $content .= "ID: {$mapping->id}" . PHP_EOL;
        $content .= "Service ID: {$mapping->service_id}" . PHP_EOL;
        $content .= "Doctor ID: {$mapping->doctor_id}" . PHP_EOL;
        $content .= "Clinic ID: {$mapping->clinic_id}" . PHP_EOL;
        $content .= "Charges: {$mapping->charges}" . PHP_EOL;
        $content .= "Status: {$mapping->status}" . PHP_EOL;
        $content .= "Extra: " . ($mapping->extra ?? 'NULL') . PHP_EOL;
        $content .= "Telemed Service: " . ($mapping->telemed_service ?? 'NULL') . PHP_EOL;
        $content .= "Duration: " . ($mapping->duration ?? 'NULL') . PHP_EOL;
        $content .= "Service Name Alias: " . ($mapping->service_name_alias ?? 'NULL') . PHP_EOL;
        $content .= "Multiple: " . ($mapping->multiple ?? 'NULL') . PHP_EOL;
        $content .= "Image: " . ($mapping->image ?? 'NULL') . PHP_EOL;
        $content .= "Description: " . ($mapping->description ?? 'NULL') . PHP_EOL;
        $content .= "Created At: {$mapping->created_at}" . PHP_EOL;
        $content .= "---" . PHP_EOL;
    }
    
    file_put_contents($mappings_file, $content);
    echo "   Exported to: {$mappings_file}" . PHP_EOL;
}

// 3. Export appointment service mappings
echo "3. Exporting appointment service mappings..." . PHP_EOL;
$apt_mappings_file = $export_dir . '/appointment_mappings_' . $timestamp . '.txt';
$apt_mappings = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}kc_appointment_service_mapping ORDER BY id");

$content = "=== APPOINTMENT SERVICE MAPPINGS EXPORT ===" . PHP_EOL;
$content .= "Total Records: " . count($apt_mappings) . PHP_EOL;
$content .= "Exported: " . current_time('Y-m-d H:i:s') . PHP_EOL;
$content .= "===========================================" . PHP_EOL . PHP_EOL;

foreach ($apt_mappings as $apt_mapping) {
    $content .= "ID: {$apt_mapping->id}" . PHP_EOL;
    $content .= "Appointment ID: {$apt_mapping->appointment_id}" . PHP_EOL;
    $content .= "Service ID: {$apt_mapping->service_id}" . PHP_EOL;
    $content .= "Created At: {$apt_mapping->created_at}" . PHP_EOL;
    $content .= "---" . PHP_EOL;
}

file_put_contents($apt_mappings_file, $content);
echo "   Exported to: {$apt_mappings_file}" . PHP_EOL;

// 4. Export summary statistics
echo "4. Generating summary statistics..." . PHP_EOL;
$summary_file = $export_dir . '/summary_' . $timestamp . '.txt';

$stats = [];
$stats['total_services'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}kc_services");
$stats['active_services'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}kc_services WHERE status = 1");
$stats['services_with_doctors'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}kc_services WHERE doctor_id > 0");
$stats['services_without_doctors'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}kc_services WHERE doctor_id = 0");

if ($mapping_table_to_export) {
    $stats['total_mappings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$mapping_table_to_export}");
    $stats['active_mappings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$mapping_table_to_export} WHERE status = 1");
}

$stats['total_appointment_mappings'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}kc_appointment_service_mapping");

$content = "=== EXPORT SUMMARY STATISTICS ===" . PHP_EOL;
$content .= "Export Date: " . current_time('Y-m-d H:i:s') . PHP_EOL;
$content .= "=================================" . PHP_EOL . PHP_EOL;

foreach ($stats as $key => $value) {
    $content .= ucwords(str_replace('_', ' ', $key)) . ": {$value}" . PHP_EOL;
}

file_put_contents($summary_file, $content);
echo "   Summary saved to: {$summary_file}" . PHP_EOL;

echo PHP_EOL . "=== EXPORT COMPLETED ===" . PHP_EOL;
echo "All files saved in: {$export_dir}/" . PHP_EOL;
echo "Files created:" . PHP_EOL;
echo "- {$services_file}" . PHP_EOL;
if (isset($mappings_file)) echo "- {$mappings_file}" . PHP_EOL;
echo "- {$apt_mappings_file}" . PHP_EOL;
echo "- {$summary_file}" . PHP_EOL;
?>
