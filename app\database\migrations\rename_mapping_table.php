<?php

/**
 * Rename Service Doctor Mapping Table
 * 
 * This script safely renames the wp_kc_service_doctor_mapping table to wp_kc_service_doctor_mapping_old
 * after confirming the enhanced services migration is working correctly.
 * 
 * IMPORTANT: Only run this after thoroughly testing the enhanced services functionality!
 */

use App\baseClasses\KCBase;

class RenameMappingTableMigration extends KCBase {

    public function up() {
        global $wpdb;
        
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        $old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';
        $services_table = $wpdb->prefix . 'kc_services';
        
        // Safety checks before renaming
        $this->performSafetyChecks($services_table, $mapping_table);
        
        // Rename the mapping table to _old
        $wpdb->query("RENAME TABLE {$mapping_table} TO {$old_mapping_table}");
        
        return true;
    }
    
    private function performSafetyChecks($services_table, $mapping_table) {
        global $wpdb;
        
        // Check if enhanced services exist
        $enhanced_services = $wpdb->get_var("SELECT COUNT(*) FROM {$services_table} WHERE doctor_id > 0");
        if ($enhanced_services == 0) {
            throw new Exception("No enhanced services found. Migration may not have completed properly.");
        }
        
        // Check if mapping table exists
        if (!$wpdb->get_var("SHOW TABLES LIKE '{$mapping_table}'")) {
            throw new Exception("Mapping table does not exist.");
        }
        
        // Compare counts
        $mapping_count = $wpdb->get_var("SELECT COUNT(*) FROM {$mapping_table}");
        if ($enhanced_services < $mapping_count) {
            throw new Exception("Enhanced services count ({$enhanced_services}) is less than mapping count ({$mapping_count}). This may indicate incomplete migration.");
        }
        
        // Check for orphaned appointment references
        $appointment_table = $wpdb->prefix . 'kc_appointment_service_mapping';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$appointment_table}'")) {
            $orphaned = $wpdb->get_var("
                SELECT COUNT(*) 
                FROM {$appointment_table} asm 
                LEFT JOIN {$services_table} s ON asm.service_id = s.id 
                WHERE s.id IS NULL AND asm.service_id > 0
            ");
            
            if ($orphaned > 0) {
                throw new Exception("Found {$orphaned} orphaned appointment service references. Fix these before renaming the mapping table.");
            }
        }
        
        // Check if old table already exists
        $old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$old_mapping_table}'")) {
            throw new Exception("Old mapping table already exists. Please remove it first or choose a different name.");
        }
    }
    
    public function down() {
        global $wpdb;
        
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        $old_mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping_old';
        
        // Restore the table by renaming it back
        if ($wpdb->get_var("SHOW TABLES LIKE '{$old_mapping_table}'")) {
            $wpdb->query("RENAME TABLE {$old_mapping_table} TO {$mapping_table}");
        } else {
            throw new Exception("Cannot restore mapping table. Old table does not exist.");
        }
    }
}

// Manual execution function (uncomment to run)
/*
function rename_service_mapping_table() {
    try {
        $migration = new RenameMappingTableMigration();
        $migration->up();
        echo "Service doctor mapping table renamed to _old successfully.\n";
        update_option('kc_mapping_table_renamed', true);
    } catch (Exception $e) {
        echo "Failed to rename mapping table: " . $e->getMessage() . "\n";
    }
}

// Uncomment the line below to execute
// rename_service_mapping_table();
*/
