<style>
  :root {
    --wp--preset--font-family--heading: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
  }

  body {
    align-items: normal !important;
  }

  .text-violet-600 {
    --tw-text-opacity: 1;
    color: rgb(124 58 237 / var(--tw-text-opacity));
  }

  .bg-violet-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(124 58 237 / var(--tw-bg-opacity));
  }

  .bg-violet-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 243 255 / var(--tw-bg-opacity));
  }

  .text-fuchsia-600 {
    --tw-text-opacity: 1;
    color: rgb(192 38 211 / var(--tw-text-opacity));
  }

  .bg-fuchsia-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(250 232 255 / var(--tw-bg-opacity));
  }

  .bg-fuchsia-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(192 38 211 / var(--tw-text-opacity));
  }

  .text-pink-600 {
    --tw-text-opacity: 1;
    color: rgb(219 39 119 / var(--tw-text-opacity));
  }

  .bg-pink-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 242 248 / var(--tw-bg-opacity));
  }

  .bg-pink-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(219 39 119 / var(--tw-bg-opacity));
  }

  .text-rose-600 {
    --tw-text-opacity: 1;
    color: rgb(225 29 72 / var(--tw-text-opacity));
  }

  .bg-rose-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(225 29 72 / var(--tw-bg-opacity));
  }

  .bg-rose-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 228 230 / var(--tw-bg-opacity));
  }

  .content-wrapper,
  body {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  }

  .contact-box-inline .select2-container--default .select2-selection--single {
  background-color: #fff !important;
  color: #6E7990;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: 5px;
  padding: 9px 16px;
  height: auto;
}

.contact-box-inline .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #6E7990;
}

.contact-box-inline .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 100%;
}

.select2-container--open .select2-dropdown--below:not(.kivicare-custom-dropdown-width), .select2-container--open .select2-dropdown--above:not(.kivicare-custom-dropdown-width) {
width: var(--kc-country-code-width) !important;
}

.multiselect .select2-container--default {
  display: block;
}

.kivi-widget .select2-container--default .select2-selection--single .select2-selection__arrow:before {
  opacity: 0;
}

.contact-box-inline .select2-container--default .select2-selection--single,
.select2-container .select2-selection--single{
  height: auto !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered{
  line-height: normal !important;
}

.select2-results__option{
  text-align: left;
}

/* Payment Result Styles */
.kivi-payment-result {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  border-radius: 0.5rem;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.kivi-payment-success .kivi-payment-result-icon {
  color: #10b981; /* Green */
  background-color: rgba(16, 185, 129, 0.1);
}

.kivi-payment-error .kivi-payment-result-icon {
  color: #ef4444; /* Red */
  background-color: rgba(239, 68, 68, 0.1);
}

.kivi-payment-result-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 1rem;
}

.kivi-payment-result-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
}

.kivi-payment-result-message {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 1.5rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.kivi-payment-result-details {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: left;
}

.kivi-payment-result-details p {
  margin-bottom: 0.5rem;
}

.kivi-payment-result-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #111827;
}

.kivi-payment-result-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.kivi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-decoration: none;
}

.kivi-btn-primary {
  background-color: var(--primary-color, #4f46e5);
  color: white !important;
}

.kivi-btn-primary:hover {
  background-color: var(--primary-color-dark, #4338ca);
  color: white !important;
}

.kivi-btn-secondary {
  background-color: #f3f4f6;
  color: #374151 !important;
  border: 1px solid #d1d5db;
}

.kivi-btn-secondary:hover {
  background-color: #e5e7eb;
  color: #111827 !important;
}

@media (max-width: 640px) {
  .kivi-payment-result {
    padding: 1.5rem;
  }

  .kivi-payment-result-actions {
    flex-direction: column;
  }

  .kivi-payment-result-icon {
    width: 64px;
    height: 64px;
  }
}
</style>
<!-- We don't need to load Vue directly as it's already included in the front-app.min.js file -->

<div class="kivi-widget">
  <?php
  // Check for payment status parameters in the URL
  $appointment_id = isset($_GET['appointment_id']) ? sanitize_text_field($_GET['appointment_id']) : '';
  $kivicare_stripe_payment = isset($_GET['kivicare_stripe_payment']) ? sanitize_text_field($_GET['kivicare_stripe_payment']) : '';
  $kivicare_payment = isset($_GET['kivicare_payment']) ? sanitize_text_field($_GET['kivicare_payment']) : '';
  $stripe_success = isset($_GET['stripe_success']) ? sanitize_text_field($_GET['stripe_success']) : '';
  $stripe_cancel = isset($_GET['stripe_cancel']) ? sanitize_text_field($_GET['stripe_cancel']) : '';

  // If payment status parameters are found, update the database and display appropriate content
  if (!empty($appointment_id) &&
      (($kivicare_stripe_payment === 'success' || $kivicare_payment === 'success' || $stripe_success === '1' || $stripe_success === 'true'))) {
    // Update payment status to approved
    global $wpdb;
    $wpdb->update($wpdb->prefix . "kc_payments_appointment_mappings",
      ['payment_status' => 'approved'],
      ['appointment_id' => $appointment_id]
    );

    // Update appointment status to confirmed (1)
    $wpdb->update($wpdb->prefix . "kc_appointments",
      ['status' => 1],
      ['id' => $appointment_id]
    );

    // Set a flag to show success content directly in the appointment page
    $payment_success = true;

    // Trigger any hooks that might be needed for payment completion
    do_action('kc_appointment_payment_complete', $appointment_id);

  } else if (!empty($appointment_id) &&
            (($kivicare_stripe_payment === 'failed' || $kivicare_payment === 'failed' || $stripe_cancel === '1' || $stripe_cancel === 'true'))) {
    // Update payment status to failed
    global $wpdb;
    $wpdb->update($wpdb->prefix . "kc_payments_appointment_mappings",
      ['payment_status' => 'failed'],
      ['appointment_id' => $appointment_id]
    );

    // Set a flag to show error content directly in the appointment page
    $payment_failed = true;
  }

  $theme_mode = get_option(KIVI_CARE_PREFIX . 'theme_mode');
  $rtl_attr = in_array($theme_mode, ['1', 'true']) ? 'rtl' : '';

  // Prepare data for Vue component
  $services_data = $service_data = new \stdClass();
  if ($shortcode_service_id != 0) {
    $service_condition_query = " ";
    if ($shortcode_clinic_id != 0) {
      $shortcode_clinic_id_query = (int) $shortcode_clinic_id;
      $service_condition_query .= " AND map.clinic_id = {$shortcode_clinic_id_query} ";
    }
    if ($shortcode_doctor_id != 0) {
      $shortcode_doctor_id_query = implode(',', array_filter(array_map('absint', explode(',', $shortcode_doctor_id))));
      $service_condition_query .= " AND map.doctor_id IN ({$shortcode_doctor_id_query}) ";
    }
    global $wpdb;
    $service_query = "SELECT ser.*,ser.type, ser.name FROM {$wpdb->prefix}kc_services AS ser
            WHERE ser.status=1 AND ser.id={$shortcode_service_id} AND ser.doctor_id > 0 {$service_condition_query} ";
    $services_data = $wpdb->get_results($service_query);
  }

  $printConfirmPage = !empty($_GET['confirm_page']) ? sanitize_text_field(wp_unslash($_GET['confirm_page'])) : 'off';
  $allPaymentMethod = kcAllPaymentMethodList();
  ?>

  <!-- Payment Success Content -->
  <?php if (isset($payment_success) && $payment_success === true): ?>
    <div class="kivi-payment-success">
      <div class="kivi-payment-result">
        <div class="kivi-payment-result-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        </div>
        <h2 class="kivi-payment-result-title">Payment Successful</h2>
        <p class="kivi-payment-result-message">Your appointment has been confirmed and payment has been processed successfully.</p>

        <?php if (!empty($appointment_id)):
          // Get appointment details
          global $wpdb;
          $appointment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}kc_appointments WHERE id = %d",
            $appointment_id
          ));

          if ($appointment):
            // Get doctor name
            $doctor_name = $wpdb->get_var($wpdb->prepare(
              "SELECT display_name FROM {$wpdb->users} WHERE ID = %d",
              $appointment->doctor_id
            ));

            // Get clinic name
            $clinic_name = $wpdb->get_var($wpdb->prepare(
              "SELECT name FROM {$wpdb->prefix}kc_clinics WHERE id = %d",
              $appointment->clinic_id
            ));
        ?>
        <div class="kivi-payment-result-details">
          <h3>Appointment Details</h3>
          <p><strong>Appointment ID:</strong> <?php echo esc_html($appointment_id); ?></p>
          <p><strong>Date:</strong> <?php echo esc_html(date('F j, Y', strtotime($appointment->appointment_start_date))); ?></p>
          <p><strong>Time:</strong> <?php echo esc_html(date('g:i A', strtotime($appointment->appointment_start_time))); ?></p>
          <?php if (!empty($doctor_name)): ?>
          <p><strong>Doctor:</strong> <?php echo esc_html($doctor_name); ?></p>
          <?php endif; ?>
          <?php if (!empty($clinic_name)): ?>
          <p><strong>Clinic:</strong> <?php echo esc_html($clinic_name); ?></p>
          <?php endif; ?>
        </div>
        <?php endif; endif; ?>

        <!-- <div class="kivi-payment-result-actions">
          <a href="<?php echo esc_url(home_url('/')); ?>" class="kivi-btn kivi-btn-primary">Return to Home</a>
        </div> -->
      </div>
    </div>

  <!-- Payment Failed Content -->
  <?php elseif (isset($payment_failed) && $payment_failed === true): ?>
    <div class="kivi-payment-error">
      <div class="kivi-payment-result">
        <div class="kivi-payment-result-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        </div>
        <h2 class="kivi-payment-result-title">Payment Failed</h2>
        <p class="kivi-payment-result-message">We were unable to process your payment. Please try again or choose another payment method.</p>

        <div class="kivi-payment-result-actions">
          <a href="<?php echo esc_url(home_url('/appointment/')); ?>" class="kivi-btn kivi-btn-primary">Try Again</a>
        </div>
      </div>
    </div>

  <!-- Normal Appointment Booking Widget -->
  <?php else: ?>
    <div id="appointment-booking-container" dir='<?php echo esc_html($rtl_attr); ?>'>
      <appointment-booking-widget
        :preset-clinic-id="<?php echo esc_attr($shortcode_clinic_id_single ?: 0); ?>"
        :preset-doctor-id="<?php echo esc_attr($shortcode_doctor_id_single ?: 0); ?>"
        :preset-service-id="<?php echo esc_attr($shortcode_service_id ?: 0); ?>"
      ></appointment-booking-widget>
    </div>
  <?php endif; ?>

</div>


<script>
  <?php
  $widgetSetting = json_decode(get_option(KIVI_CARE_PREFIX . 'widgetSetting'), true);
  ?>

  // Define ajaxurl and nonce for Vue components
  var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
  var ajaxData = {
    ajaxurl: ajaxurl,
    nonce: '<?php echo wp_create_nonce('ajax_post'); ?>',
    get_nonce: '<?php echo wp_create_nonce('ajax_get'); ?>'
  };
  var pluginBASEURL = '<?php echo KIVI_CARE_DIR_URI; ?>';

  // Set theme colors
  <?php if (!empty($widgetSetting['primaryColor'])) { ?>
    document.documentElement.style.setProperty("--primary-color", '<?php echo esc_js(!empty($widgetSetting['primaryColor']) ? $widgetSetting['primaryColor'] : '#7093e5'); ?>');
  <?php } ?>

  <?php if (!empty($widgetSetting['primaryHoverColor'])) { ?>
    document.documentElement.style.setProperty("--primary-color-dark", '<?php echo esc_js(!empty($widgetSetting['primaryHoverColor']) ? $widgetSetting['primaryHoverColor'] : '#4367b9'); ?>');
  <?php } ?>

  <?php if (!empty($widgetSetting['secondaryColor'])) { ?>
    document.documentElement.style.setProperty("--secondary-color", '<?php echo esc_js(!empty($widgetSetting['secondaryColor']) ? $widgetSetting['secondaryColor'] : '#f68685'); ?>');
  <?php } ?>

  <?php if (!empty($widgetSetting['secondaryHoverColor'])) { ?>
    document.documentElement.style.setProperty("--secondary-color-dark", '<?php echo esc_js(!empty($widgetSetting['secondaryHoverColor']) ? $widgetSetting['secondaryHoverColor'] : '#df504e'); ?>');
  <?php } ?>

  // Initialize Vue app when DOM is loaded
  document.addEventListener("DOMContentLoaded", function() {
    // The Vue component will handle all the functionality
    console.log('Appointment booking widget loaded');
    var vueLoaded = typeof window.Vue !== "undefined";
    console.log('Vue loaded:', vueLoaded);
    console.log('App element:', document.getElementById("appointment-booking-container"));



    // Function to load Vue.js if it's not already loaded
    function loadVueJs() {
      if (typeof window.Vue === "undefined") {
        console.log('Loading Vue.js directly...');

        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js';
        script.onload = function() {
          console.log('Vue.js loaded directly');

          initVueApp();
        };
        script.onerror = function() {
          console.error('Failed to load Vue.js directly');

        };
        document.head.appendChild(script);
      } else {
        initVueApp();
      }
    }

    // Function to initialize Vue app
    function initVueApp() {
      if (typeof window.Vue !== "undefined") {
        var components = Vue.options ? Object.keys(Vue.options.components) : "Not available";
        console.log('Vue components:', components);

        // Register the appointment-booking-widget component if it's not already registered
        if (!Vue.options.components['appointment-booking-widget']) {
          console.log('Registering fallback appointment-booking-widget component');
          Vue.component('appointment-booking-widget', {
            template: `
              <div class="fallback-booking-widget">
                <h2>Appointment Booking</h2>
                <p>The Vue component failed to load properly. Please try the following:</p>
                <ul>
                  <li>Refresh the page</li>
                  <li>Clear your browser cache</li>
                  <li>Try a different browser</li>
                </ul>
                <p>If the problem persists, please contact the administrator.</p>
              </div>
            `,
            data: function() {
              return {
                message: 'Fallback Appointment Booking Widget'
              };
            }
          });
        }

        // Explicitly initialize Vue app for the appointment booking widget
        if (document.getElementById("appointment-booking-container")) {
          try {
            new Vue({
              el: '#appointment-booking-container'
            });
            console.log('Vue app initialized for appointment booking widget');
          } catch (e) {
            console.error('Error initializing Vue app:', e);

          }
        }
      } else {
        console.log('Vue not loaded yet, retrying in 500ms');

        setTimeout(initVueApp, 500);
      }
    }

    // Start initialization process
    loadVueJs();
  });
</script>