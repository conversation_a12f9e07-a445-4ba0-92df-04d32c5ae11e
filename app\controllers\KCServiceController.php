<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCAppointmentServiceMapping;
use App\models\KCService;
use App\models\KCServiceDoctorMapping;
use App\models\KCReceptionistClinicMapping;
use App\models\KCClinic;
use App\models\KCDoctorClinicMapping;
use Exception;
use WP_User;
use function Clue\StreamFilter\fun;

class KCServiceController extends KCBase
{

    public $db;
    /**
     * @var KCRequest
     */
    private $request;

    public $exclude_service;
    public function __construct()
    {

        global $wpdb;

        $this->db = $wpdb;

        $this->request = new KCRequest();

        parent::__construct();
    }

/**
 * Extracts the minutes value from the duration data
 *
 * @param mixed $duration Duration data from request (could be object, array, string or integer)
 * @return int Total minutes
 *
 * Note: A duration of 0 minutes means the service won't block doctor availability
 * when booked. This is useful for services that don't require dedicated time slots.
 */
private function extractDurationMinutes($duration) {
    // Handle empty values or explicit 0
    if (empty($duration) || $duration === 0 || $duration === '0') {
        return 0;
    }

    // If it's already a numeric value, just return it
    if (is_numeric($duration)) {
        return (int) $duration;
    }

    // If duration is a value object from the frontend dropdown
    if (is_array($duration) && isset($duration['value'])) {
        return (int) $duration['value'];
    }

    // If it's a JSON string, try to decode it
    if (is_string($duration) && (strpos($duration, '{') === 0 || strpos($duration, '[') === 0)) {
        $decoded = json_decode($duration, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($decoded['value'])) {
                return (int) $decoded['value'];
            }
        }
    }

    // Handle time string format (HH:MM)
    if (is_string($duration) && strpos($duration, ':') !== false) {
        $parts = explode(':', $duration);
        if (count($parts) === 2) {
            $hours = (int) $parts[0];
            $minutes = (int) $parts[1];
            return ($hours * 60) + $minutes;
        }
    }

    // Last resort: try to cast to integer
    return (int) $duration;
}

    public function index()
    {

        if (!kcCheckPermission('service_list') && is_user_logged_in()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        $request_data = $this->request->getInputs();
        $service_table = $this->db->prefix . 'kc_services';
        $users_table = $this->db->base_prefix . 'users';
        $clinic_doctor_mapping = $this->db->prefix . 'kc_doctor_clinic_mappings';
        $clinic_table = $this->db->prefix . 'kc_clinics';
        //current login user role
        $current_login_user_role = $this->getLoginUserRole();

        //current login user id
        $current_login_user_id = get_current_user_id();

        //default query condition value
        $search_condition = $doctor_condition = $clinic_condition = $paginationCondition = $clinic_service_condition = " ";
        $orderByCondition = " ORDER BY {$service_table}.id  DESC ";

        //check request is from new appointment book shortcode/widget
        $request_from_new_appointment_widget = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';

        //check request is from new appointment book shortcode/widget and check doctor id empty or not valid id
        $request_from_new_appointment_widget_and_service_first = $request_from_new_appointment_widget && (empty($request_data['doctor_id']) || in_array($request_data['doctor_id'], [0, '0']));

        //check request from service module (listing)
        $request_from_service_module = !empty($request_data['type']) && $request_data['type'] === 'list';

        //check request is from new appointment book shortcode/widget
        if ($request_from_new_appointment_widget) {
            if (!empty($request_data['searchKey'])) {
                $request_data['searchKey'] = esc_sql($request_data['searchKey']);
                $searchKey = $request_data['searchKey'];
                //search query condition
                $search_condition = " AND ({$service_table}.name LIKE '%{$searchKey}%' OR {$service_table}.type LIKE '%{$searchKey}%' OR {$service_table}.charges LIKE '%{$searchKey}%')";
            }
        } else if ($request_from_service_module) {
            // If perPage is set and greater than 0, apply pagination
            // If perPage is 0, return all records (no pagination)
            if (isset($request_data['perPage']) && (int) $request_data['perPage'] > 0) {
                $perPage = (int) $request_data['perPage'];
                $offset = ((int) $request_data['page'] - 1) * $perPage;
                $paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
            }
            $orderByCondition = " ORDER BY id DESC ";
            if (!empty($request_data['sort'])) {
                $request_data['sort'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['sort'][0]), true));
                if (!empty($request_data['sort']['field']) && !empty($request_data['sort']['type']) && $request_data['sort']['type'] !== 'none') {
                    $sortField = esc_sql($request_data['sort']['field']);
                    $sortByValue = esc_sql(strtoupper($request_data['sort']['type']));
                    switch ($request_data['sort']['field']) {
                        case 'charges':
                        case 'status':
                        case 'id':
                        case 'duration':
                        case 'service_id':
                            $orderByCondition = " ORDER BY {$service_table}.{$sortField} {$sortByValue} ";
                            break;
                        case 'name':
                            $orderByCondition = " ORDER BY {$service_table}.{$sortField} {$sortByValue} ";
                            break;
                        case 'doctor_name':
                            $orderByCondition = " ORDER BY {$users_table}.display_name {$sortByValue} ";
                            break;
                        case 'service_type':
                            $orderByCondition = " ORDER BY {$service_table}.type {$sortByValue} ";
                            break;
                    }
                }
            }

            if (isset($request_data['searchTerm']) && trim($request_data['searchTerm']) !== '') {
                $request_data['searchTerm'] = esc_sql(strtolower(trim($request_data['searchTerm'])));
                $search_condition .= " AND (
                           {$service_table}.id LIKE '%{$request_data['searchTerm']}%'
                           OR {$service_table}.name LIKE '%{$request_data['searchTerm']}%'
                           OR {$users_table}.display_name LIKE '%{$request_data['searchTerm']}%'
                           OR {$service_table}.charges LIKE '%{$request_data['searchTerm']}%'
                           OR {$service_table}.type LIKE '%{$request_data['searchTerm']}%'
                           OR {$service_table}.status LIKE '%{$request_data['searchTerm']}%'
                           ) ";
            } else {
                if (!empty($request_data['columnFilters'])) {
                    $request_data['columnFilters'] = json_decode(stripslashes($request_data['columnFilters']), true);
                    foreach ($request_data['columnFilters'] as $column => $searchValue) {
                        $searchValue = !empty($searchValue) ? $searchValue : '';
                        $searchValue = esc_sql(strtolower(trim($searchValue)));
                        $column = esc_sql($column);
                        if ($searchValue === '') {
                            continue;
                        }
                        switch ($column) {
                            case 'charges':
                            case 'status':
                            case 'id':
                            case 'duration':
                                if ($column === 'duration') {
                                    list($hours, $minutes) = explode(":", $searchValue);
                                    $searchValue = ((int) $hours * 60) + (int) $minutes;
                                }
                                $search_condition .= " AND {$service_table}.{$column} LIKE '%{$searchValue}%' ";
                                break;
                            case 'service_id':
                                $search_condition .= " AND {$service_table}.id LIKE '%{$searchValue}%' ";
                                break;
                            case 'name':
                                $search_condition .= " AND {$service_table}.{$column} LIKE '%{$searchValue}%' ";
                                break;
                            case 'doctor_name':
                                $search_condition .= " AND {$users_table}.display_name LIKE '%{$searchValue}%' ";
                                break;
                            case 'service_type':
                                $search_condition .= " AND {$service_table}.type LIKE '%{$searchValue}%'";
                                break;
                            case 'clinic_name':
                                $search_condition .= " AND {$clinic_table}.name LIKE '%{$searchValue}%'";
                                break;
                        }
                    }
                }
            }
        }

        //check if login user is doctor or request data have valid doctor id
        if (($this->getDoctorRole() === $current_login_user_role) || (isset($request_data['doctor_id']) && !in_array($request_data['doctor_id'], [0, '0']))) {

            //doctor id
            $doctor_id = $this->getDoctorRole() === $current_login_user_role ? $current_login_user_id : $request_data['doctor_id'];
            //doctor query condition
            if (str_contains($doctor_id, ',')) {
                $doctor_id = implode(',', array_map('absint', explode(',', $doctor_id)));
                $doctor_condition = " AND {$service_table}.doctor_id IN ({$doctor_id}) ";
            } else {
                $doctor_id = (int) $doctor_id;
                $doctor_condition = " AND {$service_table}.doctor_id = {$doctor_id} ";
            }

        }

        $telemed_condition = " AND ({$service_table}.telemed_service != 'yes' OR {$service_table}.telemed_service IS NULL )  ";
        if (isKiviCareTelemedActive() || isKiviCareGoogleMeetActive()) {
            $telemed_condition = "  ";
        }

        // get only active service list in appointment book
        $active_services = $request_from_service_module ? " " : " AND {$service_table}.status = '1' ";
        $full_service_name = " {$service_table}.name,{$service_table}.type,{$service_table}.doctor_id,{$service_table}.clinic_id ";
        if ($request_from_new_appointment_widget) {
            if (
                $request_from_new_appointment_widget_and_service_first ||
                (isset($request_data['doctor_id']) && !in_array($request_data['doctor_id'], [0, '0'])
                    && (empty($request_data['doctor_id']) || in_array($request_data['clinic_id'], [0, '0'])))
            ) {
                $full_service_name = " {$service_table}.name,{$service_table}.type ";
            }
        }

        if ($request_from_new_appointment_widget && isKiviCareProActive()) {
            //get clinic id wise service list
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
                $clinic_service_condition = " AND {$service_table}.clinic_id = {$request_data['clinic_id']} ";
            }
        } else {
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                case 'administrator':
                    if ($request_from_service_module) {
                        $request_data['clinic_id'] = '';
                    } else {
                        $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    }
                    break;
                case $this->getClinicAdminRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                case $this->getReceptionistRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfReceptionist();
                    break;
                case $this->getPatientRole():
                    $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    break;
            }
            if (!empty($request_data['clinic_id'])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
                $clinic_service_condition = " AND {$service_table}.clinic_id = {$request_data['clinic_id']} ";
            }
        }

        $preselected_service_condition = ' ';
        if (!empty($request_data['preselected_service'])) {
            $request_data['preselected_service'] = implode(',', array_filter(array_map('absint', explode(',', $request_data['preselected_service']))));
            if (!empty($request_data['preselected_service'])) {
                $preselected_service_condition = " AND {$service_table}.id IN ({$request_data['preselected_service']}) ";
            }
        }

        //query for service list
        $query = "SELECT {$service_table}.*,{$service_table}.charges as service_base_price,
                  CONCAT({$full_service_name}) AS full_service_name,
                  {$service_table}.name AS name, {$service_table}.type AS service_type, {$service_table}.created_at AS created_at,
                  {$users_table}.display_name AS doctor_name ,{$clinic_table}.name AS clinic_name
                   FROM {$service_table}
                  JOIN {$clinic_table} ON {$service_table}.clinic_id = {$clinic_table}.id
                  JOIN {$users_table} ON {$users_table}.ID = {$service_table}.doctor_id
                  JOIN {$clinic_doctor_mapping} ON {$clinic_doctor_mapping}.doctor_id = {$service_table}.doctor_id
                  AND {$clinic_doctor_mapping}.clinic_id = {$service_table}.clinic_id
                  WHERE {$service_table}.doctor_id > 0 {$doctor_condition} {$clinic_condition} {$clinic_service_condition} {$active_services} {$telemed_condition} {$search_condition} {$preselected_service_condition}
                 {$orderByCondition}";

        $total = 0;
        if ($request_from_service_module) {
            $total = $this->db->get_var("SELECT count(*)  FROM {$service_table}
                  JOIN {$users_table} ON {$users_table}.ID = {$service_table}.doctor_id
                  JOIN {$clinic_table} ON {$service_table}.clinic_id = {$clinic_table}.id
                  JOIN {$clinic_doctor_mapping} ON {$clinic_doctor_mapping}.doctor_id = {$service_table}.doctor_id
                  AND {$clinic_doctor_mapping}.clinic_id = {$service_table}.clinic_id
                  WHERE {$service_table}.doctor_id > 0 {$doctor_condition} {$clinic_condition} {$clinic_service_condition} {$active_services} {$telemed_condition} {$search_condition} ");

            $query .= $paginationCondition;
        }

        $clinicCurrenySetting = kcGetClinicCurrenyPrefixAndPostfix();
        $clinic_prefix = !empty($clinicCurrenySetting['prefix']) ? $clinicCurrenySetting['prefix'] : '';
        $clinic_postfix = !empty($clinicCurrenySetting['postfix']) ? $clinicCurrenySetting['postfix'] : '';
        //get unique service (full_service_name = service_name + service_category_name + service_doctor_id)


        $services = collect($this->db->get_results($query))->unique('full_service_name')->map(function ($services) use ($clinic_prefix, $clinic_postfix, $request_data, $request_from_new_appointment_widget_and_service_first) {
            $services->charges = round((float) $services->charges, 3);
            $services->clinic_name = decodeSpecificSymbols($services->clinic_name);
            $services->service_base_price = round((float) $services->service_base_price, 3);
            //service image
            $services->image = !empty($services->image) ? wp_get_attachment_url($services->image) : '';
            //service category name format
            $services->service_type = !empty($services->service_type) ? str_replace('_', ' ', $services->service_type) : "";
            //check if service name is telemed
            if ($services->telemed_service === 'yes') {
                //get category name of telemed service (updated category name of telemed service)
                $services->service_type = !empty($services->service_name_alias) ? str_replace("_", " ", $services->service_name_alias) : $services->service_type;
            }

            if ($request_from_new_appointment_widget_and_service_first) {
                //change service charges as base service price
                $services->charges = $clinic_prefix . $services->service_base_price . $clinic_postfix;
            } else {
                if (empty($request_data['without_currency']) || (!empty($request_data['without_currency']) && $request_data['without_currency'] !== 'yes')) {
                    $services->charges = $clinic_prefix . $services->charges . $clinic_postfix;
                }
            }



            $services->share_link = add_query_arg(
                array(
                    'service_id' => $services->id,
                    'doctor_id' => $services->doctor_id,
                    'clinic_id' => $services->clinic_id,
                    'step' => 'datetime', // Add step parameter to start from date-time selection
                ),
                site_url(
                    '/appointment'
                )
            );
            return $services;
        })->values();

        if (empty($services) || count($services) < 1) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No services found', 'kc-lang'),
                'data' => []
            ]);
        } else {
            $request_data['request_from_new_appointment_widget_and_service_first'] = $request_from_new_appointment_widget_and_service_first;
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Service list', 'kc-lang'),
                'data' => $services,
                'total_rows' => $request_from_service_module ? $total : count($services),
                'html' => $request_from_new_appointment_widget ? $this->kcCreateServiceListHtml($services, $request_data) : ''
            ]);
        }
    }

    public function getClinicServices() {
        if (!kcCheckPermission('service_list') && is_user_logged_in()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        // Handle specific route and module type parameters
        if (!empty($request_data['module_type']) && $request_data['module_type'] === 'appointment_form') {
            // Handle doctor_id if provided
            if (!empty($request_data['doctor_id'])) {
                $request_data['doctor_id'] = (int)$request_data['doctor_id'];
            }

            // Handle clinic_id array if provided
            if (!empty($request_data['clinic_id']) && is_array($request_data['clinic_id'])) {
                $request_data['clinic_id'] = (int)$request_data['clinic_id'][0];
            }
        }

        // Check if request is from appointment widget
        $request_from_new_appointment_widget = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';
        $request_from_new_appointment_widget_and_service_first = $request_from_new_appointment_widget &&
            (empty($request_data['doctor_id']) || in_array($request_data['doctor_id'], [0, '0']));
        $request_from_service_module = !empty($request_data['type']) && $request_data['type'] === 'list';

        // Define table names
        $service_table = $this->db->prefix . 'kc_services';
        $users_table = $this->db->base_prefix . 'users';
        $clinic_doctor_mapping = $this->db->prefix . 'kc_doctor_clinic_mappings';
        $clinic_table = $this->db->prefix . 'kc_clinics';

        // Get clinic ID based on request or user role
        if ($request_from_new_appointment_widget && isKiviCareProActive()) {
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
            }
        } else {
            $current_login_user_role = $this->getLoginUserRole();
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                case 'administrator':
                    if ($request_from_service_module) {
                        $request_data['clinic_id'] = '';
                    } else {
                        $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    }
                    break;
                case $this->getClinicAdminRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                case $this->getReceptionistRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfReceptionist();
                    break;
                case $this->getPatientRole():
                    $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    break;
            }
        }

        $clinic_service_condition = '';
        if (!empty($request_data['clinic_id'])) {
            $request_data['clinic_id'] = (int) $request_data['clinic_id'];
            $clinic_service_condition = " AND {$service_table}.clinic_id = {$request_data['clinic_id']} ";
        }

        // Add doctor_id condition if specified
        $doctor_condition = '';
        if (!empty($request_data['doctor_id'])) {
            $doctor_id = (int) $request_data['doctor_id'];
            $doctor_condition = " AND {$service_table}.doctor_id = {$doctor_id} ";
        }

        $full_service_name = " {$service_table}.name,{$service_table}.type,{$service_table}.doctor_id,{$service_table}.clinic_id ";
        if ($request_from_new_appointment_widget) {
            if ($request_from_new_appointment_widget_and_service_first) {
                $full_service_name = " {$service_table}.name,{$service_table}.type ";
            }
        }
        $service_category_condition='';
        if (!empty($request_data['service_category'])) {
            $category = $request_data['service_category'];

            // Sanitize the category to prevent SQL injection
            $category = esc_sql($category);

            // Check if category is numeric (category_id) or string (category slug/name)
            if (is_numeric($category)) {
                // Use category_id for new category system
                $service_category_condition = " AND {$service_table}.category_id = " . (int)$category;
            } else {
                // Fallback to old type field or category slug lookup
                $service_category_condition = " AND (
                    {$service_table}.type = '{$category}' OR
                    {$service_table}.category_id IN (
                        SELECT id FROM {$this->db->prefix}kc_categories
                        WHERE (slug = '{$category}' OR name = '{$category}')
                        AND module_type = 'service' AND status = 1
                    )
                ) ";
            }

            if(isset($request_data['doctor_id']) && !empty($request_data['doctor_id'])){
                $request_data['doctor_id'] = (int) $request_data['doctor_id'];
                $service_category_condition .= " AND {$service_table}.doctor_id = ".$request_data['doctor_id'];
            }
        }

        // Query for service list
        $query = "SELECT {$service_table}.*,
                  {$service_table}.charges as service_base_price,
                  CONCAT({$full_service_name}) AS full_service_name,
                  {$service_table}.name AS name,
                  {$service_table}.type AS service_type,
                  {$service_table}.created_at AS created_at,
                  {$users_table}.display_name AS doctor_name,
                  {$clinic_table}.name AS clinic_name
                  FROM {$service_table}
                  JOIN {$clinic_table} ON {$service_table}.clinic_id = {$clinic_table}.id
                  JOIN {$users_table} ON {$users_table}.ID = {$service_table}.doctor_id
                  JOIN {$clinic_doctor_mapping} ON {$clinic_doctor_mapping}.doctor_id = {$service_table}.doctor_id
                  AND {$clinic_doctor_mapping}.clinic_id = {$service_table}.clinic_id
                  WHERE {$service_table}.doctor_id > 0 {$clinic_service_condition} {$doctor_condition}
                  AND {$service_table}.status = '1'
                  {$service_category_condition}
                  ORDER BY {$service_table}.id DESC";

        $total = $request_from_service_module ?
            $this->db->get_var(preg_replace('/SELECT.*?FROM/', 'SELECT COUNT(*) FROM', $query)) : 0;

        // Get clinic currency settings
        $clinicCurrencySetting = kcGetClinicCurrenyPrefixAndPostfix();
        $clinic_prefix = !empty($clinicCurrencySetting['prefix']) ? $clinicCurrencySetting['prefix'] : '';
        $clinic_postfix = !empty($clinicCurrencySetting['postfix']) ? $clinicCurrencySetting['postfix'] : '';

        // Get and process services
        $services =  collect($this->db->get_results($query));
        if(empty($request_data['service_category'])){
            $services->unique('full_service_name');
        }
        $services = $services->map(function ($services)
            use ($clinic_prefix, $clinic_postfix, $request_data, $request_from_new_appointment_widget_and_service_first) {

            $services->charges = round((float) $services->charges, 3);
            $services->clinic_name = decodeSpecificSymbols($services->clinic_name);
            $services->service_base_price = round((float) $services->service_base_price, 3);
            $services->image = !empty($services->image) ? wp_get_attachment_url($services->image) : '';
            $services->service_type = !empty($services->service_type) ? str_replace('_', ' ', $services->service_type) : "";

            if ($services->telemed_service === 'yes') {
                $services->service_type = !empty($services->service_name_alias) ?
                    str_replace("_", " ", $services->service_name_alias) : $services->service_type;
            }

            if ($request_from_new_appointment_widget_and_service_first) {
                $services->charges = $clinic_prefix . $services->service_base_price . $clinic_postfix;
            } else {
                if (empty($request_data['without_currency']) ||
                    (!empty($request_data['without_currency']) && $request_data['without_currency'] !== 'yes')) {
                    $services->charges = $clinic_prefix . $services->charges . $clinic_postfix;
                }
            }

            $services->share_link = add_query_arg(
                array(
                    'service_id' => $services->service_id,
                    'doctor_id' => $services->doctor_id,
                    'clinic_id' => $services->clinic_id,
                    'step' => 'datetime', // Add step parameter to start from date-time selection
                ),
                site_url('/appointment')
            );

            return $services;
        })->values();

        if (empty($services) || count($services) < 1) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No services found', 'kc-lang'),
                'data' => []
            ]);
        }

        $request_data['request_from_new_appointment_widget_and_service_first'] = $request_from_new_appointment_widget_and_service_first;
        // Create debug info for troubleshooting if needed
        $debug_info = [];
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $debug_info = [
                'category_condition' => $service_category_condition,
                'request_category' => isset($request_data['service_category']) ? $request_data['service_category'] : 'none',
                'query' => $query,
                'service_count' => count($services)
            ];
        }

        wp_send_json([
            'status' => true,
            'message' => esc_html__('Service list', 'kc-lang'),
            'data' => $services,
            'total_rows' => $request_from_service_module ? $total : count($services),
            'html' => $this->kcCreateServiceListHtml($services, $request_data),
            'debug' => $debug_info
        ]);
    }

    public function getClinicServicesCategory() {
        if (!kcCheckPermission('service_list') && is_user_logged_in()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        global $wpdb;
        $request_data = $this->request->getInputs();

        // Check if request is from appointment widget
        $request_from_new_appointment_widget = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';
        $request_from_new_appointment_widget_and_service_first = $request_from_new_appointment_widget &&
            (empty($request_data['doctor_id']) || in_array($request_data['doctor_id'], [0, '0']));
        $request_from_service_module = !empty($request_data['type']) && $request_data['type'] === 'list';

        // Get clinic ID based on request or user role
        if ($request_from_new_appointment_widget && isKiviCareProActive()) {
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
            }
        } else {
            $current_login_user_role = $this->getLoginUserRole();
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                case 'administrator':
                    if ($request_from_service_module) {
                        $request_data['clinic_id'] = '';
                    } else {
                        $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    }
                    break;
                case $this->getClinicAdminRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                case $this->getReceptionistRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfReceptionist();
                    break;
                case $this->getPatientRole():
                    $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    break;
            }
        }

        $query_condition = '';
        if(isset($request_data['searchKey']) && !empty($request_data['searchKey'])){
            $searchKey = sanitize_title($request_data['searchKey']);
            $query_condition .= " AND s.name LIKE '%$searchKey%' ";
        }

        // Determine visibility filter based on request source
        $visibility_filter = $request_from_new_appointment_widget ? ['public'] : ['public', 'backend_only'];
        $visibility_placeholders = implode(',', array_fill(0, count($visibility_filter), '%s'));

        // Use new categories system - get categories with service counts
        $params = array_merge([$request_data['clinic_id']], $visibility_filter);

        $query = $wpdb->prepare("
            SELECT
                c.id as category_id,
                c.name as category_name,
                c.id as category_value,
                c.visibility as category_visibility,
                COUNT(DISTINCT s.id) as service_count,
                MIN(s.name) as service_name,
                MIN(s.clinic_id) as clinic_id,
                MIN(s.charges) as charges
            FROM {$wpdb->prefix}kc_categories c
            INNER JOIN {$wpdb->prefix}kc_services s ON c.id = s.category_id
            WHERE s.status = 1
                AND s.doctor_id > 0
                AND s.clinic_id = %d
                AND c.module_type = 'service'
                AND c.status = 1
                AND c.visibility IN ({$visibility_placeholders})
                AND s.visibility IN ({$visibility_placeholders})
                {$query_condition}
            GROUP BY c.id, c.name, c.slug, c.visibility
            HAVING service_count > 0
            ORDER BY c.sort_order ASC, c.name ASC
        ", array_merge($params, $visibility_filter));

        $services = $wpdb->get_results($query);

        $total = $request_from_service_module ?
            $this->db->get_var(preg_replace('/SELECT.*?FROM/', 'SELECT COUNT(*) FROM', $query)) : 0;


        if (empty($services) || count($services) < 1) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No Category found', 'kc-lang'),
                'data' => []
            ]);
        }

        $request_data['request_from_new_appointment_widget_and_service_first'] = $request_from_new_appointment_widget_and_service_first;

        ob_start(); ?>
            <div class="space-y-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <?php foreach ($services as $key => $value) : ?>
                        <div class="category-item">
                            <?php
                            // Use the category data from the new system
                            $category_id = $value->category_id;
                            $category_value = $value->category_value; // This is the slug
                            $category_name = $value->category_name;

                            // Only show category if it has a valid name and services
                            if (!empty($category_name) && !empty($value->service_count)) :
                            ?>
                            <input id="category_<?php echo esc_attr($category_id); ?>" type="radio"
                                   name="service_category"
                                   value="<?php echo esc_attr($category_id); ?>"
                                   class="hidden peer service-category"
                                   data-count="<?php echo intval($value->service_count); ?>"
                                   data-slug="<?php echo esc_attr($category_value); ?>" />
                            <label for="category_<?php echo esc_attr($category_id); ?>" class="bg-gray-50 rounded-lg shadow-sm p-4 max-w-sm border-2 border-transparent checked:border-blue-500 block cursor-pointer">
                                <div>
                                    <span class="font-medium text-gray-900">
                                        <?php echo esc_html($category_name); ?>
                                    </span>
                                    <?php if (WP_DEBUG && current_user_can('manage_options')) : ?>
                                    <span class="text-xs text-gray-400 block">(<?php echo intval($value->service_count); ?> services)</span>
                                    <?php endif; ?>
                                </div>
                            </label>
                            <?php endif; ?>
                        </div>
                <?php endforeach; ?>
                </div>
            </div>
            <?php $service_category_html = ob_get_clean();


        // Create debug info for troubleshooting if needed
        $debug_info = [];
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $debug_info = [
                'query' => $query,
                'services' => $services,
                'service_count' => count($services),
                'clinic_id' => $request_data['clinic_id']
            ];
        }

        wp_send_json([
            'status' => true,
            'message' => esc_html__('Service list', 'kc-lang'),
            'data' => $services,
            'total_rows' => $request_from_service_module ? $total : count($services),
            'html' => $service_category_html,
            'debug' => $debug_info
        ]);
    }

    public function save()
    {

        global $wpdb;

        if (!kcCheckPermission('service_add')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        $request_data['price'] = round((float) $request_data['price'], 3);
        $service_table = $this->db->prefix . 'kc_services';
        $pro_plugin_active = isKiviCareProActive();
        $service = new KCService();
        $service_doctor_mapping = new KCServiceDoctorMapping();

        //current login user role
        $current_login_user_role = $this->getLoginUserRole();

        //current login user id
        $current_login_user_id = get_current_user_id();


        //service data
        $temp = [
            'name' => $request_data['name'],
            'price' => $request_data['price'],
            'type' => str_replace(' ', '_', strtolower($request_data['type']['label'])),
            'category_id' => !empty($request_data['category_id']) ? (int)$request_data['category_id'] : null,
            'visibility' => !empty($request_data['visibility']) ? $request_data['visibility'] : 'public',
            'sort_order' => !empty($request_data['sort_order']) ? (int)$request_data['sort_order'] : 0,
            'status' => $request_data['status']['id'],
            'updated_at' => current_time('Y-m-d H:i:s')
        ];
        $telemedTemp = $temp;

        //change category name of telemed service to default
        // if($request_data['telemed_service'] === 'yes'){
        //     $temp['type'] = 'system_service';
        // }

        //get clinic_id
        $clinic_id = kcGetDefaultClinicId();
        if (isKiviCareProActive()) {
            switch ($current_login_user_role) {
                case $this->getReceptionistRole():
                    $clinic_id = kcGetClinicIdOfReceptionist();
                    $request_data['clinic_id'] = [$clinic_id];
                    break;
                case $this->getClinicAdminRole():
                    $clinic_id = kcGetClinicIdOfClinicAdmin();
                    $request_data['clinic_id'] = [$clinic_id];
                    break;
                default:
                    if (is_array($request_data['clinic_id']) && !empty($request_data['clinic_id'][0]['id'])) {
                        $request_data['clinic_id'] = collect($request_data['clinic_id'])->pluck('id')->map(function ($v) {
                            return (int) $v;
                        })->toArray();
                    } else {
                        if (!empty($request_data['clinic_id']['id'])) {
                            $clinic_id = (int) $request_data['clinic_id']['id'];
                        }
                        $request_data['clinic_id'] = [$clinic_id];
                    }
                    break;
            }
        }

        //validation query to check same data exists
        $validationQuery = "SELECT id FROM {$service_table} WHERE 0=0 ";

        //service image attachment id
        $attachment_id = 0;
        if (isset($request_data['profile_image']) && !empty((int) $request_data['profile_image'])) {
            $attachment_id = $request_data['profile_image'];
        }

        if (empty($request_data['id'])) {
            //get doctor id from request data
            if ($current_login_user_role === $this->getDoctorRole()) {
                $doctor_id = [$current_login_user_id];
            } else {
                $doctor_id = collect($request_data['doctor_id'])->pluck('id')->map(function ($v) {
                    return (int) $v;
                })->toArray();
            }
            $implode_clinic_condition = '';
            if (!empty($request_data['clinic_id'])) {
                //implode clinic id for query
                $implode_clinic_id = implode(',', $request_data['clinic_id']);
                $implode_clinic_condition = " clinic_id IN ({$implode_clinic_id}) AND ";
            }
            //implode doctor id for query
            $implode_doctor_id = implode(',', $doctor_id);
            //clinic wise doctor array
            $clinic_doctors = collect($this->db->get_results("SELECT GROUP_CONCAT(doctor_id) AS doctor_id, clinic_id FROM
            {$this->db->prefix}kc_doctor_clinic_mappings WHERE $implode_clinic_condition doctor_id IN ({$implode_doctor_id}) GROUP BY clinic_id"))->keyBy('clinic_id')->map(function ($v) {
                return explode(',', $v->doctor_id);
            })->toArray();

            //check if same service exists with same doctor
            foreach ($clinic_doctors as $clinic_doctor_key => $clinic_doctor_val) {
                $doctor_ids = implode(',', $clinic_doctor_val);
                $validationRow = $wpdb->get_var($validationQuery . " AND doctor_id
                IN ({$doctor_ids}) AND clinic_id = $clinic_doctor_key
                 AND type ='{$temp['type']}' AND name='{$request_data['name']}'");
                if (!empty($validationRow)) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Same Service Already Exists,Please select Different category or service name', 'kc-lang'),
                        'data' => []
                    ]);
                }
            }

            $temp['created_at'] = current_time('Y-m-d H:i:s');
            $service_id = $wpdb->get_var("SELECT id FROM {$service_table} WHERE type='{$temp['type']}' AND name='{$temp['name']}'");
            if (empty($service_id)) {
                $service_id = $service->insert($temp);
            }

            if ($service_id) {
                foreach ($clinic_doctors as $clinic_id => $clinic_doctor_val) {
                    foreach ($clinic_doctor_val as $doctor) {
                        // Create enhanced service with doctor/clinic mapping
                        $enhanced_service_data = [
                            'name' => $temp['name'],
                            'price' => $temp['price'],
                            'type' => $temp['type'],
                            'category_id' => $temp['category_id'],
                            'visibility' => $temp['visibility'],
                            'sort_order' => $temp['sort_order'],
                            'clinic_id' => (int) $clinic_id,
                            'doctor_id' => (int) $doctor,
                            'charges' => $request_data['price'],
                            'status' => (int) $request_data['status']['id'],
                            'image' => $attachment_id,
                            'multiple' => $request_data['multiservice']['id'],
                            'telemed_service' => $request_data['telemed_service'],
                            'service_name_alias' => $request_data['telemed_service'] === 'yes' ? $temp['type'] : null,
                            'created_at' => current_time('Y-m-d H:i:s'),
                            'updated_at' => current_time('Y-m-d H:i:s')
                        ];

                        $enhanced_service_data = apply_filters('kivicare_update_service_mapping_save_fields', $enhanced_service_data, $request_data);

                        if ($pro_plugin_active && !empty($request_data['duration'])) {
                            $enhanced_service_data['duration'] = $this->extractDurationMinutes($request_data['duration']);
                        }
                        $service_doctor_mapping->insert($enhanced_service_data);
                        // hook for service add.
                        do_action('kc_service_add', $enhanced_service_data);
                    }
                }
            }

            $message = esc_html__('Service saved successfully', 'kc-lang');

            kcLogActivity(
                'create_service',
                sprintf(esc_html__('Service has been created successfully', 'kc-lang'), $service_id),
                [
                    'service_id' => $service_id,
                ]
            );

        } else {
            $request_data['id'] = (int) $request_data['id'];
            if (!(new KCServiceDoctorMapping())->serviceUserPermission($request_data['id'])) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $clinic_id = isset($request_data['clinic_id'][0]) && !empty($request_data['clinic_id'][0])
                ? (int) $request_data['clinic_id'][0]
                : (isset($request_data['clinic_id']['id']) && !empty($request_data['clinic_id']['id'])
                    ? (int) $request_data['clinic_id']['id']
                    : kcGetDefaultClinicId());

            $doctor_id = $current_login_user_role === $this->getDoctorRole() ? $current_login_user_id : (int) $request_data['doctor_id']['id'];

            //check if same service exists with same doctor
            $validationRow = $wpdb->get_var($validationQuery . " AND doctor_id = {$doctor_id}
             AND clinic_id = {$clinic_id} AND type ='{$temp['type']}'
            AND name='{$request_data['name']}' AND id != {$request_data['id']} ");
            if (!empty($validationRow)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Same Service Already Exists,Please select Different category or service name', 'kc-lang'),
                    'data' => []
                ]);
            }

            $service_id = $wpdb->get_var("SELECT id FROM {$service_table} WHERE type='{$temp['type']}' AND name='{$temp['name']}'");
            if (empty($service_id)) {
                $temp['created_at'] = current_time('Y-m-d H:i:s');
                $request_data['service_id'] = $service->insert($temp);
            } else {
                $request_data['service_id'] = (int) $request_data['service_id'];
                $service->update([
                    'name' => $request_data['name']
                ], array('id' => $request_data['service_id']));
            }

            // Update enhanced service data
            $service_update_data = [
                'name' => $request_data['name'],
                'price' => $request_data['price'],
                'type' => $temp['type'],
                'category_id' => $temp['category_id'],
                'visibility' => $temp['visibility'],
                'sort_order' => $temp['sort_order'],
                'clinic_id' => $clinic_id,
                'doctor_id' => $doctor_id,
                'charges' => $request_data['price'],
                'status' => (int) $request_data['status']['id'],
                'multiple' => $request_data['multiservice']['id'],
                'telemed_service' => $request_data['telemed_service'],
                'updated_at' => current_time('Y-m-d H:i:s')
            ];

            $service_update_data = apply_filters('kivicare_update_service_mapping_save_fields', $service_update_data, $request_data);

            if ($pro_plugin_active && !empty($request_data['duration'])) {
                $service_update_data['duration'] = $this->extractDurationMinutes($request_data['duration']);
            }
            if (!empty($attachment_id)) {
                $service_update_data['image'] = $attachment_id;
            }

            if ($request_data['telemed_service'] === 'yes') {
                $service_update_data['service_name_alias'] = $temp['type'];
            } else {
                $service_update_data['service_name_alias'] = null;
            }

            $old_service_id_before_update = $this->db->get_var("SELECT id FROM {$service_table} WHERE id={$request_data['id']}");

            if ($request_data['service_id'] !== (int) $old_service_id_before_update) {
                (new KCAppointmentServiceMapping())->update(['service_id' => $request_data['service_id']], ['service_id' => (int) $old_service_id_before_update]);
            }

            $service_doctor_mapping->update($service_update_data, array('id' => $request_data['id']));

            $product_id = $this->getProductIdOfService($request_data['id']);
            if (!empty($product_id) && get_post_status($product_id)) {
                update_post_meta($product_id, '_price', $request_data['price']);
                update_post_meta($product_id, '_sale_price', $request_data['price']);
                if (!empty($attachment_id)) {
                    update_post_meta($product_id, '_thumbnail_id', $attachment_id);
                }

                $my_post = array(
                    'ID' => $product_id,
                    'post_title' => $request_data['name'],
                );
                wp_update_post($my_post);
            }

            $service_update_data['id'] = $request_data['id'];

            do_action('kc_service_update', $service_update_data);

            $message = esc_html__('Service updated successfully', 'kc-lang');

            kcLogActivity(
                'update_service',
                sprintf(esc_html__('Service has been updated successfully', 'kc-lang'), $service_id),
                [
                    'service_id' => $service_id,
                ]
            );
        }

        wp_send_json([
            'status' => true,
            'message' => $message
        ]);

    }

    public function edit()
    {


        if (!kcCheckPermission('service_edit') || !kcCheckPermission('service_view')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        try {

            if (!isset($request_data['id'])) {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
            }

            $edit_id = (int) $request_data['id'];

            if (!(new KCServiceDoctorMapping())->serviceUserPermission($edit_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            $service_table = $this->db->prefix . 'kc_services';
            $users_table = $this->db->base_prefix . 'users';
            $clinic_table = $this->db->prefix . 'kc_clinics';
            $categories_table = $this->db->prefix . 'kc_categories';

            $query = " SELECT {$service_table}.id AS mapping_id, {$service_table}.id AS service_id,
                              {$service_table}.*, {$users_table}.display_name AS doctor_name,
                              {$service_table}.charges AS doctor_charges,
                              {$service_table}.status AS mapping_status,
                              {$clinic_table}.name AS clinic_name,
                              {$categories_table}.id AS category_id,
                              {$categories_table}.name AS category_name,
                              {$categories_table}.slug AS category_slug
                       FROM  {$service_table}
					   JOIN  {$users_table} ON {$users_table}.ID = {$service_table}.doctor_id
                       JOIN  {$clinic_table} ON {$clinic_table}.id = {$service_table}.clinic_id
					   LEFT JOIN {$categories_table} ON {$service_table}.category_id = {$categories_table}.id
					   WHERE {$service_table}.id = {$edit_id} ";


            $service = $this->db->get_row($query);

            if (count((array) $service)) {

                $status = new \stdClass();
                $status->id = 0;
                $status->label = 'Inactive';

                if ((int) $service->mapping_status === 1) {
                    $status->id = 1;
                    $status->label = 'Active';
                }

                $temp = [
                    'id' => $service->mapping_id,
                    'service_id' => $service->service_id,
                    'name' => $service->name,
                    'price' => round((float) $service->doctor_charges, 3),
                    'doctor_id' => [
                        'id' => $service->doctor_id,
                        'label' => $service->doctor_name
                    ],
                    'clinic_id' => [
                        'id' => $service->clinic_id,
                        'label' => decodeSpecificSymbols($service->clinic_name)
                    ],
                    'category_id' => $service->category_id,
                    'category' => !empty($service->category_name) ? [
                        'id' => $service->category_id,
                        'label' => $service->category_name,
                        'slug' => $service->category_slug
                    ] : null,
                    'visibility' => $service->visibility ?? 'public',
                    'sort_order' => $service->sort_order ?? 0,
                    // 'duration'=> !empty($service->duration) ? $service->duration : '',
                    'type' => [
                        'id' => $service->type,
                        'label' => str_replace('_', " ", $service->type)
                    ],
                    'telemed_service' => $service->telemed_service,
                    'status' => $status,
                    'image' => !empty($service->image) ? wp_get_attachment_url($service->image) : ''
                ];

                if (!empty($service->multiple)) {
                    $temp['multiservice'] = [
                        'id' => strtolower($service->multiple),
                        'label' => $service->multiple
                    ];
                } else {
                    $temp['multiservice'] = [
                        'id' => 'yes',
                        'label' => __("yes", "kc-lang")
                    ];
                }
                if ($service->telemed_service === 'yes') {
                    if (!empty($service->service_name_alias)) {
                        $temp['type'] = [
                            'id' => str_replace(" ", "_", strtolower($service->service_name_alias)),
                            'label' => $service->service_name_alias
                        ];
                    } else {
                        $temp['type'] = [
                            'id' => $service->type,
                            'label' => str_replace('_', " ", $service->type)
                        ];
                    }
                }

                $hours = floor($service->duration / 60);
                $minutes = (int) $service->duration % 60;
                $temp['duration'] = sprintf("%02d:%02d", $hours, $minutes);
                $temp = apply_filters('kivicare_update_service_edit_fields', $temp, $service);
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Service data', 'kc-lang'),
                    'data' => $temp
                ]);

            } else {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
            }


        } catch (Exception $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            header("Status: $code $message");
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete()
    {

        if (!kcCheckPermission('service_delete')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        try {

            if (!isset($request_data['id'])) {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
            }

            $service_doctor_mapping = new KCServiceDoctorMapping();

            $request_data['id'] = (int) $request_data['id'];

            if (!(new KCServiceDoctorMapping())->serviceUserPermission($request_data['id'])) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            $product_id = $this->getProductIdOfService($request_data['id']);
            if (!empty($product_id) && get_post_status($product_id)) {
                do_action('kc_woocoomerce_service_delete', $product_id);
                wp_delete_post($product_id);
            }

            $id = $request_data['id'];
            $results = $service_doctor_mapping->delete(['id' => $id]);

            if ($results) {
                kcLogActivity(
                    'delete_service',
                    sprintf(esc_html__('Service has been deleted successfully', 'kc-lang'), $id),
                    [
                        'service_id' => $id,
                    ]
                );

                do_action('kc_service_delete', $id);
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Service has been deleted successfully', 'kc-lang'),
                ]);
            } else {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Failed to delete Service', 'kc-lang'), 400));
            }


        } catch (Exception $e) {

            $code = $e->getCode();
            $message = $e->getMessage();

            header("Status: $code $message");

            wp_send_json([
                'status' => false,
                'message' => $message
            ]);
        }
    }

    //	public function clinicService () {
//	    $table = $this->db->prefix  . 'kc_services' ;
//        $query = "SELECT `id`, `type`, `name`, `price` FROM {$table} " ;
//        $services = $this->db->get_results( $query, OBJECT );
//        wp_send_json([
//            'status' => true,
//            'message' => esc_html__('Clinic service list', 'kc-lang'),
//            'data' => $services
//        ]);
//    }

    public function getProductIdOfService($id)
    {
        $id = (int) $id;
        $product_id = '';
        $service_table = $this->db->prefix . 'kc_services';
        $data = $this->db->get_var('select extra from ' . $service_table . ' where id=' . $id);
        if ($data != null) {
            $data = json_decode($data);
            $product_id = $data->product_id;
        }
        return $product_id;
    }
    public function kcCreateServiceListHtml($services, $request_data) {
        $services = $services->sortBy('service_type')->groupBy('service_type');
        $showServicetImageStatus = kcGetSingleWidgetSetting('showServiceImage');
        $showServicetypeStatus = kcGetSingleWidgetSetting('showServicetype');
        $showServicePriceStatus = kcGetSingleWidgetSetting('showServicePrice');
        $showServiceDurationStatus = kcGetSingleWidgetSetting('showServiceDuration');
        $total_service_category = count($services);

        ob_start();

        ?>
         <div class="grid grid-cols-1 sm:grid-cols-2  gap-4">
            <?php
        foreach ($services as $key => $main_services) {
            ?>

                <?php
                $total_service = count($main_services);
                foreach ($main_services as $service) {
                    $singleServiceClass = '';
                    if (!empty($service->multiple) && $service->multiple == 'no') {
                        $singleServiceClass = ' selected-service-single';
                    }
                    $prechecked = ' ';
                    $is_checked_class='';
                    if ($total_service == 1 && $total_service_category == 1) {
                        $singleServiceClass = ' selected-service-single ';
                        $is_checked_class = ' border-purple-500 bg-purple-50 ';
                        $prechecked = ' checked ';
                    }
                    if (!empty($request_data['request_from_new_appointment_widget_and_service_first'])) {
                        $singleServiceClass = ' selected-service-single';
                    }



                    //get doctor session days
                    $results = collect($this->db->get_results("SELECT DISTINCT day FROM {$this->db->prefix}kc_clinic_sessions where doctor_id={$service->doctor_id} AND clinic_id={$service->clinic_id}"))->pluck('day')->toArray();
                    $leaves = collect($this->db->get_results("SELECT * FROM {$this->db->prefix}kc_clinic_schedule where module_id ={$service->doctor_id} AND module_type = 'doctor'"))->toArray();

                    //week day for php vue appointment dashboard
                    $days = [1 => 'sun', 2 => 'mon', 3 =>'tue', 4 => 'wed', 5 => 'thu', 6 => 'fri', 7 => 'sat'];
                    //week day for php shortcode widget
                    if(!empty($request_data['type']) && $request_data['type'] === 'flatpicker'){
                        $days = [0 => 'sun', 1 => 'mon', 2 =>'tue', 3 => 'wed', 4 => 'thu', 5 => 'fri', 6 => 'sat'];
                    }


                    if(count($results) > 0){
                        // get unavilable  days
                    $results = array_diff(array_values($days),$results);
                    //get key of unavilable days
                    $results = array_map(function ($v) use ($days){
                        return array_search($v,$days);
                    },$results);
                    $results = array_values($results);
                    }
                    else{
                        //get all days keys
                        $results = array_keys($days);
                    }

                    $next_available_date=  kc_get_next_available_date([
                         'days' => $results,
                         'holiday' => $leaves
                    ]);

                    // Determine service type
                    $serviceType = ($service->telemed_service === 'yes') ? 'virtual' : 'clinic';
                    ?>
                    <!-- Parent container for each card -->
                    <div class="service-item <?php echo esc_attr($serviceType); ?>">
                        <!-- Hidden checkbox for selection -->
                        <input
                            type="checkbox"
                            class="hidden peer card-checkbox selected-service <?php echo esc_html($singleServiceClass); ?>"
                            name="card_main"
                            id="service_<?php echo esc_html($service->id); ?>"
                            value="<?php echo esc_html($service->id); ?>"
                            service_id="<?php echo esc_html($service->service_id); ?>"
                            service_name="<?php echo esc_html($service->name); ?>"
                            service_price="<?php echo esc_html($service->charges); ?>"
                            doctor_id="<?php echo esc_html($service->doctor_id); ?>"
                            clinic_id="<?php echo esc_html($service->clinic_id); ?>"
                            status="<?php echo esc_html($service->status); ?>"
                            created_at="<?php echo esc_html($service->created_at); ?>"
                            doctor_name="<?php echo esc_html($service->doctor_name); ?>"
                            service_type="<?php echo esc_html($serviceType); ?>"
                            multipleService="<?php echo esc_html(!empty($service->multiple) && $service->multiple == 'no' ? 'no' : 'yes'); ?>"
                            <?php echo esc_html($prechecked); ?>/>

                        <!-- Label acts as the card; clicking it checks the box -->
                        <label
                            for="service_<?php echo esc_html($service->id); ?>"
                            class="block w-full p-4 bg-gray-50 border border-gray-200 rounded-lg shadow-sm cursor-pointer hover:border-indigo-200 transition-colors peer-checked:border-indigo-500 <?php echo esc_attr($is_checked_class)?>" >
                            <!-- Top row: service name + price -->
                            <div class="flex justify-between items-start">
                                <div class="text-left">
                                    <h3 class="text-base font-medium text-gray-900">
                                        <?php echo esc_html($service->name); ?>
                                    </h3>
                                    <!-- Doctor’s name (optional) -->
                                    <?php if (!empty($service->doctor_name)): ?>
                                        <p class="text-sm text-indigo-600 mt-1">
                                            Dr <?php echo esc_html($service->doctor_name); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>

                                <!-- Price info -->
                                <?php if ($showServicePriceStatus): ?>
                                    <div class="ml-2 text-right">
                                        <span class="text-lg font-semibold text-gray-900">
                                            <?php echo esc_html($service->charges); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Middle row: duration & virtual/in-clinic badges -->
                            <div class="mt-3 flex items-center space-x-2">
                                <?php if (!empty($service->duration) && $showServiceDurationStatus): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-600">
                                        <?php echo esc_html($service->duration); ?> min
                                    </span>
                                <?php endif; ?>

                                <?php if ($service->telemed_service === 'yes'): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-50 text-green-600">
                                        Virtual
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-50 text-blue-600">
                                        In-Clinic
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- Optional service description (Pro) -->
                            <?php if (isKiviCareProActive() && !empty($service->description)): ?>
                                <div class="mt-2 text-sm text-gray-500">
                                    <?php echo esc_html($service->description); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Next available date (hard-coded or dynamic) -->
                            <div class="mt-3 text-sm text-gray-500 text-left">
                                <?php echo $next_available_date;?>
                            </div>
                        </label>
                    </div>

                <?php } ?>

            <?php
        }
        ?> </div> <?php
        return ob_get_clean();
    }
}


